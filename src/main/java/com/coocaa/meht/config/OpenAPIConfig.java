package com.coocaa.meht.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.SpecVersion;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @date 2024-09-10 15:39:39
 * <p> openapi配置 </p>
 */
@Configuration
public class OpenAPIConfig {

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI(SpecVersion.V30)
                .info(new Info()
                        .title("楼宇评级系统h5端接口文档")
                        .version("v1")
                        .description("楼宇评级系统h5端接口文档")
                        .license(new License().name("Apache 2.0")));

    }




}
