package com.coocaa.meht.config;

import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.exception.ErrorCode;
import com.coocaa.meht.utils.*;
import com.coocaa.meht.common.exception.CommonException;
import com.google.common.collect.Sets;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/27
 */
@Slf4j
@Component
@Deprecated
public class TokenInterceptor implements HandlerInterceptor {
    private final HashSet<String> white = Sets.newHashSet(
            "/sys/user/login",
            "/sys/user/token/{refreshToken}",
            "/sys/captcha",
            "/sys/phone",
            "/sys/user/login/{mobile}");
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
       //免登录
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method1 = handlerMethod.getMethod();
            Anonymous methodAnnotation = method1.getAnnotation(Anonymous.class);
            if (methodAnnotation != null) {
                return true;
            }
        }

        String method = request.getMethod();
            if (!method.equalsIgnoreCase("OPTIONS")) {
                if (innerCheck(request)) return true;
                String requestUri = request.getRequestURI();
                log.info("日志打印：{}", requestUri);
                boolean enable = white.stream()
                        .anyMatch(pattern -> pathMatcher.match(pattern, requestUri));
                if (!enable) {
                    String token = request.getHeader("token");
                    if (StringUtils.isEmpty(token)) {
//                        throw new CommonException(401, "没有token");
                        ServletUtils.respResult(response, Result.error(ErrorCode.UNAUTHORIZED));
                        return false;
                    }
                    try {
                        parseToken(token);
                    } catch (TokenExpiredException expiredJwtException) {
                        ServletUtils.respResult(response, Result.error(ErrorCode.LOGIN_TIME_OUT));
                        return false;
                    } catch (Exception e) {
                        log.error("解析token出错", e);
                        ServletUtils.respResult(response, Result.error(ErrorCode.UNAUTHORIZED));
                        return false;
                    }
                }

            }


        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserIdUtils.remove();
        UserCodeUtils.remove();
        TokenUtils.remove();
    }
    private static boolean innerCheck(HttpServletRequest request) {
        String inner = request.getHeader("inner");
        if(StringUtils.isNotBlank(inner)){
            String token = request.getHeader("token");
            if(StringUtils.isNotBlank(token)){
                parseToken(token);
            }
            return true;
        }
        return false;
    }
    private static void parseToken(String token) {
        DecodedJWT decodedJWT = JWTUtil.parseToken(token);
        Integer usedId = decodedJWT.getClaim("id").asInt();
        String usedCode = decodedJWT.getClaim("userCode").asString();
        UserIdUtils.setUserId(usedId);
        TokenUtils.setToken(token);
        UserCodeUtils.setUserCode(usedCode);
    }
}
