package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.annotation.RollBack;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.DictCodeVO;
import com.coocaa.meht.common.bean.IReturnCode;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.common.constants.RatingConstants;
import com.coocaa.meht.common.constants.TopicConstants;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.config.AiBaseScoreProperties;
import com.coocaa.meht.config.AuditConfigProperties;
import com.coocaa.meht.config.LargeScreenProperties;
import com.coocaa.meht.converter.CodeNameHelper;
import com.coocaa.meht.kafka.KafkaProducerService;
import com.coocaa.meht.module.api.ark.DouAiService;
import com.coocaa.meht.module.api.map.MapApiService;
import com.coocaa.meht.module.approve.dto.ApprovalDTO;
import com.coocaa.meht.module.approve.enums.ApprovalTypeEnum;
import com.coocaa.meht.module.approve.facade.ApprovalCenterFacade;
import com.coocaa.meht.module.building.entity.CompleteRatingEntity;
import com.coocaa.meht.module.building.entity.CreHousePriceRentEntity;
import com.coocaa.meht.module.building.handler.AiRatingHandler;
import com.coocaa.meht.module.building.service.BuildingScreenService;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.building.service.CreHousePriceRentService;
import com.coocaa.meht.module.building.service.RatingService;
import com.coocaa.meht.module.building.vo.BuildingScreenVO;
import com.coocaa.meht.module.crm.dto.CmsBusinessDto;
import com.coocaa.meht.module.crm.dto.CrmCustomerListDto;
import com.coocaa.meht.module.crm.dto.req.TransferReq;
import com.coocaa.meht.module.crm.service.CrmCustomerService;
import com.coocaa.meht.module.crm.vo.BuildingVO;
import com.coocaa.meht.module.rating.entity.TempRating;
import com.coocaa.meht.module.rating.service.TempRatingService;
import com.coocaa.meht.module.sys.dto.SysUserDto;
import com.coocaa.meht.module.sys.entity.SysConfigEntity;
import com.coocaa.meht.module.sys.entity.SysFileEntity;
import com.coocaa.meht.module.sys.service.SysConfigService;
import com.coocaa.meht.module.sys.service.SysFileService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dao.BuildingDetailsDao;
import com.coocaa.meht.module.web.dao.BuildingRatingDao;
import com.coocaa.meht.module.web.dao.PriceApplyDao;
import com.coocaa.meht.module.web.dto.AddressDto;
import com.coocaa.meht.module.web.dto.BigScreenCalculateDTO;
import com.coocaa.meht.module.web.dto.BuildingFollowerDTO;
import com.coocaa.meht.module.web.dto.BuildingRateDto;
import com.coocaa.meht.module.web.dto.BuildingRatingDTO;
import com.coocaa.meht.module.web.dto.BuildingRatingExportDTO;
import com.coocaa.meht.module.web.dto.BusinessProjectDto;
import com.coocaa.meht.module.web.dto.CityAddressDto;
import com.coocaa.meht.module.web.dto.MapAddressDto;
import com.coocaa.meht.module.web.dto.PriceApplySimpleDto;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.dto.RatingApproveDto;
import com.coocaa.meht.module.web.dto.RatingCalculateDto;
import com.coocaa.meht.module.web.dto.RatingCalculateManualDto;
import com.coocaa.meht.module.web.dto.RatingExportDTO;
import com.coocaa.meht.module.web.dto.VerifyRatingDTO;
import com.coocaa.meht.module.web.dto.convert.BuildingRatingConvert;
import com.coocaa.meht.module.web.dto.req.BuildingPicReq;
import com.coocaa.meht.module.web.dto.tctask.CalculateResultDTO;
import com.coocaa.meht.module.web.entity.BuildingCityRentEntity;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaImgRelationEntity;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingSnapshotEntity;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.CityCoefficientEntity;
import com.coocaa.meht.module.web.entity.CityRentEntity;
import com.coocaa.meht.module.web.entity.PersonnelApprovalEntity;
import com.coocaa.meht.module.web.entity.PointEntity;
import com.coocaa.meht.module.web.entity.PointPlanEntity;
import com.coocaa.meht.module.web.entity.PriceApplyEntity;
import com.coocaa.meht.module.web.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.enums.SceneTypeEnum;
import com.coocaa.meht.module.web.service.BuildingCityRentService;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.service.BuildingParameterService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BuildingSnapshotService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.CityCoefficientService;
import com.coocaa.meht.module.web.service.CityRentService;
import com.coocaa.meht.module.web.service.DataHandlerService;
import com.coocaa.meht.module.web.service.HighSeaCustomerService;
import com.coocaa.meht.module.web.service.IBuildingMetaImgRelationService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.IBuildingStatusChangeLogService;
import com.coocaa.meht.module.web.service.MessageRecordService;
import com.coocaa.meht.module.web.service.PersonnelApprovalService;
import com.coocaa.meht.module.web.service.PointPlanService;
import com.coocaa.meht.module.web.service.PointService;
import com.coocaa.meht.module.web.service.PriceApplyService;
import com.coocaa.meht.module.web.service.ScreenApproveRecordService;
import com.coocaa.meht.module.web.vo.BuildingGeneVO;
import com.coocaa.meht.module.web.vo.BuildingMarginVO;
import com.coocaa.meht.module.web.vo.BuildingRating2WorkOrderVO;
import com.coocaa.meht.module.web.vo.BuildingRatingPicVO;
import com.coocaa.meht.module.web.vo.BuildingStatusVO;
import com.coocaa.meht.module.web.vo.BuildingTrialCalculateVO;
import com.coocaa.meht.module.web.vo.BusinessStatusChangeVO;
import com.coocaa.meht.module.web.vo.ScreenApproveRecordVo;
import com.coocaa.meht.module.workorder.dto.BuildingSelectQuery;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.FeignMehtWebRpc;
import com.coocaa.meht.rpc.dto.BuildingTopEntity;
import com.coocaa.meht.rpc.dto.BuildingTopQueryParam;
import com.coocaa.meht.rpc.dto.CoreAreaJudgeParam;
import com.coocaa.meht.rpc.dto.CreHousePriceRentRequest;
import com.coocaa.meht.rpc.dto.UserDataAccessV2DTO;
import com.coocaa.meht.utils.CodeGenerator;
import com.coocaa.meht.utils.Converts;
import com.coocaa.meht.utils.JsonUtils;
import com.coocaa.meht.utils.LargeScreenCalculator;
import com.coocaa.meht.utils.RsaExample;
import com.coocaa.meht.utils.WordUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.BiPredicate;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BuildingRatingServiceImpl extends ServiceImpl<BuildingRatingDao, BuildingRatingEntity> implements BuildingRatingService {

    @Value("${building.trial.expire.time:300}")
    private Long buildingTrialExpireTime;

    @Value("${parameter.max.dataFlag}")
    private Integer dataFlag;

    @Resource
    private DouAiService douAiService;

    @Resource
    private BuildingDetailsService buildingDetailsService;

    @Resource
    private BuildingParameterService buildingParameterService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private SysFileService sysFileService;


    @Resource
    private CityRentService cityRentService;

    @Resource
    private MessageRecordService messageRecordService;

    @Resource
    private CityCoefficientService cityCoefficientService;

    @Resource
    private MapApiService mapApiService;

    @Resource
    private RsaExample rsaExample;

    @Resource
    private PersonnelApprovalService personnelApprovalService;

    @Resource
    private PriceApplyDao priceApplyDao;

    @Resource
    private IBuildingMetaService buildingMetaService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private IBuildingStatusChangeLogService changeLogService;

    @Autowired
    private BusinessOpportunityService businessOpportunityService;

    @Autowired
    private IBuildingMetaImgRelationService buildingMetaImgRelationService;

    @Resource
    private KafkaProducerService kafkaProducerService;
    @Resource
    private CrmCustomerService crmCustomerService;

    @Resource
    private DataHandlerService dataHandlerService;
    @Autowired
    private PriceApplyService priceApplyService;
    @Autowired
    private PointService pointService;
    @Autowired
    private PointPlanService pointPlanService;
    @Autowired
    private BuildingRatingService buildingRatingService;

    @Autowired
    private FeignCmsRpc feignCmsRpc;

    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    @Autowired
    private FeignMehtWebRpc feignMehtWebRpc;

    @Resource
    private BuildingGeneService buildingGeneService;

    @Autowired
    private LargeScreenCalculator largeScreenCalculator;

    @Autowired
    private LargeScreenProperties largeScreenProperties;

    @Autowired
    private ScreenApproveRecordService screenApproveRecordService;

    @Resource
    private CodeNameHelper codeNameHelper;

    @Resource
    private SysUserService userService;

    @Autowired
    private BuildingSnapshotService buildingSnapshotService;

    @Autowired
    private HighSeaCustomerService highSeaCustomerService;

    @Resource
    private AuditConfigProperties auditConfigProperties;


    @Resource
    private BuildingCityRentService buildingCityRentService;

    @Autowired
    private TempRatingService tempRatingService;

    @Autowired
    private BuildingScreenService buildingScreenService;

    @Autowired
    private CodeGenerator codeGenerator;

    @Autowired
    private RatingService ratingService;

    @Autowired
    private ApprovalCenterFacade approvalCenterFacade;

    @Autowired
    private CompleteRatingService completeRatingService;

    @Autowired
    private AiRatingHandler aiRatingHandler;

    @Autowired
    private AiBaseScoreProperties aiBaseScoreProperties;

    @Autowired
    private CreHousePriceRentService creHousePriceRentService;

    @Autowired
    private BuildingDetailsDao buildingDetailsDao;

    @Autowired
    private BuildingRatingConvert buildingRatingConvert;

    @Resource
    private UserCacheHelper userCacheHelper;

    // 处理AI数据
    public BuildingDetailsEntity processThirdPartyData(BuildingDetailsEntity details, Integer buildingType, String mapAdCode, Integer dataFlag) {
        List<BuildingParameterEntity> rules = buildingParameterService.list(
                Wrappers.<BuildingParameterEntity>lambdaQuery()
                        .eq(BuildingParameterEntity::getDataFlag, dataFlag)
                        .eq(BuildingParameterEntity::getBuildingType, buildingType));

        if (CollectionUtils.isEmpty(rules)) {
            return details;
        }

        Map<String, List<BuildingParameterEntity>> ruleMap = rules.stream()
                .filter(e -> e.getParentId() != 0)
                .collect(Collectors.groupingBy(BuildingParameterEntity::getParameterCode));

        // 处理等级
        processSimpleMatch(details, "buildingGrade", ruleMap,
                (rule, value) -> rule.getParameterRule().equals(value.strip()));

        // 处理地理位置
        processSimpleMatch(details, "buildingLocation", ruleMap,
                (rule, value) -> rule.getParameterRule().equals(value.strip()));

        // 处理楼层数
        processNumberMatch2(details, "buildingNumber", ruleMap);

        // 处理日租金
        processAiDailyRentMatch(details, ruleMap, mapAdCode, buildingType);

        // 处理楼龄
        processNumberMatch2(details, "buildingAge", ruleMap);

        // 处理外立面
        processSimilarityMatch(details, "buildingExterior", ruleMap, 0.2);

        // 处理大堂
        processSimilarityMatch(details, "buildingLobby", ruleMap, 0.2);

        // 处理品牌
        processSimpleMatch(details, "buildingBrand", ruleMap,
                (rule, value) -> rule.getParameterRule().equals(value.strip()));

        // 处理评分
        processNumberMatch2(details, "buildingRating", ruleMap);

        // 处理地下车库
        processSimilarityMatch(details, "buildingGarage", ruleMap, 0.6);

        // 处理候梯厅，AI实际没有获取该参数，补充该项只是为了设置基础分
        processSimilarityMatch(details, "buildingHall", ruleMap, 0.2);

        // 处理入住率，AI实际没有获取该参数，补充该项只是为了设置基础分
        processNumberMatch2(details, "buildingSettled", ruleMap);

        return details;
    }

    private void processSimpleMatch(BuildingDetailsEntity details, String code,
                                    Map<String, List<BuildingParameterEntity>> ruleMap, BiPredicate<BuildingParameterEntity, String> matcher) {

        String value = details.getValue("third" + code);

        if (aiBaseScoreProperties.isInvalid(value)) {
            log.info("AI数据（{}）无效，设置基础分，code：{}", value, code);
            details.setValueId("third" + code + "id", aiBaseScoreProperties.getLatestScore().id());
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        rules.stream()
                .filter(rule -> matcher.test(rule, value))
                .findFirst()
                .ifPresent(rule -> details.setValueId("third" + code + "id", rule.getId()));
    }

    private void processSimilarityMatch(BuildingDetailsEntity details, String code,
                                        Map<String, List<BuildingParameterEntity>> ruleMap, double threshold) {

        String value = details.getValue("third" + code);

        if (aiBaseScoreProperties.isInvalid(value)) {
            log.info("AI数据（{}）无效，设置基础分，code：{}", value, code);
            details.setValueId("third" + code + "id", aiBaseScoreProperties.getLatestScore().id());
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        Optional<BuildingParameterEntity> matched = rules.stream()
                .filter(rule -> WordUtils.calculateCosineSimilarity(rule.getParameterRule(), value.strip()) > threshold)
                .findFirst();

        if (matched.isPresent()) {
            details.setValueId("third" + code + "id", matched.get().getId());
        } else {
            rules.stream()
                    .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(7)) == 0)
                    .findFirst()
                    .ifPresent(rule -> details.setValueId("third" + code + "id", rule.getId()));
        }
    }

    @Override
    public Map<String, Object> getByRating(String name, String mapNo) {
        List<BuildingRatingEntity> ratingList = lambdaQuery()
                .select(BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getStatus,
                        BuildingRatingEntity::getBuildingStatus,
                        BuildingRatingEntity::getRatingVersion,
                        BuildingRatingEntity::getSubmitUser)
                .eq(BuildingRatingEntity::getMapNo, mapNo)
                .orderByDesc(BuildingRatingEntity::getCreateTime)
                .list();

        BuildingRatingEntity ratingEntity = getHighestPriorityRating(ratingList);
        if (Objects.nonNull(ratingEntity)) {
            log.info("楼宇申请getByRating：{}", JsonUtils.toJson(ratingEntity));
            // 返回详情
            return JsonUtils.fromMap(JsonUtils.toJson(
                    ratingService.info(ratingEntity.getBuildingNo(), RatingConstants.TYPE_RATING, ratingEntity.getRatingVersion())));
        }

        return Collections.emptyMap();
    }

    /**
     * 获取最高优先级数据，优先返回认证中或已认证的数据，否则返回当前登陆人的草稿数据
     *
     * @param ratingEntities
     * @return
     */
    private BuildingRatingEntity getHighestPriorityRating(List<BuildingRatingEntity> ratingEntities) {
        if (CollUtil.isEmpty(ratingEntities)) {
            return null;
        }

        for (BuildingRatingEntity ratingEntity : ratingEntities) {
            // 有认证中的数据直接返回
            if (BuildingRatingEntity.Status.WAIT_AUDIT.getValue() == ratingEntity.getStatus()
                    || BuildingRatingEntity.Status.AUDITED.getValue() == ratingEntity.getStatus()) {
                return ratingEntity;
            }
        }

        // 有草稿数据，返回当前登录人创建的的草稿数据
        return ratingEntities.stream()
                .filter(ratingEntity -> UserThreadLocal.getUser().getWno().equals(ratingEntity.getSubmitUser()))
                .filter(ratingEntity -> BuildingRatingEntity.Status.DRAFT.getValue() == ratingEntity.getStatus())
                .findFirst()
                .orElse(null);
    }

    @Deprecated
    @Override
    public Map<String, Object> info(String buildingNo) {
        BuildingRatingEntity entity = this.getOne(Wrappers.<BuildingRatingEntity>lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo).last("limit 1"));
        if (Objects.isNull(entity)) {
            throw new ServerException("该楼宇编号不存在");
        }
        BuildingGeneVO buildingGene = buildingGeneService.getBuildingGeneByNo(buildingNo);

        try {
            entity.setMapAddress(rsaExample.decryptByPrivate(entity.getMapAddress()));
            entity.setMapLatitude(rsaExample.decryptByPrivate(entity.getMapLatitude()));
            entity.setMapLongitude(rsaExample.decryptByPrivate(entity.getMapLongitude()));
        } catch (Exception ignored) {

        }
        Map<String, Object> result = JsonUtils.fromMap(JsonUtils.toJson(entity));
        result.put("buildingRatingId", entity.getId());
        // result.put("largeScreen", buildingGeneService.isScreen(buildingGene));
        // 外墙材料附件地址
        fillPic(entity.getBuildingExteriorPic(), result, "buildingExteriorPic");
        // 楼盘大堂附件地址
        fillPic(entity.getBuildingLobbyPic(), result, "buildingLobbyPic");
        // 侯梯厅附件地址
        fillPic(entity.getBuildingHallPic(), result, "buildingHallPic");
        // 大堂环境图附件地址
        fillPic(entity.getBuildingLobbyEnvPic(), result, "buildingLobbyEnvPic");
        // 梯厅环境图附件地址
        fillPic(entity.getBuildingElevatorPic(), result, "buildingElevatorPic");
        // 闸口图附件地址
        fillPic(entity.getBuildingGatePic(), result, "buildingGatePic");
        // 安装示意图附件地址
        fillPic(entity.getBuildingInstallationPic(), result, "buildingInstallationPic");


        BuildingDetailsEntity detailsEntity = buildingDetailsService.getOne(Wrappers.<BuildingDetailsEntity>lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, buildingNo).last("limit 1"));
        if (Objects.nonNull(detailsEntity)) {
            result.putAll(JsonUtils.fromMap(JsonUtils.toJson(detailsEntity)));
            result.put("buildingBrandIdName", detailsEntity.getTopBrandName());
            List<Long> ids = List.of(detailsEntity.getBuildingGrade(), detailsEntity.getBuildingLocation()
                    , detailsEntity.getBuildingNumber(), detailsEntity.getBuildingPrice()
                    , detailsEntity.getBuildingAge(), detailsEntity.getBuildingLobby()
                    , detailsEntity.getBuildingGarage(), detailsEntity.getBuildingExterior()
                    , detailsEntity.getBuildingSettled(), detailsEntity.getBuildingHall()
                    , detailsEntity.getBuildingBrand(), detailsEntity.getBuildingRating());
            Map<String, BuildingParameterEntity> mapParameter = buildingParameterService.getByMapIds(ids);
            BuildingParameterEntity buildingGrade = mapParameter.get("buildingGrade");
            if (Objects.nonNull(buildingGrade)) {
                result.put("buildingGradeName", buildingGrade.getParameterName());
            }
            BuildingParameterEntity buildingLocation = mapParameter.get("buildingLocation");
            if (Objects.nonNull(buildingLocation)) {
                result.put("buildingLocationName", buildingLocation.getParameterName());
            }
            BuildingParameterEntity buildingNumber = mapParameter.get("buildingNumber");
            if (Objects.nonNull(buildingNumber)) {
                result.put("buildingNumberName", buildingNumber.getParameterName());
            }
            BuildingParameterEntity buildingPrice = mapParameter.get("buildingPrice");
            if (Objects.nonNull(buildingPrice)) {
                result.put("buildingPriceName", buildingPrice.getParameterName());
            }
            BuildingParameterEntity buildingAge = mapParameter.get("buildingAge");
            if (Objects.nonNull(buildingAge)) {
                result.put("buildingAgeName", buildingAge.getParameterName());
            }
            BuildingParameterEntity buildingLobby = mapParameter.get("buildingLobby");
            if (Objects.nonNull(buildingLobby)) {
                result.put("buildingLobbyName", buildingLobby.getParameterName());
            }
            BuildingParameterEntity buildingGarage = mapParameter.get("buildingGarage");
            if (Objects.nonNull(buildingGarage)) {
                result.put("buildingGarageName", buildingGarage.getParameterName());
            }
            BuildingParameterEntity buildingExterior = mapParameter.get("buildingExterior");
            if (Objects.nonNull(buildingExterior)) {
                result.put("buildingExteriorName", buildingExterior.getParameterName());
            }
            BuildingParameterEntity buildingSettled = mapParameter.get("buildingSettled");
            if (Objects.nonNull(buildingSettled)) {
                result.put("buildingSettledName", buildingSettled.getParameterName());
            }
            BuildingParameterEntity buildingHall = mapParameter.get("buildingHall");
            if (Objects.nonNull(buildingHall)) {
                result.put("buildingHallName", buildingHall.getParameterName());
            }
            BuildingParameterEntity buildingBrand = mapParameter.get("buildingBrand");
            if (Objects.nonNull(buildingBrand)) {
                result.put("buildingBrandName", buildingBrand.getParameterName());
            }
            BuildingParameterEntity buildingRating = mapParameter.get("buildingRating");
            if (Objects.nonNull(buildingRating)) {
                result.put("buildingRatingName", buildingRating.getParameterName());
            }
            result.put("buildingTypeName", BuildingRatingEntity.BuildingType.getNameByValue(entity.getBuildingType()));

        }
        Map<String, SysUserDto> nameMaps = sysUserService.getNameMaps(Arrays.asList(entity.getApproveUser(), entity.getSubmitUser(),
                entity.getRejectUser(), entity.getCreateBy()));
        SysUserDto approveUser = nameMaps.get(entity.getApproveUser());
        if (Objects.nonNull(approveUser)) {
            result.put("approveUserName", approveUser.getRealName());
            result.put("approveUserType", approveUser.getUserType());
            result.put("approveBelongCode", approveUser.getBelongCode());
            result.put("approveBelongName", approveUser.getBelongName());
        }
        SysUserDto submitUser = nameMaps.get(entity.getSubmitUser());
        if (Objects.nonNull(submitUser)) {
            result.put("submitUserName", submitUser.getRealName());
            result.put("submitUserType", submitUser.getUserType());
            result.put("submitBelongCode", submitUser.getBelongCode());
            result.put("submitBelongName", submitUser.getBelongName());
        }
        SysUserDto rejectUser = nameMaps.get(entity.getRejectUser());
        if (Objects.nonNull(rejectUser)) {
            result.put("rejectUserName", rejectUser.getRealName());
            result.put("rejectUserType", rejectUser.getUserType());
            result.put("rejectBelongCode", rejectUser.getBelongCode());
            result.put("rejectBelongName", rejectUser.getBelongName());
        }
        SysUserDto createUser = nameMaps.get(entity.getCreateBy());
        if (Objects.nonNull(createUser)) {
            result.put("createUserCode", createUser.getEmpCode());
            result.put("createUserName", createUser.getRealName());
            result.put("createUserType", createUser.getUserType());
            result.put("createBelongCode", createUser.getBelongCode());
            result.put("createBelongName", createUser.getBelongName());
        }
        CityCoefficientEntity coefficient = cityCoefficientService.getCoefficient(entity.getMapAdCode());
        if (Objects.nonNull(coefficient)) {
            result.put("coefficientValue", coefficient.getCoefficient());
        } else {
            result.put("coefficientValue", BigDecimal.ONE);
        }
        String outTime = sysConfigService.getVal("out_time", "3");
        result.put("outTime", outTime);
        // 审批人
        String approveUsers = sysConfigService.getVal("approve_user", "CC1967");
        result.put("approveUsers", approveUsers);

        if (Objects.nonNull(buildingGene)) {
            /*if (StrUtil.isNotBlank(buildingGene.getSpec())) {
                List<String> dicCodes = JSON.parseArray(buildingGene.getSpec(), String.class);
                List<CodeNameVO> codeNameVOS = codeNameHelper.getDictMapping(dicCodes).entrySet().stream()
                        .map(entry -> new CodeNameVO(null, entry.getKey(), entry.getValue(), null, null))
                        .toList();
                buildingGene.setSpec(JSON.toJSONString(codeNameVOS));
            }*/
            // 目标点位数，页面回显应该使用rating表字段值，基因表字段与其重名，前端会用基因表字段值覆盖rating表，导致显示的是基因表的值
            buildingGene.setTargetPointCount(entity.getTargetPointCount());

            result.put("buildingGene", buildingGene);
        }
        List<ScreenApproveRecordEntity> screenApproveRecordEntities = screenApproveRecordService.lambdaQuery()
                .eq(ScreenApproveRecordEntity::getNaturalKey, buildingNo)
                .eq(ScreenApproveRecordEntity::getSceneType, SceneTypeEnum.BUILDING.getType())
                .eq(ScreenApproveRecordEntity::getDeleteFlag, BooleFlagEnum.NO.getCode()).list();
        if (CollUtil.isNotEmpty(screenApproveRecordEntities)) {
            Map<String, SysUserDto> userMap = userService.getNameMaps(screenApproveRecordEntities.stream()
                    .map(ScreenApproveRecordEntity::getApproveUser).filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList()));
            List<ScreenApproveRecordVo> screenApproveRecordVos = screenApproveRecordEntities.stream()
                    .map(old -> {
                        ScreenApproveRecordVo vo = BeanUtil.copyProperties(old, ScreenApproveRecordVo.class);
                        vo.setStatusName(ScreenApproveRecordVo.BuildingStatusResultEnum.getNameByValue(old.getStatus()));
                        vo.setApproveUserName(Optional.ofNullable(userMap.get(old.getApproveUser()))
                                .map(SysUserDto::getRealName).orElse(""));
                        return vo;
                    }).toList();

            result.put("screenApproveRecord", screenApproveRecordVos.stream()
                    .sorted(Comparator.comparing(ScreenApproveRecordVo::getCreateTime)
                            .thenComparing(ScreenApproveRecordVo::getApproveLevel).reversed()).toList());
        } else {
            Map<String, SysUserDto> userMap = userService.getNameMaps(screenApproveRecordEntities.stream()
                    .map(ScreenApproveRecordEntity::getApproveUser).filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList()));
            List<ScreenApproveRecordVo> screenApproveRecordVos = new ArrayList<>(2);
            ScreenApproveRecordVo screenApproveRecordVo = new ScreenApproveRecordVo();
            screenApproveRecordVo.setApproveLevel(0);
            screenApproveRecordVo.setStatus(BuildingRatingEntity.Status.WAIT_AUDIT.getValue());
            screenApproveRecordVo.setStatusName(BuildingRatingEntity.Status.getNameByValue(BuildingRatingEntity.Status.WAIT_AUDIT.getValue()));
            screenApproveRecordVo.setApproveUser(entity.getSubmitUser());
            screenApproveRecordVo.setApproveUserName(Optional.ofNullable(userMap.get(entity.getSubmitUser()))
                    .map(SysUserDto::getRealName).orElse(""));
            screenApproveRecordVo.setApproveTime(entity.getSubmitTime());
            screenApproveRecordVo.setCreateTime(entity.getCreateTime());
            screenApproveRecordVo.setOperateType(1);
            screenApproveRecordVos.add(screenApproveRecordVo);

            ScreenApproveRecordVo newScreenApproveRecordVo = new ScreenApproveRecordVo();
            newScreenApproveRecordVo.setApproveLevel(1);
            newScreenApproveRecordVo.setStatus(entity.getStatus());
            newScreenApproveRecordVo.setStatusName(BuildingRatingEntity.Status.getNameByValue(entity.getStatus()));
            newScreenApproveRecordVo.setApproveTime(entity.getStatus()
                    .equals(BuildingRatingEntity.Status.REJECTED.getValue()) ? entity.getRejectTime() : entity.getApproveTime());
            newScreenApproveRecordVo.setApproveUser(entity.getApproveUser());
            newScreenApproveRecordVo.setApproveUserName(Optional.ofNullable(userMap.get(entity.getApproveUser()))
                    .map(SysUserDto::getRealName).orElse(""));
            newScreenApproveRecordVo.setRemark(entity.getStatus()
                    .equals(BuildingRatingEntity.Status.REJECTED.getValue()) ? entity.getRejectDesc() : entity.getApproveDesc());
            newScreenApproveRecordVo.setCreateTime(entity.getSubmitTime());
            screenApproveRecordVo.setOperateType(1);
            screenApproveRecordVos.add(newScreenApproveRecordVo);
            result.put("screenApproveRecord", screenApproveRecordVos.stream()
                    .sorted(Comparator.comparing(ScreenApproveRecordVo::getCreateTime)
                            .thenComparing(ScreenApproveRecordVo::getApproveLevel).reversed()).toList());
        }
        result.put("mapUrl", getH5MapUrl(entity));

        // 地图地址
        // MapAddressDto mapAddressDto = mapApiService.getConvertAddress(entity.getMapLongitude(), entity.getMapLatitude());
        // String mapConvertAddress = mapAddressDto.getX() + "," + mapAddressDto.getY();
        // result.put("mapConvertAddress", "https://map.baidu.com/search/" + entity.getBuildingName() + "/@" + mapConvertAddress + ",19z?querytype=s&wd=" + entity.getBuildingName() + "");
        return result;
    }

    private void fillPic(String fileIds, Map<String, Object> result, String key) {
        if (StringUtils.isNotBlank(fileIds)) {
            String[] split = StringUtils.split(fileIds, ",");
            List<SysFileEntity> list = sysFileService.list(Wrappers.<SysFileEntity>lambdaQuery()
                    .in(SysFileEntity::getId, Arrays.asList(split)));
            if (CollectionUtils.isNotEmpty(list)) {
                List<Map<String, Object>> attachmentAddressMaps = new ArrayList<>();
                list.forEach(ele -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", ele.getId());
                    map.put("name", ele.getName());
                    map.put("url", ele.getUrl());
                    map.put("size", ele.getSize());
                    map.put("attachmentType", ele.getAttachmentType());
                    attachmentAddressMaps.add(map);
                });
                result.put(key, attachmentAddressMaps);
            } else {
                result.put(key, List.of());
            }
        } else {
            result.put(key, List.of());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BuildingRatingEntity apply(RatingApplyDto param) {
        log.info("楼宇评级入参mapNo:{},工号：{}, param:{}", param.getMapNo(), UserThreadLocal.getUser()
                .getWno(), JSON.toJSONString(param));

        // 检查是否有已认证或认证中数据
        boolean hasPendingData = lambdaQuery()
                .eq(BuildingRatingEntity::getMapNo, param.getMapNo())
                .in(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue(),
                        BuildingRatingEntity.Status.AUDITED.getValue())
                .exists();
        if (hasPendingData) {
            throw new ServerException("当前楼宇已有认证数据，请勿重复提交");
        }

        // 检查用户可用客户数
        Long availableUserCount = RpcUtils.unBox(feignMehtWebRpc.getAvailableUserCount(UserThreadLocal.getUser()
                .getWno()));
        if (availableUserCount == 0) {
            throw new ServerException("当前登录人无可用客户数，无法提交评级申请");
        }

        // 大屏标识
        boolean isLargeScreen = isLargeScreen(param);

        // 非写字楼大屏检查
        if (BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue() != param.getBuildingType()
                && isLargeScreen) {
            throw new ServerException("非写字楼，不能选择大屏");
        }

        // 创建楼宇详情
        BuildingDetailsEntity detailsEntity = buildingDetailsService.createDetailsEntity(param);

        // 人工评级，填充评级数据到detailsEntity
        CalculateResultDTO manualRatingResult = manualRating(param, detailsEntity);
        // 人工评级达标判断
        if (RatingConstants.RATING_LEVEL_BELOW_A.equals(manualRatingResult.getProjectLevel())) {
            throw new ServerException("当前楼宇评级未达到A级，无法提交认证申请");
        }

        // 审核不通过重提需要保存快照
        saveSnapshot(param);

        // 创建楼宇评级
        BuildingRatingEntity ratingEntity = createRatingEntity(param, true);
        // 设置大小屏评级标识
        ratingEntity.setSmallScreenRatingFlag(BooleFlagEnum.YES.getCode());
        ratingEntity.setLargeScreenRatingFlag(isLargeScreen ? BooleFlagEnum.YES.getCode() : BooleFlagEnum.NO.getCode());
        // 填充评级数据
        fillRatingData(ratingEntity, manualRatingResult);
        // 保存或更新楼宇评级
        saveOrUpdateRating(ratingEntity, param);
        // 保存楼宇状态修改记录
        addBuildingChangeLog(ratingEntity.getId(), ratingEntity.getBuildingNo(), ratingEntity.getStatus());

        // 保存或更新楼宇详情
        detailsEntity.setBuildingNo(ratingEntity.getBuildingNo());
        saveOrUpdateDetail(detailsEntity);

        // 大屏提交系数
        String submitCoefficient = calculateSubmitCoefficient(param, isLargeScreen);

        // 保存或更新BuildingScreen对象
        buildingScreenService.saveOrUpdate(ratingEntity.getBuildingNo(), submitCoefficient, param);

        // 保存或更新楼宇基因数据
        buildingGeneService.saveOrUpdate(ratingEntity.getBuildingNo(), param);

        // 保存或更新楼宇元数据
        buildingMetaService.saveOrUpdate(ratingEntity, isLargeScreen, true);

        // 清空草稿
        clearDraft(ratingEntity.getBuildingNo());

        // 提交AI评级处理
        aiRatingHandler.aiRatingAfterCommit(AiRatingHandler.Type.BUILDING_RATING, ratingEntity.getBuildingNo(),
                UserThreadLocal.getUser().getWno());

        return ratingEntity;
    }

    private void saveSnapshot(RatingApplyDto param) {
        if (Objects.isNull(param.getBuildingNo())) {
            return;
        }

        BuildingRatingEntity ratingEntity = lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, param.getBuildingNo())
                .one();
        if (Objects.nonNull(ratingEntity) && ratingEntity.getStatus() == BuildingRatingEntity.Status.FAILED_AUDIT.getValue()) {
            buildingSnapshotService.save(param.getBuildingNo(), BuildingSnapshotEntity.Type.FAILED_AUDIT);
        }
    }

    @Override
    public void submitApproval(String buildingNo, String operatorWno) {
        BuildingRatingEntity ratingEntity = lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .last("limit 1")
                .one();
        if (Objects.isNull(ratingEntity)) {
            log.warn("楼宇评级不存在: {}", buildingNo);
            return;
        }

        submitApproval(ratingEntity, operatorWno);
    }

    private void submitApproval(BuildingRatingEntity ratingEntity, String operatorWno) {
        ApprovalDTO approvalDTO = new ApprovalDTO();
        approvalDTO.setBusinessKey(ratingEntity.getBuildingNo());
        approvalDTO.setVersion(ratingEntity.getRatingVersion());
        approvalDTO.setApprovalType(ApprovalTypeEnum.BUILDING_APPROVAL.getCode());
        approvalDTO.setSubmitter(operatorWno);
        approvalCenterFacade.submitApproval(approvalDTO);
    }

    private void clearDraft(String buildingNo) {
        if (StrUtil.isBlank(buildingNo)) {
            return;
        }
        LambdaUpdateWrapper<BuildingRatingEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(BuildingRatingEntity::getDraft, null);
        updateWrapper.eq(BuildingRatingEntity::getBuildingNo, buildingNo);
        update(updateWrapper);
    }

    private String calculateSubmitCoefficient(RatingApplyDto param, boolean isLargeScreen) {
        if (!isLargeScreen) {
            return null;
        }
        BigScreenCalculateDTO bigScreenCalculateDTO = new BigScreenCalculateDTO();
        bigScreenCalculateDTO.setBuildingCeilingHeight(param.getBuildingCeilingHeight());
        bigScreenCalculateDTO.setBuildingNumberInput(param.getBuildingNumberInput());
        bigScreenCalculateDTO.setBuildingSpacing(param.getBuildingSpacing());
        bigScreenCalculateDTO.setBuildingAgeInput(param.getBuildingAgeInput());
        bigScreenCalculateDTO.setBuildingLocationText(param.getLocationName());
        return largeScreenCalculator.calculate(bigScreenCalculateDTO);
    }

    private void saveOrUpdateDetail(BuildingDetailsEntity detailsEntity) {
        BuildingDetailsEntity existEntity = buildingDetailsService.lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, detailsEntity.getBuildingNo())
                .last("limit 1")
                .one();

        if (Objects.isNull(existEntity)) {
            buildingDetailsService.save(detailsEntity);
            return;
        }

        detailsEntity.setId(existEntity.getId());
        buildingDetailsService.updateById(detailsEntity);
        if (Objects.nonNull(existEntity.getDeliveryDate()) && Objects.isNull(detailsEntity.getDeliveryDate())) {
            // 交付日期置空处理
            LambdaUpdateWrapper<BuildingDetailsEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BuildingDetailsEntity::getDeliveryDate, null);
            updateWrapper.eq(BuildingDetailsEntity::getId, existEntity.getId());
            buildingDetailsService.update(updateWrapper);
        }
    }

    private void fillRatingData(BuildingRatingEntity ratingEntity, CalculateResultDTO manualRatingResult) {
        ratingEntity.setBuildingScore(manualRatingResult.getBuildingScore())
                .setProjectLevel(manualRatingResult.getProjectLevel())
                .setFirstFloorExclusive(manualRatingResult.getFirstFloorExclusiveScore())
                .setFirstFloorShare(manualRatingResult.getFirstFloorShareScore())
                .setNegativeFirstFloor(manualRatingResult.getNegativeFirstFloorScore())
                .setNegativeTwoFloor(manualRatingResult.getNegativeTwoFloorScore())
                .setTwoFloorAbove(manualRatingResult.getTwoFloorAboveScore())
                .setThirdFloorBelow(manualRatingResult.getThirdFloorBelowScore());
    }

    @Override
    public BuildingRatingEntity createRatingEntity(RatingApplyDto dto, boolean isSubmit) {
        BuildingRatingEntity ratingEntity = new BuildingRatingEntity()
                .setBuildingName(dto.getBuildingName())
                .setBuildingType(dto.getBuildingType())
                .setBuildingScore(dto.getBuildingScore())
                .setBuildingDesc(dto.getBuildingDesc())
                .setTargetPointCount(dto.getTargetPointCount())
                .setMapNo(dto.getMapNo())
                .setMapAddress(rsaExample.encryptByPublic(dto.getMapAddress()))
                .setMapCity(dto.getMapCity())
                .setMapProvince(dto.getMapProvince())
                .setMapRegion(dto.getMapRegion())
                .setMapLongitude(rsaExample.encryptByPublic(dto.getMapLongitude()))
                .setMapLatitude(rsaExample.encryptByPublic(dto.getMapLatitude()))
                .setMapAdCode(dto.getMapAdCode())
                .setAuthenticationStart(LocalDateTime.now())
                .setAuthenticationEnd(LocalDateTime.now().plusDays(365))
                .setAuthenticationPeriod(365L)
                .setDataFlag(dataFlag)
                .setHighSeaFlag(BooleFlagEnum.NO.getCode())
                // 设置评级版本号
                .setRatingVersion(codeGenerator.generateRatingVersion())
                .setSubmitUser(UserThreadLocal.getUser().getWno());

        if (isSubmit) {
            // 提交
            ratingEntity
                    .setBuildingStatus(BuildingRatingEntity.BuildingStatus.CONFIRMING.value)
                    .setStatus(BuildingRatingEntity.Status.WAIT_AUDIT.value)
                    .setSubmitTime(LocalDateTime.now());
        } else {
            // 草稿
            ratingEntity
                    .setBuildingStatus(BuildingRatingEntity.BuildingStatus.UN_CONFIRM.value)
                    .setStatus(BuildingRatingEntity.Status.DRAFT.value);
        }

        // 设置城市和区域信息
        processRegionInfo(ratingEntity, dto);

        // 设置图片
        processPictures(dto, ratingEntity);

        // 设置top楼宇
        ratingEntity.setTopLevel(applyTopLevel(ratingEntity));

        return ratingEntity;
    }

    private void processPictures(RatingApplyDto dto, BuildingRatingEntity newEntity) {
        // 先清空图片
        newEntity.setBuildingExteriorPic("")
                .setBuildingLobbyPic("")
                .setBuildingHallPic("")
                .setBuildingLobbyEnvPic("")
                .setBuildingElevatorPic("")
                .setBuildingGatePic("")
                .setBuildingInstallationPic("");

        // 定义要处理的图片字段映射
        Map<List<String>, Consumer<String>> pictureFieldsMap = new HashMap<>(7);
        pictureFieldsMap.put(dto.getBuildingExteriorPics(), newEntity::setBuildingExteriorPic);
        pictureFieldsMap.put(dto.getBuildingLobbyPics(), newEntity::setBuildingLobbyPic);
        pictureFieldsMap.put(dto.getBuildingHallPics(), newEntity::setBuildingHallPic);
        pictureFieldsMap.put(dto.getBuildingLobbyEnvPics(), newEntity::setBuildingLobbyEnvPic);
        pictureFieldsMap.put(dto.getBuildingElevatorPics(), newEntity::setBuildingElevatorPic);
        pictureFieldsMap.put(dto.getBuildingGatePics(), newEntity::setBuildingGatePic);
        pictureFieldsMap.put(dto.getBuildingInstallationPics(), newEntity::setBuildingInstallationPic);

        // 批量处理所有图片字段
        pictureFieldsMap.forEach((pictureUrls, setter) -> {
            if (CollectionUtils.isNotEmpty(pictureUrls)) {
                // 从数据库一次查询附件ID
                List<SysFileEntity> sysFileEntities = sysFileService.list(Wrappers.<SysFileEntity>lambdaQuery()
                        .in(SysFileEntity::getUrl, pictureUrls));

                // 提取ID并设置到实体中
                if (CollectionUtils.isNotEmpty(sysFileEntities)) {
                    String ids = sysFileEntities.stream()
                            .map(SysFileEntity::getId)
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    setter.accept(ids);
                }
            }
        });
    }

    private boolean isLargeScreen(RatingApplyDto dto) {
        if (dto.getBuildingType().equals(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue())
                && StringUtils.isNotBlank(dto.getSpec())) {
            List<String> codeList = JSON.parseArray(dto.getSpec(), String.class);
            List<String> largeDeviceKey = largeScreenProperties.getLargeDictKey();

            Collection<String> intersection = CollectionUtil.intersection(largeDeviceKey, codeList);
            return CollectionUtil.isNotEmpty(intersection);
        }
        return false;
    }

    private void processRegionInfo(BuildingRatingEntity entity, RatingApplyDto dto) {
        if (StringUtils.isNotBlank(dto.getMapRegion()) && StringUtils.isBlank(dto.getMapCity())) {
            entity.setMapCity(dto.getMapRegion());
            entity.setMapRegion(dto.getMapRegion() + "区");
        }
        if (StringUtils.isBlank(dto.getMapRegion()) && StringUtils.isNotBlank(dto.getMapCity())) {
            entity.setMapRegion(dto.getMapCity() + "区");
        }
    }

    private void saveOrUpdateRating(BuildingRatingEntity newEntity, RatingApplyDto param) {
        if (StringUtils.isNotBlank(param.getBuildingNo())) {
            newEntity.setBuildingNo(param.getBuildingNo());
            updateRating(newEntity);
        } else {
            newEntity.setBuildingNo(codeGenerator.generateRatingCode());
            save(newEntity);
        }
    }


    public void processBusiness(BuildingRatingEntity entity) {

        // 审核不通过数据重编辑提交，查询是否已建商机
        List<BusinessOpportunityEntity> businessEntities = businessOpportunityService.lambdaQuery()
                .select(BusinessOpportunityEntity::getId,
                        BusinessOpportunityEntity::getCode)
                .eq(BusinessOpportunityEntity::getBuildingNo, entity.getBuildingNo())
                .list();

        if (CollUtil.isEmpty(businessEntities)) {
            // 没用创建商机，创建默认商机
            createDefaultBusiness(entity);
            return;
        }

        // 恢复商机
        businessOpportunityService.recover(businessEntities);
    }

    private void syncToGene(RatingApplyDto dto, String buildingNo) {
        BuildingGeneEntity buildingGene = buildingGeneService.lambdaQuery()
                .eq(BuildingGeneEntity::getBuildingRatingNo, buildingNo)
                .one();
        boolean exist = buildingGene != null;
        buildingGene = ObjectUtils.defaultIfNull(buildingGene, new BuildingGeneEntity());
        fillGeneFiled(dto, buildingGene);
        if (!exist) {
            buildingGene.setBuildingRatingNo(buildingNo);
            buildingGeneService.save(buildingGene);
            return;
        }
        buildingGeneService.updateById(buildingGene);
    }

    private void fillGeneFiled(RatingApplyDto dto, BuildingGeneEntity buildingGene) {
        // 目标点位数 - 添加空指针检查
        if (dto.getTargetPointCount() != null) {
            buildingGene.setTargetPointCount(dto.getTargetPointCount());
        }


        if (dto.getBuildingAge() != null) {
            buildingGene.setBuildingAge(dto.getBuildingAge().intValue());
        }

        // 楼宇数量转换
        if (StringUtils.isNotBlank(dto.getBuildingNumberInput())) {
            try {
                buildingGene.setMaxFloorCount(Integer.valueOf(dto.getBuildingNumberInput().trim()));
            } catch (NumberFormatException e) {
                log.error("楼宇数量转换异常, input: {}", dto.getBuildingNumberInput(), e);
            }
        }

        // 楼龄转换
        if (StringUtils.isNotBlank(dto.getBuildingAgeInput())) {
            try {
                buildingGene.setBuildingAge(Integer.valueOf(dto.getBuildingAgeInput().trim()));
            } catch (NumberFormatException e) {
                log.error("楼龄转换异常, input: {}", dto.getBuildingAgeInput(), e);
            }
        }
    }

    public String applyTopLevel(BuildingRatingEntity buildingRating) {
        if (Objects.isNull(buildingRating.getBuildingType())) {
            return "";
        }

        BuildingTopQueryParam buildingTopQueryParam = new BuildingTopQueryParam();
        buildingTopQueryParam.setBuildingName(buildingRating.getBuildingName());
        buildingTopQueryParam.setProvince(buildingRating.getMapProvince());
        buildingTopQueryParam.setCity(buildingRating.getMapCity());
        String buildingTypeName = switch (buildingRating.getBuildingType()) {
            case 0 -> "写字楼";
            case 1 -> "商住楼";
            case 2 -> "综合体";
            case 3 -> "产业园区";
            default -> "";
        };
        buildingTopQueryParam.setBuildingType(buildingTypeName);
        log.info("topLevel入参:{}", JsonUtils.toJson(buildingTopQueryParam));
        ResultTemplate<BuildingTopEntity> unique = feignMehtWebRpc.getUnique(buildingTopQueryParam);
        log.info("topLevel返回结果:{}", JsonUtils.toJson(unique));
        if (Objects.nonNull(unique.getData())) {
            return unique.getData().getTopLevel();
        }
        return "";
    }


    /**
     * 驳回并重新提交RatingEntity
     */
    public void updateRating(BuildingRatingEntity newEntity) {
        BuildingRatingEntity buildingRating = this.getOne(Wrappers.<BuildingRatingEntity>lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, newEntity.getBuildingNo()));
        if (Objects.isNull(buildingRating)) {
            throw new ServerException("未找到楼宇");
        }

        newEntity.setId(buildingRating.getId());
        updateById(newEntity);
    }

    public void addBuildingChangeLog(Long buildingId, String buildingNo, Integer status) {
        BuildingStatusChangeLogEntity changeLogEntity = new BuildingStatusChangeLogEntity();
        changeLogEntity.setBizId(buildingId);
        changeLogEntity.setBizCode(buildingNo);
        changeLogEntity.setType(BuildingStatusChangeLogEntity.BizType.RATING.getCode());
        changeLogEntity.setChangeTime(LocalDateTime.now());

        CachedUser user = UserThreadLocal.getUser();
        changeLogEntity.setOperatorWno(user.getWno());
        changeLogEntity.setOperatorName(user.getName());
        changeLogEntity.setOperator(Optional.ofNullable(user.getId()).map(Long::valueOf).orElse(null));

        // 状态
        String statusCode = BuildingStatusChangeLogEntity.RatingApplicationStatus.WAIT_APPROVED.getCode();
        switch (status) {
            case 1:
                statusCode = BuildingStatusChangeLogEntity.RatingApplicationStatus.APPROVED.getCode();
                break;
            case 2:
                statusCode = BuildingStatusChangeLogEntity.RatingApplicationStatus.REJECTED.getCode();
                break;
            case 3:
                statusCode = BuildingStatusChangeLogEntity.RatingApplicationStatus.NOT_APPROVED.getCode();
            default:
                break;
        }
        changeLogEntity.setStatus(statusCode);
        changeLogService.save(changeLogEntity);
    }


    public BigDecimal calculateScore(BuildingDetailsEntity details, String adCode) {
        List<Long> ids = new ArrayList<>();
        ids.add(details.getBuildingGrade());
        ids.add(details.getBuildingLocation());
        ids.add(details.getBuildingNumber());
        ids.add(details.getBuildingPrice());
        ids.add(details.getBuildingAge());
        ids.add(details.getBuildingLobby());
        ids.add(details.getBuildingGarage());
        ids.add(details.getBuildingExterior());
        ids.add(details.getBuildingSettled());
        ids.add(details.getBuildingHall());
        ids.add(details.getBuildingBrand());
        ids.add(details.getBuildingRating());
        List<BuildingParameterEntity> list = buildingParameterService.list(Wrappers.<BuildingParameterEntity>lambdaQuery()
                .eq(BuildingParameterEntity::getDataFlag, dataFlag)
                .in(BuildingParameterEntity::getId, ids));
        BigDecimal totalScore = BigDecimal.ZERO;
        for (BuildingParameterEntity entity : list) {
            BigDecimal score = entity.getParameterScore().multiply(entity.getWeightValue().divide(new BigDecimal(100)));
            totalScore = totalScore.add(score);
        }
        CityCoefficientEntity cityCoefficientEntity = cityCoefficientService.getCoefficient(adCode);
        if (Objects.nonNull(cityCoefficientEntity)) {
            return totalScore.multiply(cityCoefficientEntity.getCoefficient());
        }
        return totalScore.multiply(BigDecimal.ONE);
    }

    public String calculateProjectLevel(BigDecimal score) {
        if (score.compareTo(new BigDecimal(8.5)) >= 0) {
            return "AAA";
        } else if (score.compareTo(new BigDecimal(7)) >= 0)
            return "AA";
        else if (score.compareTo(new BigDecimal(6)) >= 0) {
            return "A";
        } else {
            throw new ServerException("当前楼宇评级未达到A级，无法提交认证申请");
        }
    }

    public BigDecimal calculateScoreByBuildingType(BuildingDetailsEntity details, String adCode, Integer buildingType, Integer dataFlag) {
        // 根据楼宇类型计算评分, 0 写字楼 1 商住楼 2 综合体 3 产业园区
        List<Long> scoreIds = new ArrayList<>();
        switch (buildingType) {
            case 0:
                scoreIds.add(details.getBuildingGrade());
                scoreIds.add(details.getBuildingLocation());
                scoreIds.add(details.getBuildingNumber());
                scoreIds.add(details.getBuildingPrice());
                scoreIds.add(details.getBuildingAge());
                scoreIds.add(details.getBuildingExterior());
                scoreIds.add(details.getBuildingLobby());
                scoreIds.add(details.getBuildingGarage());
                break;
            case 1:
                scoreIds.add(details.getBuildingAge());
                scoreIds.add(details.getBuildingLocation());
                scoreIds.add(details.getBuildingNumber());
                scoreIds.add(details.getBuildingPrice());
                scoreIds.add(details.getBuildingExterior());
                scoreIds.add(details.getBuildingHall());
                scoreIds.add(details.getBuildingGarage());
                break;
            case 2:
                scoreIds.add(details.getBuildingBrand());
                scoreIds.add(details.getBuildingLocation());
                scoreIds.add(details.getBuildingRating());
                break;
            case 3:
                scoreIds.add(details.getBuildingAge());
                scoreIds.add(details.getBuildingLocation());
                scoreIds.add(details.getBuildingNumber());
                scoreIds.add(details.getBuildingSettled());
                scoreIds.add(details.getBuildingExterior());
                scoreIds.add(details.getBuildingHall());
                break;
            default:
                throw new ServerException("当前楼宇类型不存在");
        }

        List<BuildingParameterEntity> list = buildingParameterService.list(Wrappers.<BuildingParameterEntity>lambdaQuery()
                .eq(BuildingParameterEntity::getDataFlag, dataFlag)
                .in(BuildingParameterEntity::getId, scoreIds));
        BigDecimal totalScore = BigDecimal.ZERO;
        for (BuildingParameterEntity entity : list) {
            BigDecimal score = entity.getParameterScore().multiply(entity.getWeightValue().divide(new BigDecimal(100)));
            totalScore = totalScore.add(score);
        }
        // 获取城市系数
        CityCoefficientEntity cityCoefficientEntity = cityCoefficientService.getCoefficient(adCode);
        if (Objects.nonNull(cityCoefficientEntity)) {
            return totalScore.multiply(cityCoefficientEntity.getCoefficient());
        }
        return totalScore.multiply(BigDecimal.ONE);
    }

    public BigDecimal calculateAIScoreByBuildingType(BuildingDetailsEntity details, Integer buildingType, BigDecimal coefficient) {
        // 根据楼宇类型计算评分, 0 写字楼 1 商住楼 2 综合体 3 产业园区
        List<BuildingParameterEntity.ScoreAndWeight<BigDecimal>> scoreAndWeights = new ArrayList<>();

        switch (buildingType) {
            case 0:
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingGradeId(), "buildingGrade"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingLocationId(), "buildingLocation"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingNumberId(), "buildingNumber"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingPriceId(), "buildingPrice"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingAgeId(), "buildingAge"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingExteriorId(), "buildingExterior"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingLobbyId(), "buildingLobby"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingGarageId(), "buildingGarage"));
                break;
            case 1:
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingAgeId(), "buildingAge"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingLocationId(), "buildingLocation"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingNumberId(), "buildingNumber"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingPriceId(), "buildingPrice"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingExteriorId(), "buildingExterior"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingHallId(), "buildingHall"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingGarageId(), "buildingGarage"));
                break;
            case 2:
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingBrandId(), "buildingBrand"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingLocationId(), "buildingLocation"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingRatingId(), "buildingRating"));
                break;
            case 3:
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingAgeId(), "buildingAge"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingLocationId(), "buildingLocation"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingNumberId(), "buildingNumber"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingSettledId(), "buildingSettled"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingExteriorId(), "buildingExterior"));
                scoreAndWeights.add(buildingParameterService.getScoreAndWeight(dataFlag, buildingType,
                        details.getThirdBuildingHallId(), "buildingHall"));
                break;
            default:
                throw new ServerException("当前楼宇类型不存在");
        }

        // 计算总分
        BigDecimal totalScore = BigDecimal.ZERO;
        for (BuildingParameterEntity.ScoreAndWeight<BigDecimal> scoreAndWeight : scoreAndWeights) {
            totalScore = totalScore.add(scoreAndWeight.Score().multiply(
                    scoreAndWeight.weight().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)));
        }

        // 乘以城市系数
        return totalScore.multiply(coefficient);
    }

    @Transactional
    @Override
    public Map<String, Object> applyUpdate(RatingApplyDto dto) {
        String userCode = UserThreadLocal.getUser().getWno();
        if (StringUtils.isBlank(userCode)) {
            throw new ServerException("当前登录人人员信息获取失败");
        }
        BuildingRatingEntity entity = this.getOne(Wrappers.<BuildingRatingEntity>lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, dto.getBuildingNo()).last("limit 1"));
        if (Objects.isNull(entity)) {
            throw new ServerException("该楼宇编号不存在");
        }
        String approveUser = "";
        PersonnelApprovalEntity personnelApprovalEntity = personnelApprovalService.getApprovalByCode(userCode);
        if (Objects.isNull(personnelApprovalEntity)) {
            approveUser = sysConfigService.getVal("approve_user", "CC2123");
        } else {
            approveUser = personnelApprovalEntity.getApprovalCode();
        }
        BuildingRatingEntity newEntity = new BuildingRatingEntity()
                .setId(entity.getId())
                .setBuildingNo(entity.getBuildingNo())
                .setBuildingName(dto.getBuildingName())
                .setBuildingType(dto.getBuildingType())
                .setBuildingScore(dto.getBuildingScore())
                .setBuildingDesc(dto.getBuildingDesc())
                .setStatus(BuildingRatingEntity.Status.WAIT_AUDIT.value)
                .setApproveUser(approveUser)
                .setMapNo(dto.getMapNo())
                .setMapAddress(rsaExample.encryptByPublic(dto.getMapAddress()))
                .setMapCity(dto.getMapCity())
                .setMapProvince(dto.getMapProvince())
                .setMapRegion(dto.getMapRegion())
                .setMapLongitude(rsaExample.encryptByPublic(dto.getMapLongitude()))
                .setMapLatitude(rsaExample.encryptByPublic(dto.getMapLatitude()))
                .setMapAdCode(dto.getMapAdCode())
                .setAuthenticationStart(LocalDateTime.now())
                .setAuthenticationEnd(LocalDateTime.now().plusDays(30))
                .setSubmitUser(userCode);
        if (CollectionUtils.isNotEmpty(dto.getBuildingExteriorPics())) {
            List<String> attachmentAddressMaps = dto.getBuildingExteriorPics();
            List<SysFileEntity> sysFileEntities = sysFileService.list(Wrappers.<SysFileEntity>lambdaQuery()
                    .in(SysFileEntity::getUrl, attachmentAddressMaps));
            List<Long> ids = new ArrayList<>();
            for (SysFileEntity sysFileEntity : sysFileEntities) {
                ids.add(sysFileEntity.getId());
            }
            newEntity.setBuildingExteriorPic(StringUtils.join(ids, ","));
        }
        if (CollectionUtils.isNotEmpty(dto.getBuildingLobbyPics())) {
            List<String> attachmentAddressMaps = dto.getBuildingLobbyPics();
            List<SysFileEntity> sysFileEntities = sysFileService.list(Wrappers.<SysFileEntity>lambdaQuery()
                    .in(SysFileEntity::getUrl, attachmentAddressMaps));
            List<Long> ids = new ArrayList<>();
            for (SysFileEntity sysFileEntity : sysFileEntities) {
                ids.add(sysFileEntity.getId());
            }
            newEntity.setBuildingLobbyPic(StringUtils.join(ids, ","));
        }
        if (CollectionUtils.isNotEmpty(dto.getBuildingHallPics())) {
            List<String> attachmentAddressMaps = dto.getBuildingHallPics();
            List<SysFileEntity> sysFileEntities = sysFileService.list(Wrappers.<SysFileEntity>lambdaQuery()
                    .in(SysFileEntity::getUrl, attachmentAddressMaps));
            List<Long> ids = new ArrayList<>();
            for (SysFileEntity sysFileEntity : sysFileEntities) {
                ids.add(sysFileEntity.getId());
            }
            newEntity.setBuildingHallPic(StringUtils.join(ids, ","));
        }
        this.updateById(newEntity);
        this.update(Wrappers.<BuildingRatingEntity>lambdaUpdate().eq(BuildingRatingEntity::getId, entity.getId())
                .set(BuildingRatingEntity::getRejectUser, null).set(BuildingRatingEntity::getRejectTime, null)
                .set(BuildingRatingEntity::getRejectDesc, null));
        BuildingDetailsEntity buildingDetailsEntity = buildingDetailsService.getOne(Wrappers.<BuildingDetailsEntity>lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, dto.getBuildingNo()).last("limit 1"));
        BuildingDetailsEntity newDetailsEntity = new BuildingDetailsEntity().setBuildingNo(newEntity.getBuildingNo())
                .setBuildingGrade(dto.getBuildingGrade())
                .setId(buildingDetailsEntity.getId())
                .setBuildingLocation(dto.getBuildingLocation())
                .setBuildingNumber(Long.valueOf(getEnum(dto.getBuildingNumberInput(), 1, null, dto.getBuildingType())))
                .setBuildingNumberInput(dto.getBuildingNumberInput())
                .setBuildingPrice(Long.valueOf(getEnum(dto.getBuildingPriceInput(), 3, dto.getMapAdCode(), dto.getBuildingType())))
                .setBuildingPriceInput(dto.getBuildingPriceInput())
                .setBuildingAge(Long.valueOf(getEnum(dto.getBuildingAgeInput(), 2, null, dto.getBuildingType())))
                .setBuildingAgeInput(dto.getBuildingAgeInput())
                .setBuildingExterior(dto.getBuildingExterior())
                .setBuildingLobby(dto.getBuildingLobby())
                .setBuildingGarage(dto.getBuildingGarage())
                .setBuildingHall(dto.getBuildingHall())
                .setBuildingBrand(dto.getBuildingBrand())
                .setBuildingRating(dto.getBuildingRating())
                .setBuildingSettled(dto.getBuildingSettled())
                .setThirdBuildingGrade(dto.getThirdBuildingGrade())
                .setThirdBuildingLocation(dto.getThirdBuildingLocation())
                .setThirdBuildingNumber(dto.getThirdBuildingNumber())
                .setThirdBuildingPrice(dto.getThirdBuildingPrice())
                .setThirdBuildingAge(dto.getThirdBuildingAge())
                .setThirdBuildingExterior(dto.getThirdBuildingExterior())
                .setThirdBuildingLobby(dto.getThirdBuildingLobby())
                .setThirdBuildingGarage(dto.getThirdBuildingGarage())
                .setThirdBuildingHall(dto.getThirdBuildingHall())
                .setThirdBuildingBrand(dto.getThirdBuildingBrand())
                .setThirdBuildingRating(dto.getThirdBuildingRating())
                .setThirdBuildingSettled(dto.getThirdBuildingSettled());
        this.buildingDetailsService.updateById(newDetailsEntity);
        newEntity.setBuildingScore(calculateScore(newDetailsEntity, dto.getMapAdCode()));
        BigDecimal coefficient = BigDecimal.ONE;
        CityCoefficientEntity cityCoefficientEntity = cityCoefficientService.getCoefficient(dto.getMapAdCode());
        if (Objects.nonNull(cityCoefficientEntity)) {
            coefficient = cityCoefficientEntity.getCoefficient();
        }
        this.updateById(new BuildingRatingEntity().setId(newEntity.getId())
                .setBuildingNo(newEntity.getBuildingNo())
                .setBuildingScore(newEntity.getBuildingScore())
                .setProjectLevel(calculateProjectLevel(newEntity.getBuildingScore()))
                .setFirstFloorExclusive(newEntity.getBuildingScore().multiply(coefficient)
                        .multiply(new BigDecimal("1.2")))
                .setFirstFloorShare(newEntity.getBuildingScore().multiply(coefficient).multiply(BigDecimal.ONE))
                .setNegativeFirstFloor(newEntity.getBuildingScore().multiply(coefficient)
                        .multiply(new BigDecimal("0.9")))
                .setNegativeTwoFloor(newEntity.getBuildingScore().multiply(coefficient).multiply(new BigDecimal("0.8")))
                .setTwoFloorAbove(newEntity.getBuildingScore().multiply(coefficient).multiply(new BigDecimal("0.8")))
                .setThirdFloorBelow(newEntity.getBuildingScore().multiply(coefficient)
                        .multiply(new BigDecimal("0.6"))));
        // 推送飞书消息
        Map<String, SysUserDto> nameMaps = sysUserService.getNameMaps(Collections.singletonList(newEntity.getApproveUser()));
        SysUserDto approveUserDto = nameMaps.get(newEntity.getApproveUser());
        if (Objects.nonNull(approveUserDto)) {
            if (0 == approveUserDto.getUserType()) {
                messageRecordService.sendApplyForMsg(newEntity.getApproveUser(), userCode, UserThreadLocal.getUser()
                        .getName(), newEntity.getBuildingName(), newEntity.getMapCity(), newEntity.getBuildingNo());
            }
        }
        return JsonUtils.fromMap(JsonUtils.toJson(newEntity));
    }

    @Override
    public Map<String, Object> getScoringDetails(String buildingNo, String ratingVersion) {
        BuildingRatingEntity ratingEntity = this.getOne(new LambdaQueryWrapper<BuildingRatingEntity>()
                .select(BuildingRatingEntity::getId,
                        BuildingRatingEntity::getBuildingType,
                        BuildingRatingEntity::getFirstFloorExclusive,
                        BuildingRatingEntity::getFirstFloorShare,
                        BuildingRatingEntity::getNegativeFirstFloor,
                        BuildingRatingEntity::getNegativeTwoFloor,
                        BuildingRatingEntity::getThirdFloorBelow,
                        BuildingRatingEntity::getThirdFloorBelow,
                        BuildingRatingEntity::getProjectLevel,
                        BuildingRatingEntity::getProjectAiLevel,
                        BuildingRatingEntity::getBuildingScore,
                        BuildingRatingEntity::getBuildingAiScore,
                        BuildingRatingEntity::getDataFlag,
                        BuildingRatingEntity::getMapAdCode,
                        BuildingRatingEntity::getRatingVersion,
                        BuildingRatingEntity::getProjectReviewLevel)
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo));
        if (Objects.isNull(ratingEntity)) {
            throw new ServerException("楼宇不存在");
        }

        BuildingDetailsEntity details;
        if (StrUtil.isNotBlank(ratingVersion) && !ratingVersion.equals(ratingEntity.getRatingVersion())) {
            // 非当前版本数据，需要查询对应版本的数据
            BuildingSnapshotEntity snapshotEntity = buildingSnapshotService.lambdaQuery()
                    .eq(BuildingSnapshotEntity::getBuildingRatingNo, buildingNo)
                    .eq(BuildingSnapshotEntity::getRatingVersion, ratingVersion)
                    .one();
            if (Objects.isNull(snapshotEntity)) {
                throw new ServerException("楼宇快照不存在");
            }
            ratingEntity = JSON.parseObject(snapshotEntity.getRatingSnapshot(), BuildingRatingEntity.class);
            details = JSON.parseObject(snapshotEntity.getDetailsSnapshot(), BuildingDetailsEntity.class);
        } else {
            details = buildingDetailsService.lambdaQuery()
                    .eq(BuildingDetailsEntity::getBuildingNo, buildingNo)
                    .eq(BuildingDetailsEntity::getDeleted, BooleFlagEnum.NO.getCode())
                    .last("limit 1")
                    .one();
        }

        Integer dataFlag = ratingEntity.getDataFlag();
        Integer buildingType = ratingEntity.getBuildingType();
        List<BuildingParameterEntity> rules = buildingParameterService.lambdaQuery()
                .eq(BuildingParameterEntity::getDataFlag, dataFlag)
                .eq(BuildingParameterEntity::getBuildingType, ratingEntity.getBuildingType())
                .orderByAsc(BuildingParameterEntity::getSort)
                .list();

        if (CollectionUtils.isEmpty(rules)) {
            return Collections.emptyMap();
        }

        Map<Long, BuildingParameterEntity> parameterMap = new HashMap<>();
        List<BuildingParameterEntity> parentList = rules.stream()
                .peek(ele -> parameterMap.put(ele.getId(), ele))
                .filter(ele -> Objects.equals(0L, ele.getParentId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentList)) {
            return Collections.emptyMap();
        }

        Map<String, Object> mapVo = new HashMap<>();
        List<Map<String, Object>> listVo = new ArrayList<>();
        mapVo.put("list", listVo);
        mapVo.put("projectLevel", ratingEntity.getProjectLevel());
        mapVo.put("projectReviewLevel", ratingEntity.getProjectReviewLevel());
        mapVo.put("buildingAiScore", ratingEntity.getBuildingAiScore());
        mapVo.put("thirdBuildingType", details.getThirdBuildingType());
        mapVo.put("BuildingType", BuildingRatingEntity.BuildingType.getNameByValue(ratingEntity.getBuildingType()));
        mapVo.put("projectAiLevel", ratingEntity.getProjectAiLevel());
        mapVo.put("totalScore", ratingEntity.getBuildingScore());
        mapVo.put("firstFloorExclusive", ratingEntity.getFirstFloorExclusive());
        mapVo.put("firstFloorShare", ratingEntity.getFirstFloorShare());
        mapVo.put("negativeFirstFloor", ratingEntity.getNegativeFirstFloor());
        mapVo.put("negativeTwoFloor", ratingEntity.getNegativeTwoFloor());
        mapVo.put("twoFloorAbove", ratingEntity.getThirdFloorBelow());
        mapVo.put("thirdFloorBelow", ratingEntity.getThirdFloorBelow());

        CityCoefficientEntity coefficient = cityCoefficientService.getCoefficient(ratingEntity.getMapAdCode());
        mapVo.put("coefficientValue", Objects.nonNull(coefficient) ? coefficient.getCoefficient() : BigDecimal.ONE);

        parentList.forEach(ele -> {
            Map<String, Object> map = new HashMap<>();
            // 优先以交付时间展示
            if (ele.getParameterName()
                    .equals("楼龄") && Objects.nonNull(details) && Objects.nonNull(details.getDeliveryDate())) {
                map.put("parameterName", "交付时间");
            } else {
                map.put("parameterName", ele.getParameterName());
            }

            // 人工评级
            BuildingParameterEntity valParameter = parameterMap.get(details == null ? null : details.valueId(ele.getParameterCode()));
            if (valParameter != null) {
                map.put("parameterValue", valParameter.getParameterName());
                map.put("score", valParameter.getParameterScore());
                map.put("weight", valParameter.getWeightValue());
                map.put("avgScore", valParameter.getParameterScore().multiply(valParameter.getWeightValue())
                        .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            }

            // AI评级
            map.put("thirdValue", details == null ? null : details.thirdValue(ele.getParameterCode()));
            Long ruleId = Objects.isNull(details) ? null : details.valueId("third" + ele.getParameterCode() + "id");
            if (Objects.nonNull(ruleId)) {
                BuildingParameterEntity.ScoreAndWeight<BigDecimal> scoreAndWeight = buildingParameterService.getScoreAndWeight(
                        dataFlag, buildingType, ruleId, ele.getParameterCode());

                BigDecimal aiScore = BigDecimal.ZERO;
                BigDecimal aiAvgScore = BigDecimal.ZERO;
                if (ObjectUtil.isNotNull(scoreAndWeight)) {
                    aiScore = scoreAndWeight.Score();
                    aiAvgScore = scoreAndWeight.Score().multiply(scoreAndWeight.weight())
                            .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                }
                map.put("aiScore", aiScore);
                map.put("aiAvgScore", aiAvgScore);
            }

            listVo.add(map);
        });
        return mapVo;
    }

    @Override
    public Map<String, Object> scoringFsDetails(String buildingNo, String ratingVersion) {
        Map<String, Object> mapVo = getScoringDetails(buildingNo, ratingVersion);
        // 获取大屏系数
        BuildingScreenVO screenVO = buildingScreenService.getBuildingScreenByNo(buildingNo);
        if (Objects.nonNull(screenVO) && buildingScreenService.isLargeScreen(screenVO)) {
            mapVo.put("submitCoefficient", screenVO.getSubmitCoefficient());
            mapVo.put("finalCoefficient", screenVO.getFinalCoefficient());
        }
        mapVo.put("largeScreen", buildingScreenService.isLargeScreen(screenVO));
        return mapVo;
    }

    @Override
    public BuildingRateDto infoFs(String buildingNo) {
        BuildingRateDto buildingRateDto = new BuildingRateDto();
        BuildingRatingEntity entity = this.getOne(Wrappers.<BuildingRatingEntity>lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo).last("limit 1"));
        if (Objects.isNull(entity)) {
            throw new ServerException("该楼宇编号不存在");
        }
        try {
            entity.setMapLatitude(rsaExample.decryptByPrivate(entity.getMapLatitude()));
            entity.setMapLongitude(rsaExample.decryptByPrivate(entity.getMapLongitude()));
        } catch (Exception ignored) {

        }
        buildingRateDto.setBuildingNo(buildingNo);
        buildingRateDto.setBuildingName(entity.getBuildingName());
        buildingRateDto.setBuildingTypeName(BuildingRatingEntity.BuildingType.getNameByValue(entity.getBuildingType()));
        buildingRateDto.setMapCity(entity.getMapProvince() + "-" + entity.getMapCity() + "-" + entity.getMapRegion());
        buildingRateDto.setMapAddress(rsaExample.decryptByPrivate(entity.getMapAddress()));
        buildingRateDto.setProjectLevel(entity.getProjectLevel());
        buildingRateDto.setProjectAiLevel(entity.getProjectAiLevel());
        // 地图地址
        MapAddressDto mapAddressDto = mapApiService.getConvertAddress(entity.getMapLongitude(), entity.getMapLatitude());
        String mapConvertAddress = mapAddressDto.getX() + "," + mapAddressDto.getY();

        AddressDto addressDto = new AddressDto();
        addressDto.setTitle("查看地图（PC端）");
        addressDto.setUrl("https://map.baidu.com/search/" + entity.getBuildingName() + "/@" + mapConvertAddress + ",19z?querytype=s&wd=" + entity.getBuildingName() + "");
        buildingRateDto.setMapConvertAddress(addressDto);

        AddressDto addressDto1 = new AddressDto();
        addressDto1.setUrl("https://meth.cshimedia.com/detailPage?buildingNo=" + entity.getBuildingNo());
        addressDto1.setTitle("评分详情");
        buildingRateDto.setScoreDetailAddress(addressDto1);

        CityAddressDto cityAddressDto = mapApiService.getCityId("路", entity.getMapCity());
        String cityId = cityAddressDto.getCityid();

        AddressDto addressDto2 = new AddressDto();
        addressDto2.setUrl("https://map.baidu.com/mobile/webapp/place/list/qt=con&wd=" + entity.getBuildingName() + "&c=" + cityId + "&contp=1/vt=map");
        addressDto2.setTitle("查看地图（移动端）");
        buildingRateDto.setMobileConverAddress(addressDto2);

        // 查询价格申请单位
        buildingRateDto.setPriceApplies(priceApplyDao.selectList(Wrappers.<PriceApplyEntity>lambdaQuery()
                        .select(PriceApplyEntity::getApplyCode, PriceApplyEntity::getId)
                        .eq(PriceApplyEntity::getBuildingNo, buildingNo))
                .stream()
                .map(item -> new PriceApplySimpleDto(item.getId(), item.getApplyCode()))
                .collect(Collectors.toList()));

        return buildingRateDto;
    }

    public String getH5MapUrl(BuildingRatingEntity entity) {
        CityAddressDto cityAddressDto = mapApiService.getCityId("路", entity.getMapCity());
        String cityId = cityAddressDto.getCityid();
        String url = "https://map.baidu.com/mobile/webapp/place/list/qt=con&wd=" + entity.getBuildingName() + "&c=" + cityId + "&contp=1/vt=map";
        return url;
    }

    @Override
    public List<Map<String, Object>> getStatusChart() {
        return this.baseMapper.getStatusChart();
    }

    @Override
    public List<Map<String, Object>> getUv() {
        return this.baseMapper.getUv();
    }

    @Override
    public List<Map<String, Object>> getList() {
        return this.baseMapper.getList();
    }

    @Override
    public List<Map<String, Object>> getProvinceList() {
        List<Map<String, Object>> list = this.baseMapper.getProvinceList();
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (Map<String, Object> map : list) {
                if (Converts.toInt(map.get("value")) > 0) {
                    result.add(map);
                }
            }
        }
        return result;
    }


    @Override
    public List<Map<String, Object>> getTotal() {
        return this.baseMapper.getTotal();
    }

    @Override
    public void fillCrmBuilding(List<CrmCustomerListDto> crmCustomerDtoList) {
        if (CollUtil.isEmpty(crmCustomerDtoList)) {
            return;
        }

        List<String> customerIdList = crmCustomerDtoList.stream().map(CrmCustomerListDto::getCustomerId)
                .collect(Collectors.toList());
        LambdaQueryWrapper<BuildingRatingEntity> queryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda()
                .in(BuildingRatingEntity::getCustomerId, customerIdList);
        List<BuildingRatingEntity> buildingRatingList = this.list(queryWrapper);
        if (CollUtil.isEmpty(buildingRatingList)) {
            return;
        }
        Map<String, BuildingRatingEntity> buildingRatingMap = buildingRatingList.stream()
                .collect(Collectors.toMap(BuildingRatingEntity::getCustomerId, Function.identity(), (k1, k2) -> k2));

        List<String> buildingNoList = buildingRatingList.stream().map(BuildingRatingEntity::getBuildingNo)
                .collect(Collectors.toList());
        List<BuildingDetailsEntity> detailsEntityList = buildingDetailsService.listDetailsByBuildingNo(buildingNoList);
        Map<String, BuildingDetailsEntity> detailsMap = new HashMap<>();
        if (CollUtil.isNotEmpty(detailsEntityList)) {
            detailsMap = detailsEntityList.stream()
                    .collect(Collectors.toMap(BuildingDetailsEntity::getBuildingNo, Function.identity(), (k1, k2) -> k2));
        }

        // 填充楼宇主数据名称

        Map<String, String> collect = buildingMetaService.lambdaQuery()
                .select(BuildingMetaEntity::getBuildingRatingNo, BuildingMetaEntity::getBuildingName)
                .in(BuildingMetaEntity::getBuildingRatingNo, buildingNoList).list().stream()
                .collect(Collectors.toMap(BuildingMetaEntity::getBuildingRatingNo, BuildingMetaEntity::getBuildingName));


        for (CrmCustomerListDto crmCustomerListDto : crmCustomerDtoList) {
            BuildingRatingEntity buildingRatingEntity = buildingRatingMap.get(crmCustomerListDto.getCustomerId());
            if (buildingRatingEntity == null) {
                continue;
            }
            crmCustomerListDto.setBuildingNo(buildingRatingEntity.getBuildingNo());
            crmCustomerListDto.setBuildingTypeName(BuildingRatingEntity.BuildingType.getNameByValue(buildingRatingEntity.getBuildingType()));
            BuildingDetailsEntity buildingDetailsEntity = detailsMap.get(buildingRatingEntity.getBuildingNo());
            if (buildingDetailsEntity == null) {
                continue;
            }
            crmCustomerListDto.setBuildingNumber(buildingDetailsEntity.getBuildingNumber());
            crmCustomerListDto.setBuildingPrice(buildingDetailsEntity.getBuildingPrice());
            crmCustomerListDto.setCreateBy(buildingRatingEntity.getSubmitUser());
            crmCustomerListDto.setCustomerName(collect.get(buildingRatingEntity.getBuildingNo()));
        }
    }

    @Override
    public List<BuildingRatingEntity> listNotCustomerId() {
        LambdaQueryWrapper<BuildingRatingEntity> queryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda()
                .eq(BuildingRatingEntity::getCrmPushStatus, 1)
                .and(a -> a.isNull(BuildingRatingEntity::getCustomerId).or()
                        .eq(BuildingRatingEntity::getCustomerId, ""));
        return this.list(queryWrapper);
    }

    @Override
    public List<BuildingRatingEntity> listPushCrm(Long buildingId) {
        LambdaQueryWrapper<BuildingRatingEntity> queryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda()
                .eq(BuildingRatingEntity::getCrmPushStatus, 1)
                .gt(BuildingRatingEntity::getId, buildingId).last("limit 20");
        return this.list(queryWrapper);
    }

    @Override
    public void updateCustomerId(Long id, String customerId) {
        LambdaUpdateWrapper<BuildingRatingEntity> updateWrapper = new UpdateWrapper<BuildingRatingEntity>().lambda()
                .eq(BuildingRatingEntity::getId, id)
                .set(BuildingRatingEntity::getCustomerId, customerId);
        this.update(updateWrapper);
    }

    @Override
    public void cronBuildingPushCrm(String buildingNo) {

        try {
            dataHandlerService.crmHandlerBuildingRating(buildingNo);
        } finally {
            SecurityUser.clearLogin();
        }
    }

    @Override
    public Map<String, Object> applyList(String name, Integer pageSize, Integer pageNum) {
        String userCode = UserThreadLocal.getUser().getWno();
        if (StringUtils.isBlank(userCode)) {
            throw new ServerException("当前登录人人员信息获取失败");
        }
//        List<BuildingRatingEntity> page = this.page(Wrappers.<BuildingRatingEntity>lambdaQuery()
//                .eq(BuildingRatingEntity::getSubmitUser, user.getUserCode())
//                .like(StringUtils.isNotBlank(name), BuildingRatingEntity::getBuildingName, name).orderByDesc(BuildingRatingEntity::getCreateTime));
        Page<BuildingRatingEntity> page = this.page(new Page<>(pageNum, pageSize), Wrappers.<BuildingRatingEntity>lambdaQuery()
                .eq(BuildingRatingEntity::getSubmitUser, userCode)
                .like(StringUtils.isNotBlank(name), BuildingRatingEntity::getBuildingName, name)
                .orderByDesc(BuildingRatingEntity::getUpdateTime));
        // 解码地址
        page.getRecords().forEach(e -> e.setMapAddress(rsaExample.decryptByPrivate(e.getMapAddress())));
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            List<String> userCodes = new ArrayList<>(page.getRecords().size());
            List<String> buildingNos = new ArrayList<>(page.getRecords().size());
            result = JsonUtils.fromListMap(JsonUtils.toJson(page.getRecords()));
            page.getRecords()
                    .forEach(ele -> {
                        buildingNos.add(ele.getBuildingNo());
                        userCodes.addAll(Arrays.asList(ele.getApproveUser(), ele.getSubmitUser(), ele.getRejectUser()));
                    });
            Map<String, SysUserDto> nameMaps = sysUserService.getNameMaps(userCodes);

            // 查询对应的审核操作类型
            Map<String, List<ScreenApproveRecordEntity>> operateTypeMapping = screenApproveRecordService.lambdaQuery()
                    .select(ScreenApproveRecordEntity::getNaturalKey, ScreenApproveRecordEntity::getOperateType)
                    .eq(ScreenApproveRecordEntity::getSceneType, SceneTypeEnum.BUILDING.getType())
                    .in(ScreenApproveRecordEntity::getNaturalKey, buildingNos)
                    .orderByDesc(ScreenApproveRecordEntity::getId)
                    .list().stream()
                    .collect(Collectors.groupingBy(ScreenApproveRecordEntity::getNaturalKey));

            for (Map<String, Object> map : result) {
                SysUserDto approveUser = nameMaps.get(Converts.toStr(map.get("approveUser")));
                if (Objects.nonNull(approveUser)) {
                    map.put("approveUserName", approveUser.getRealName());
                    map.put("approveUserType", approveUser.getUserType());
                    map.put("approveBelongCode", approveUser.getBelongCode());
                    map.put("approveBelongName", approveUser.getBelongName());
                }
                SysUserDto submitUser = nameMaps.get(Converts.toStr(map.get("submitUser")));
                if (Objects.nonNull(submitUser)) {
                    map.put("submitUserName", submitUser.getRealName());
                    map.put("submitUserType", submitUser.getUserType());
                    map.put("submitBelongCode", submitUser.getBelongCode());
                    map.put("submitBelongName", submitUser.getBelongName());
                }
                SysUserDto rejectUser = nameMaps.get(Converts.toStr(map.get("rejectUser")));
                if (Objects.nonNull(rejectUser)) {
                    map.put("rejectUserName", rejectUser.getRealName());
                    map.put("rejectUserType", rejectUser.getUserType());
                    map.put("rejectBelongCode", rejectUser.getBelongCode());
                    map.put("rejectBelongName", rejectUser.getBelongName());
                }

                List<ScreenApproveRecordEntity> approveRecordEntities = operateTypeMapping.get(Converts.toStr(map.get("buildingNo")));
                if (CollectionUtils.isNotEmpty(approveRecordEntities)) {
                    map.put("operateType", approveRecordEntities.get(0).getOperateType());
                } else {
                    // 老数据都是新建数据审核流程
                    map.put("operateType", ScreenApproveRecordEntity.OperateType.NEW_DATA.getCode());
                }
            }
        }
        page.setRecords(null);
        Map<String, Object> res = new HashMap<>();
        res.put("page", page);
        res.put("result", result);
        return res;
    }

    @Override
    public List<Map<String, Object>> listAudited(String name, boolean allUser) {
        String userCode = UserThreadLocal.getUser().getWno();
        if (StringUtils.isBlank(userCode)) {
            throw new ServerException("当前登录人人员信息获取失败");
        }

        List<BusinessProjectDto> projects = businessOpportunityService.getPriceApplyBusiness(name, allUser,
                List.of(BusinessChangeStatusEnum.REACHING_INTENTION.getCode(), BusinessChangeStatusEnum.PROPOSAL_QUOTATION.getCode()),
                userCode);
        if (CollectionUtils.isEmpty(projects)) {
            return Collections.emptyList();
        }

        // 查询价格申请状态
        Set<String> buildingCodes = projects.stream().map(BusinessProjectDto::getBuildingNo)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, Integer> buildingStatusMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(buildingCodes)) {
            buildingStatusMap = priceApplyDao.selectList(Wrappers.<PriceApplyEntity>lambdaQuery()
                            .select(PriceApplyEntity::getBuildingNo, PriceApplyEntity::getStatus)
                            .in(PriceApplyEntity::getBuildingNo, buildingCodes)
                            .orderByAsc(PriceApplyEntity::getStatus, PriceApplyEntity::getId)
                    ).stream()
                    .collect(Collectors.toMap(PriceApplyEntity::getBuildingNo, PriceApplyEntity::getStatus, (o, n) -> n));
        }

        List<Map<String, Object>> results = JsonUtils.fromListMap(JsonUtils.toJson(projects));
        for (Map<String, Object> map : results) {
            String buildingNo = Converts.toStr(map.get("buildingNo"));
            Integer buildingType = Converts.toInt(map.get("buildingType"));
            String buildingTypeName = BuildingRatingEntity.BuildingType.getNameByValue(buildingType);
            map.put("buildingTypeName", buildingTypeName);
            map.put("priceApplyStatus", buildingStatusMap.getOrDefault(buildingNo, 1));
        }

        return results;
    }

    @Override
    public BuildingRatingEntity getByBuildingNo(String buildingNo) {
        LambdaQueryWrapper<BuildingRatingEntity> lambdaQueryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .last(" limit 1");
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public List<BuildingRatingEntity> listByBuildingNo(List<String> buildingNoList) {
        LambdaQueryWrapper<BuildingRatingEntity> lambdaQueryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda()
                .in(BuildingRatingEntity::getBuildingNo, buildingNoList);

        return this.list(lambdaQueryWrapper);
    }

    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> approve(RatingApproveDto dto) {
        String userCode = UserThreadLocal.getUser().getWno();
        if (StringUtils.isBlank(userCode)) {
            throw new ServerException("当前登录人人员信息获取失败");
        }
        BuildingRatingEntity entity = this.getOne(Wrappers.<BuildingRatingEntity>lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, dto.getBuildingNo())
                .last("limit 1"));

        if (Objects.isNull(entity)) {
            throw new ServerException("楼宇不存在");
        }

        if (!entity.getStatus().equals(BuildingRatingEntity.Status.WAIT_AUDIT.getValue())) {
            throw new ServerException("楼宇已审核");
        }

        // 大屏数据审核标识
        Boolean isLargeScreen = buildingGeneService.isScreen(entity.getBuildingNo());

        // 获取待审核节点
        ScreenApproveRecordEntity approveRecord = screenApproveRecordService.lambdaQuery()
                .eq(ScreenApproveRecordEntity::getNaturalKey, dto.getBuildingNo())
                .eq(ScreenApproveRecordEntity::getApproveUser, userCode)
                .eq(ScreenApproveRecordEntity::getSceneType, SceneTypeEnum.BUILDING.getType())
                .eq(ScreenApproveRecordEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue())
                .eq(ScreenApproveRecordEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(ScreenApproveRecordEntity::getApproveLevel, 1, 2)
                .orderByDesc(ScreenApproveRecordEntity::getId)
                .last("limit 1")
                .one();
        // 非写字楼大屏老数据没有生成审批记录，不能拦截，只能拦截大屏
        if (isLargeScreen && Objects.isNull(approveRecord)) {
            throw new ServerException("无待审核节点，不能审核");
        }

        // 更新审核记录
        if (Objects.nonNull(approveRecord)) {
            screenApproveRecordService.lambdaUpdate()
                    .set(ScreenApproveRecordEntity::getStatus, dto.getStatus())
                    .set(ScreenApproveRecordEntity::getRemark, dto.getDesc())
                    .set(ScreenApproveRecordEntity::getApproveTime, LocalDateTime.now())
                    .set(StrUtil.isNotBlank(dto.getFinalCoefficient()), ScreenApproveRecordEntity::getFinalCoefficient, dto.getFinalCoefficient())
                    .eq(ScreenApproveRecordEntity::getId, approveRecord.getId())
                    .update();
        }

        // 完善评级审核且审核不通过，需要回滚数据
        if (dto.getStatus().equals(BuildingRatingEntity.Status.FAILED_AUDIT.getValue())
                && ScreenApproveRecordEntity.OperateType.IMPROVE_RATING.getCode()
                .equals(approveRecord.getOperateType())) {
            rollBack(entity.getBuildingNo());
            return JsonUtils.fromMap(JsonUtils.toJson(entity));
        }

        // 大屏处理
        if (isLargeScreen) {
            // 审核通过刷新复核系数
            if (dto.getStatus().equals(BuildingRatingEntity.Status.AUDITED.getValue())) {
                if (StrUtil.isBlank(dto.getFinalCoefficient())) {
                    throw new ServerException("复核系数不能为空");
                }
                buildingGeneService.lambdaUpdate()
                        .set(BuildingGeneEntity::getFinalCoefficient, dto.getFinalCoefficient())
                        .eq(BuildingGeneEntity::getBuildingRatingNo, dto.getBuildingNo())
                        .update();
            } else {
                // 未通过置空复核系数
                LambdaUpdateWrapper<BuildingGeneEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(BuildingGeneEntity::getFinalCoefficient, null)
                        .eq(BuildingGeneEntity::getBuildingRatingNo, dto.getBuildingNo());
                buildingGeneService.update(updateWrapper);
            }
            List<String> auditUsers = auditConfigProperties.getRatingAuditUsers(isLargeScreen, entity.getMapCity());
            boolean isFirstLevel = approveRecord.getApproveLevel() == 1;
            boolean isAuditPass = dto.getStatus().equals(BuildingRatingEntity.Status.AUDITED.getValue());
            String firstUser = auditUsers.get(0);
            String secondUser = auditUsers.get(1);

            if (isFirstLevel && isAuditPass) {
                if (!StrUtil.equals(firstUser, secondUser)) {
                    // 一二级审批人不一致，创建二级审核节点
                    createNextApproveRecord(approveRecord.getOperateType(), dto, isLargeScreen, entity.getMapCity());
                    return JsonUtils.fromMap(JsonUtils.toJson(entity));
                } else {
                    // 一二级审批人相同，自动通过二级审核并生成记录
                    ScreenApproveRecordEntity screenApproveRecordEntity = new ScreenApproveRecordEntity();
                    screenApproveRecordEntity.setApproveLevel(2);
                    screenApproveRecordEntity.setApproveUser(secondUser);
                    screenApproveRecordEntity.setNaturalKey(dto.getBuildingNo());
                    screenApproveRecordEntity.setStatus(BuildingRatingEntity.Status.AUDITED.value);
                    if (StrUtil.isNotBlank(dto.getFinalCoefficient())) {
                        screenApproveRecordEntity.setFinalCoefficient(new BigDecimal(dto.getFinalCoefficient()));
                    }
                    screenApproveRecordEntity.setSceneType(SceneTypeEnum.BUILDING.getType());
                    screenApproveRecordEntity.setRemark("系统自动通过");
                    screenApproveRecordEntity.setOperateType(approveRecord.getOperateType());
                    screenApproveRecordEntity.setApproveTime(LocalDateTime.now());
                    screenApproveRecordService.save(screenApproveRecordEntity);
                }
            }
        }

        // 刷新楼宇审核相关字段
        entity.setStatus(dto.getStatus());
        int buildingStatus = BuildingRatingEntity.BuildingStatus.CONFIRMING.getValue();
        if (dto.getStatus().equals(BuildingRatingEntity.Status.AUDITED.getValue())) {
            buildingStatus = BuildingRatingEntity.BuildingStatus.CONFIRMED.getValue();
        } else if (dto.getStatus().equals(BuildingRatingEntity.Status.FAILED_AUDIT.getValue())) {
            buildingStatus = BuildingRatingEntity.BuildingStatus.UN_CONFIRM.getValue();
        }

        BuildingRatingEntity newEntity = new BuildingRatingEntity().setId(entity.getId())
                .setStatus(dto.getStatus())
                .setCrmPushStatus(0)
                .setBuildingStatus(buildingStatus)
                .setApproveDesc(dto.getStatus() == 1 || dto.getStatus() == 3 ? dto.getDesc() : null)
                .setApproveTime(dto.getStatus() == 1 || dto.getStatus() == 3 || dto.getStatus() == 2 ? LocalDateTime.now() : null)
                .setRejectUser(dto.getStatus() == 2 ? userCode : null)
                .setRejectDesc(dto.getStatus() == 2 ? dto.getDesc() : null)
                .setRejectTime(dto.getStatus() == 2 ? LocalDateTime.now() : null);
        // 审核通过需要更新大小屏对应的评级状态
        if (dto.getStatus().equals(BuildingRatingEntity.Status.AUDITED.getValue())) {
            newEntity.setSmallScreenRatingFlag(BooleFlagEnum.YES.getCode());
            if (isLargeScreen) {
                newEntity.setLargeScreenRatingFlag(BooleFlagEnum.YES.getCode());
            }
        }
        this.updateById(newEntity);

        // 审核不通过或放弃，非公海客户清空入公海时间
        if (BuildingRatingEntity.HighSeaFlagEnum.NO.getCode().equals(entity.getHighSeaFlag())
                && (dto.getStatus().equals(BuildingRatingEntity.Status.FAILED_AUDIT.getValue())
                || dto.getStatus().equals(BuildingRatingEntity.Status.ABANDONED.getValue()))) {
            LambdaUpdateWrapper<BuildingRatingEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BuildingRatingEntity::getId, entity.getId());
            updateWrapper.set(BuildingRatingEntity::getEnterSeaTime, null);
            update(updateWrapper);
        }

        // 楼宇主数据状态修改及商机处理
        buildingMetaService.updateStatus(dto.getBuildingNo(), dto.getStatus());

        // 增加楼宇状态变动记录
        addBuildingChangeLog(entity.getId(), entity.getBuildingNo(), dto.getStatus());

        return JsonUtils.fromMap(JsonUtils.toJson(entity));
    }

    @Deprecated
    @Override
    public void rollBack(Integer id) {
        if (Objects.isNull(id)) {
            throw new ServerException("回滚失败，快照数据id为空");
        }

        BuildingSnapshotEntity snapshotEntity = buildingSnapshotService.getById(id);
        if (Objects.isNull(snapshotEntity)) {
            throw new ServerException("回滚失败，无快照数据");
        }

        rollBack(snapshotEntity);
    }

    @Deprecated
    @Override
    public void rollBack(String buildingNo) {
        BuildingSnapshotEntity snapshotEntity = buildingSnapshotService.lambdaQuery()
                .eq(BuildingSnapshotEntity::getBuildingRatingNo, buildingNo)
                .orderByDesc(BuildingSnapshotEntity::getId)
                .last("limit 1")
                .one();
        if (Objects.isNull(snapshotEntity)) {
            log.error("回滚失败，无快照数据，buildingNo {}", buildingNo);
            throw new ServerException("回滚失败，无快照数据");
        }

        rollBack(snapshotEntity);
    }

    @Deprecated
    private void rollBack(BuildingSnapshotEntity snapshotEntity) {
        String buildingNo = snapshotEntity.getBuildingRatingNo();

        log.info("回滚快照数据，buildingNo {}，snapshotId {}", buildingNo, snapshotEntity.getId());

        // 回滚rating表
        if (StrUtil.isBlank(snapshotEntity.getRatingSnapshot())) {
            log.error("回滚失败，无rating表快照数据，buildingNo {}", buildingNo);
            throw new ServerException("回滚失败，无rating表快照数据");
        }
        doRollBack(snapshotEntity.getRatingSnapshot(), BuildingRatingEntity.class, buildingRatingService::update);

        // 回滚基因表
        if (StrUtil.isNotBlank(snapshotEntity.getScreenSnapshot())) {
            doRollBack(snapshotEntity.getScreenSnapshot(), BuildingGeneEntity.class, buildingGeneService::update);
        } else {
            // 快照数据为空，表示原来没有基因数据，如果存在表示客户编辑处保存了其他基因字段，需要置空可回滚字段
            Long count = buildingGeneService.lambdaQuery()
                    .eq(BuildingGeneEntity::getBuildingRatingNo, buildingNo)
                    .count();
            if (count > 0) {
                LambdaUpdateWrapper<BuildingGeneEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(BuildingGeneEntity::getBuildingRatingNo, buildingNo);
                updateWrapper.set(BuildingGeneEntity::getTotalUnitCount, null);
                updateWrapper.set(BuildingGeneEntity::getElevatorCount, null);
                updateWrapper.set(BuildingGeneEntity::getCompanyCount, null);
                updateWrapper.set(BuildingGeneEntity::getBuildingSpacing, null);
                updateWrapper.set(BuildingGeneEntity::getBuildingCeilingHeight, null);
                updateWrapper.set(BuildingGeneEntity::getSpec, "");
                updateWrapper.set(BuildingGeneEntity::getSubmitCoefficient, null);
                updateWrapper.set(BuildingGeneEntity::getFinalCoefficient, null);
                buildingGeneService.update(updateWrapper);
                log.info("回滚基因表，置空需要回滚字段，buildingNo {}", buildingNo);
            }
        }

        // 回滚详情表
        if (StrUtil.isNotBlank(snapshotEntity.getDetailsSnapshot())) {
            doRollBack(snapshotEntity.getDetailsSnapshot(), BuildingDetailsEntity.class, buildingDetailsService::update);
        } else {
            buildingDetailsService.lambdaUpdate()
                    .eq(BuildingDetailsEntity::getBuildingNo, buildingNo)
                    .remove();
            log.info("回滚详情表，删除对应数据，buildingNo {}", buildingNo);
        }

        // 删除快照
        buildingSnapshotService.removeById(snapshotEntity.getId());
    }

    @Deprecated
    private <T> void doRollBack(String jsonStr, Class<T> clazz, Consumer<UpdateWrapper<T>> consumer) {
        T entity = JSON.parseObject(jsonStr, clazz);
        UpdateWrapper<T> updateWrapper;
        try {
            updateWrapper = reflectUpdateWrapper(entity);
        } catch (IllegalAccessException e) {
            log.error("回滚失败，反射组装sql异常", e);
            throw new ServerException("回滚失败，反射组装sql异常");
        }
        if (updateWrapper.isEmptyOfWhere()) {
            log.error("回滚失败，无回滚条件，sql：{}", updateWrapper.getTargetSql());
            throw new ServerException("回滚失败，无回滚条件");
        }
        consumer.accept(updateWrapper);
    }

    @Deprecated
    private <T> UpdateWrapper<T> reflectUpdateWrapper(T entity) throws IllegalAccessException {
        Field[] declaredFields = entity.getClass().getDeclaredFields();
        // 全字段需要回滚标识
        RollBack clazzRollBack = entity.getClass().getAnnotation(RollBack.class);
        boolean isClazzRollBack = Objects.nonNull(clazzRollBack) && clazzRollBack.value();
        UpdateWrapper<T> updateWrapper = new UpdateWrapper<>();
        for (Field field : declaredFields) {
            TableField tableField = field.getAnnotation(TableField.class);
            if (Objects.nonNull(tableField) && !tableField.exist()) {
                continue;
            }

            field.setAccessible(true);

            // 主键设置为条件
            TableId tableId = field.getAnnotation(TableId.class);
            if (Objects.nonNull(tableId)) {
                Object id = field.get(entity);
                if (Objects.isNull(id)) {
                    log.error("回滚失败，没有主键值，entity：{}", JSON.toJSONString(entity));
                    throw new ServerException("回滚失败，没有主键值");
                }
                updateWrapper.eq(StrUtil.toUnderlineCase(field.getName()).toLowerCase(), id);
                continue;
            }

            RollBack filedRollBack = field.getAnnotation(RollBack.class);
            if (Objects.nonNull(filedRollBack) && !filedRollBack.value()) {
                // 字段设置了不能回滚，直接忽略
                continue;
            }

            // 类级别回滚或指定字段回滚，设置回滚字段
            if (isClazzRollBack || Objects.nonNull(filedRollBack)) {
                updateWrapper.set(StrUtil.toUnderlineCase(field.getName()).toLowerCase(), field.get(entity));
            }
        }

        // BaseEntity父类处理
        if (entity instanceof BaseEntity) {
            Field[] parentFields = entity.getClass().getSuperclass().getDeclaredFields();
            for (Field field : parentFields) {
                TableField tableField = field.getAnnotation(TableField.class);
                if (Objects.nonNull(tableField) && !tableField.exist()) {
                    continue;
                }
                field.setAccessible(true);
                updateWrapper.set(StrUtil.toUnderlineCase(field.getName()).toLowerCase(), field.get(entity));
            }
        }

        return updateWrapper;
    }

    private void createNextApproveRecord(Integer operateType, RatingApproveDto dto, boolean isScreen, String city) {
        List<String> auditUsers = auditConfigProperties.getRatingAuditUsers(isScreen, city);
        if (auditUsers.size() < 2) {
            log.info("城市{}，审批人配置异常，审核人数据不完整", city);
        }
        ScreenApproveRecordEntity screenApproveRecordEntity = new ScreenApproveRecordEntity();
        screenApproveRecordEntity.setApproveLevel(2);
        screenApproveRecordEntity.setApproveUser(auditUsers.get(auditUsers.size() - 1));
        screenApproveRecordEntity.setNaturalKey(dto.getBuildingNo());
        screenApproveRecordEntity.setStatus(BuildingRatingEntity.Status.WAIT_AUDIT.value);
        screenApproveRecordEntity.setSceneType(SceneTypeEnum.BUILDING.getType());
        screenApproveRecordEntity.setOperateType(operateType);
        screenApproveRecordService.save(screenApproveRecordEntity);

        this.lambdaUpdate()
                .set(BuildingRatingEntity::getApproveUser, auditUsers.get(auditUsers.size() - 1))
                .set(BuildingRatingEntity::getApproveTime, LocalDateTime.now())
                .eq(BuildingRatingEntity::getBuildingNo, dto.getBuildingNo())
                .update();

        try {
            // 推送飞书消息
            BuildingRatingEntity newEntity = this.lambdaQuery()
                    .eq(BuildingRatingEntity::getBuildingNo, dto.getBuildingNo())
                    .one();
            Map<String, SysUserDto> nameMaps = sysUserService.getUserNameMaps(Arrays.asList(newEntity.getApproveUser(), newEntity.getSubmitUser()));
            SysUserDto approveUserDto = nameMaps.get(newEntity.getApproveUser());
            SysUserDto submitUserDto = nameMaps.get(newEntity.getSubmitUser());
            if (Objects.nonNull(approveUserDto) && 0 == approveUserDto.getUserType()) {
                messageRecordService.sendApplyForMsg(newEntity.getApproveUser(), submitUserDto.getEmpCode(), submitUserDto.getRealName(),
                        newEntity.getBuildingName(), newEntity.getMapCity(), newEntity.getBuildingNo());
            }
        } catch (Exception e) {
            log.error("推送飞书楼宇审批消息失败", e);
        }
    }

    @Deprecated
    @Override
    public Map<String, Object> approveList(String name, Integer status, Integer pageSize, Integer pageNum) {
        String userCode = UserThreadLocal.getUser().getWno();
        if (StringUtils.isBlank(userCode)) {
            throw new ServerException("当前登录人人员信息获取失败");
        }
        Page<BuildingRatingEntity> page = null;
        // 待我审
        if (status.equals(0)) {
            page = this.page(new Page<>(pageNum, pageSize), Wrappers.<BuildingRatingEntity>lambdaQuery()
                    .eq(BuildingRatingEntity::getApproveUser, userCode)
                    .like(StringUtils.isNotBlank(name), BuildingRatingEntity::getBuildingName, name)
                    .eq(BuildingRatingEntity::getStatus, 0)
                    .orderByAsc(BuildingRatingEntity::getSubmitTime)
            );
        } else if (status.equals(1)) {
            // 我已审
            page = getBaseMapper().approveList(new Page<>(pageNum, pageSize), name, userCode);
        }
//        List<BuildingRatingEntity> page = this.list(Wrappers.<BuildingRatingEntity>lambdaQuery()
//                .eq(BuildingRatingEntity::getApproveUser, user.getUserCode())
//                .like(StringUtils.isNotBlank(name), BuildingRatingEntity::getBuildingName, name)
//                .eq(BuildingRatingEntity::getStatus, 0)
//                .orderByDesc(BuildingRatingEntity::getProjectLevel)
//                .orderByAsc(BuildingRatingEntity::getSubmitTime)
//        );

        List<Map<String, Object>> result = new ArrayList<>();
        if (page != null && CollectionUtils.isNotEmpty(page.getRecords())) {
            // 转换地址
            page.getRecords().forEach(e -> e.setMapAddress(rsaExample.decryptByPrivate(e.getMapAddress())));
            List<String> userCodes = new ArrayList<>();
            result = JsonUtils.fromListMap(JsonUtils.toJson(page.getRecords()));
            page.getRecords()
                    .forEach(ele -> userCodes.addAll(Arrays.asList(ele.getApproveUser(), ele.getSubmitUser(), ele.getRejectUser())));
            Map<String, SysUserDto> nameMaps = sysUserService.getNameMaps(userCodes);
            for (Map<String, Object> map : result) {
                SysUserDto approveUser = nameMaps.get(Converts.toStr(map.get("approveUser")));
                if (Objects.nonNull(approveUser)) {
                    map.put("approveUserName", approveUser.getRealName());
                    map.put("approveUserType", approveUser.getUserType());
                    map.put("approveBelongCode", approveUser.getBelongCode());
                    map.put("approveBelongName", approveUser.getBelongName());
                }
                SysUserDto submitUser = nameMaps.get(Converts.toStr(map.get("submitUser")));
                if (Objects.nonNull(submitUser)) {
                    map.put("submitUserName", submitUser.getRealName());
                    map.put("submitUserType", submitUser.getUserType());
                    map.put("submitBelongCode", submitUser.getBelongCode());
                    map.put("submitBelongName", submitUser.getBelongName());
                }
                SysUserDto rejectUser = nameMaps.get(Converts.toStr(map.get("rejectUser")));
                if (Objects.nonNull(rejectUser)) {
                    map.put("rejectUserName", rejectUser.getRealName());
                    map.put("rejectUserType", rejectUser.getUserType());
                    map.put("rejectBelongCode", rejectUser.getBelongCode());
                    map.put("rejectBelongName", rejectUser.getBelongName());
                }
            }
        }
        Map<String, Object> res = new HashMap<>();
        page.setRecords(null);
        res.put("page", page);
        res.put("result", result);
        return res;
    }

    /**
     * 判断下拉值
     *
     * @param resultInput  输入的值
     * @param type         1 楼层数    2 楼龄  3 月租金
     * @param adCode       月租金区域编码
     * @param buildingType 楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区',
     * @return
     */
    public int getEnum(String resultInput, Integer type, String adCode, Integer buildingType) {
        int enums = 0;
        if (buildingType == 0) {
            if (type == 1) {
                // 楼层数
                if (Integer.parseInt(resultInput) >= 30) {
                    enums = 10;
                } else if (Integer.parseInt(resultInput) >= 20) {
                    enums = 11;
                } else if (Integer.parseInt(resultInput) >= 15) {
                    enums = 12;
                } else {
                    enums = 13;
                }
            } else if (type == 2) {
                // 楼龄
                if (Integer.parseInt(resultInput) <= 5) {
                    enums = 20;
                } else if (Integer.parseInt(resultInput) > 5 && Integer.parseInt(resultInput) <= 10) {
                    enums = 21;
                } else if (Integer.parseInt(resultInput) > 10 && Integer.parseInt(resultInput) <= 15) {
                    enums = 22;
                } else if (Integer.parseInt(resultInput) >= 15) {
                    enums = 23;
                }
            } else if (type == 3) {
                // 月租金
                CityRentEntity cityRentEntity = cityRentService.getRent(adCode);
                // 如果找不到该城市默认0.7计算
                if (Objects.isNull(cityRentEntity)) {
                    enums = 17;
                } else {
                    BigDecimal rent = cityRentEntity.getOfficeRent();
                    BigDecimal bigDecimalValue = new BigDecimal(resultInput);
                    BigDecimal rentRate = bigDecimalValue.subtract(rent).divide(rent, 2, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100));
                    //（手写的租金-平均租金）/平均租金 > 30
                    if (rentRate.compareTo(BigDecimal.valueOf(30)) >= 0) {
                        enums = 15;
                    } else if (rentRate.compareTo(BigDecimal.valueOf(0)) > 0) {
                        enums = 16;
                    } else if (rentRate.compareTo(BigDecimal.valueOf(0)) < 0 && rentRate.compareTo(BigDecimal.valueOf(-30)) > 0) {
                        enums = 18;
                    } else {
                        enums = 17;
                    }
                }
            }
        } else if (buildingType == 1) {
            if (type == 1) {
                // 楼层数
                if (Integer.parseInt(resultInput) >= 30) {
                    enums = 45;
                } else if (Integer.parseInt(resultInput) >= 20) {
                    enums = 46;
                } else if (Integer.parseInt(resultInput) >= 15) {
                    enums = 47;
                } else {
                    enums = 48;
                }
            } else if (type == 2) {
                // 楼龄
                if (Integer.parseInt(resultInput) <= 5) {
                    enums = 37;
                } else if (Integer.parseInt(resultInput) > 5 && Integer.parseInt(resultInput) <= 10) {
                    enums = 38;
                } else if (Integer.parseInt(resultInput) > 10 && Integer.parseInt(resultInput) <= 15) {
                    enums = 39;
                } else if (Integer.parseInt(resultInput) >= 15) {
                    enums = 40;
                }
            } else if (type == 3) {
                // 月租金
                CityRentEntity cityRentEntity = cityRentService.getRent(adCode);
                if (Objects.isNull(cityRentEntity)) {
                    enums = 52;
                } else {
                    BigDecimal rent = cityRentEntity.getResidRent();
                    BigDecimal bigDecimalValue = new BigDecimal(resultInput);
                    BigDecimal rentRate = bigDecimalValue.subtract(rent).divide(rent, 2, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100));
                    //（手写的租金-平均租金）/平均租金 > 30
                    if (rentRate.compareTo(BigDecimal.valueOf(30)) >= 0) {
                        enums = 50;
                    } else if (rentRate.compareTo(BigDecimal.valueOf(0)) > 0) {
                        enums = 51;
                    } else if (rentRate.compareTo(BigDecimal.valueOf(0)) < 0 && rentRate.compareTo(BigDecimal.valueOf(-30)) > 0) {
                        enums = 53;
                    } else {
                        enums = 52;
                    }
                }
            }
        } else if (buildingType == 3) {
            if (type == 1) {
                // 楼层数
                if (Integer.parseInt(resultInput) >= 30) {
                    enums = 87;
                } else if (Integer.parseInt(resultInput) >= 20) {
                    enums = 88;
                } else if (Integer.parseInt(resultInput) >= 15) {
                    enums = 89;
                } else {
                    enums = 90;
                }
            } else if (type == 2) {
                // 楼龄
                if (Integer.parseInt(resultInput) <= 5) {
                    enums = 79;
                } else if (Integer.parseInt(resultInput) > 5 && Integer.parseInt(resultInput) <= 10) {
                    enums = 80;
                } else if (Integer.parseInt(resultInput) > 10 && Integer.parseInt(resultInput) <= 15) {
                    enums = 81;
                } else if (Integer.parseInt(resultInput) >= 15) {
                    enums = 82;
                }
            }
        }
        return enums;

    }

    /**
     * 楼宇数据客户商机处理
     *
     * @param entity
     */
    private void createDefaultBusiness(BuildingRatingEntity entity) {
        try {

            log.info("[handlerBuildingRating][处理楼宇数据,id:{},username:{},buildingId:{}]", UserThreadLocal.getUserId(),
                    UserThreadLocal.getUser().getName(), entity.getId());
            BuildingRatingEntity buildingRating = buildingRatingService.getById(entity.getId());
            // 商机名
            String businessName = buildingRating.getBuildingName() + "-默认";

            // 保存商机
            int countNum = (int) businessOpportunityService.count(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                    .eq(BusinessOpportunityEntity::getBuildingNo, buildingRating.getBuildingNo()));
            BusinessOpportunityEntity businessOpportunityEntity = new BusinessOpportunityEntity();
            businessOpportunityEntity.setName(businessName);
            businessOpportunityEntity.setCode(businessCode(buildingRating.getBuildingNo(), countNum));
            businessOpportunityEntity.setBuildingNo(buildingRating.getBuildingNo());
            businessOpportunityEntity.setOwner(entity.getSubmitUser());
            businessOpportunityEntity.setSubmitUser(entity.getSubmitUser());
            businessOpportunityEntity.setStatus(BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode());
            businessOpportunityService.save(businessOpportunityEntity);

            // 发商机状态变更消息
            CachedUser cachedUser = UserThreadLocal.getUser();
            BusinessStatusChangeVO businessStatusChangeVO = new BusinessStatusChangeVO();
            businessStatusChangeVO.setBusinessCode(businessOpportunityEntity.getCode());
            businessStatusChangeVO.setStatus(businessOpportunityEntity.getStatus());
            businessStatusChangeVO.setOperatorId(cachedUser.getId());
            businessStatusChangeVO.setOperatorWno(cachedUser.getWno());
            businessStatusChangeVO.setOperatorName(cachedUser.getName());
            kafkaProducerService.sendMessageAfterCommit(TopicConstants.BUSINESS_STATUS_CHANGE, JSON.toJSONString(businessStatusChangeVO));
        } catch (Exception e) {
            log.error("[approve][处理客户商机失败]", e);
            throw new RuntimeException(e);

        }
    }

    /**
     * 商机编码
     */

    private String businessCode(String buildingNo, int num) {
        int number = num + 1;
        return buildingNo + "-" + number;
    }

    @Deprecated
    @Override
    public BuildingTrialCalculateVO calculate(RatingCalculateDto dto) {
        String userCode = UserThreadLocal.getUser().getWno();
        if (StringUtils.isBlank(userCode)) {
            throw new ServerException("当前登录人人员信息获取失败");
        }

        CopyOptions copyOptions = new CopyOptions();
        copyOptions.setIgnoreNullValue(true); // 只复制非空值

        // 处理前端提交的参数项
        BuildingDetailsEntity details = new BuildingDetailsEntity();
        BeanUtil.copyProperties(dto, details, copyOptions);
        List<BuildingRatingEntity> ratingEntityList = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getMapNo, dto.getMapNo())
                .eq(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.REJECTED.getValue())
                .orderByDesc(BuildingRatingEntity::getCreateTime)
                .list();
        int tempDataFlag = dataFlag;
        if (CollectionUtils.isNotEmpty(ratingEntityList)
                && ratingEntityList.get(0).getDataFlag() != null) {
            tempDataFlag = ratingEntityList.get(0).getDataFlag();
        }

        // 获取所有评分参数
        List<BuildingParameterEntity> parameterList = buildingParameterService.list(Wrappers.<BuildingParameterEntity>lambdaQuery()
                .eq(BuildingParameterEntity::getDataFlag, tempDataFlag)
                .ne(BuildingParameterEntity::getParentId, 0)
                .eq(BuildingParameterEntity::getBuildingType, dto.getBuildingType()));
        Map<String, List<BuildingParameterEntity>> parameterMap = parameterList.stream()
                .collect(Collectors.groupingBy(BuildingParameterEntity::getParameterCode));

        // 计算手动输入的参数项：楼层，月租金，楼龄
        processNumberMatch(details, "buildingNumber", parameterMap);
        processRentMatch(details, "buildingPrice", parameterMap, dto.getMapAdCode(), dto.getBuildingType());
        processNumberMatch(details, "buildingAge", parameterMap);

        // 计算总分
        BigDecimal coefficient = getCityCoefficient(dto.getMapAdCode());
        BigDecimal score = calculateScoreByBuildingType(details, dto.getMapAdCode(), dto.getBuildingType(), tempDataFlag);
        String projectLevel = calculateProjectLevel2(score);

        // 构建计算结果DTO
        CalculateResultDTO resultDTO = new CalculateResultDTO();
        BeanUtil.copyProperties(details, resultDTO, copyOptions);

        // 获取AI评估数据
        Map<String, Object> aiData = douAiService.getBuildingAppraiser(
                String.format("%s%s", dto.getMapCity(), dto.getBuildingName()));

        // 初始化AI总分及评级
        BigDecimal aiScore = BigDecimal.ZERO;
        String aiProjectLevel = RatingConstants.RATING_LEVEL_BELOW_A;
        String buildingType = BuildingRatingEntity.BuildingType.getNameByValue(dto.getBuildingType());
        String thirdBuildingType = null != aiData ? aiData.getOrDefault("thirdBuildingType", "").toString() : "";
        if (buildingType.equals(thirdBuildingType)) {
            // 处理AI数据
            setAiData(aiData, details);
            // 计算AI评分
            details = processThirdPartyData(details, dto.getBuildingType(), dto.getMapAdCode(), tempDataFlag);
            // 计算AI总分
            aiScore = calculateAIScoreByBuildingType(details, dto.getBuildingType(), coefficient);
            // 计算AI评级
            aiProjectLevel = calculateProjectLevel2(aiScore);
        } else {
            details.setThirdBuildingType(thirdBuildingType);
        }

        BeanUtil.copyProperties(details, resultDTO, copyOptions);

        resultDTO.setBuildingScore(score.setScale(2, RoundingMode.HALF_UP));
        resultDTO.setProjectLevel(projectLevel);
        resultDTO.setBuildingAiScore(aiScore.setScale(2, RoundingMode.HALF_UP));
        resultDTO.setProjectAiLevel(aiProjectLevel);

        // 构建返回VO
        BuildingTrialCalculateVO vo = new BuildingTrialCalculateVO();
        vo.setBuildingAiScore(score.setScale(2, RoundingMode.HALF_UP).toString());
        vo.setProjectAiLevel(projectLevel);

        // 设置基本信息
        vo.setBasicInfos(buildBasicInfos(details, dto));
        vo.setQualityInfos(buildQualityInfos(details, dto));
        vo.setFloorScores(calculateFloorScores(score, coefficient, resultDTO));

        // 缓存计算结果
        cacheCalculateResult(dto.getMapNo(), resultDTO);

        return vo;
    }

    /**
     * 人工评级方法
     *
     * @param param   评级申请参数
     * @param details 楼宇详情
     * @return 评级结果
     */
    @Override
    public CalculateResultDTO manualRating(RatingApplyDto param, BuildingDetailsEntity details) {
        // 获取所有评分参数
        List<BuildingParameterEntity> rules = buildingParameterService.list(Wrappers.<BuildingParameterEntity>lambdaQuery()
                .eq(BuildingParameterEntity::getDataFlag, dataFlag)
                .ne(BuildingParameterEntity::getParentId, 0)
                .eq(BuildingParameterEntity::getBuildingType, param.getBuildingType()));

        Map<String, List<BuildingParameterEntity>> ruleCodeMapping = rules.stream()
                .collect(Collectors.groupingBy(BuildingParameterEntity::getParameterCode));

        // 楼层匹配处理
        processNumberMatch(details, "buildingNumber", ruleCodeMapping);
        // 楼龄匹配处理
        processNumberMatch(details, "buildingAge", ruleCodeMapping);
        // 日租金匹配处理
        processDailyRentMatch(details, ruleCodeMapping, param.getMapAdCode(), param.getBuildingType());
        // 评级项文本匹配
        processTextMatch(details, ruleCodeMapping);

        // 计算总分
        BigDecimal score = calculateScoreByBuildingType(details, param.getMapAdCode(), param.getBuildingType(), dataFlag);
        String projectLevel = calculateProjectLevel2(score);

        // 构建返回结果
        CalculateResultDTO resultDTO = new CalculateResultDTO();
        resultDTO.setBuildingScore(score.setScale(2, RoundingMode.HALF_UP));
        resultDTO.setProjectLevel(projectLevel);
        calculateFloorScores(score, getCityCoefficient(param.getMapAdCode()), resultDTO);

        return resultDTO;
    }

    private void processTextMatch(BuildingDetailsEntity detailEntity, Map<String, List<BuildingParameterEntity>> ruleCodeMapping) {
        ruleCodeMapping.forEach((code, rules) -> {
            if (Arrays.asList("buildingNumber", "buildingPrice", "buildingAge").contains(code)) {
                // 已特殊处理的评分项跳过
                return;
            }

            String textName = detailEntity.getValue(code);

            if (StrUtil.isBlank(textName)) {
                return;
            }

            rules.stream()
                    .filter(rule -> rule.getParameterName().equals(textName))
                    .findFirst()
                    .ifPresent(rule -> detailEntity.setValueId(code, rule.getId()));
        });
    }

    public void processDailyRentMatch(BuildingDetailsEntity details, Map<String, List<BuildingParameterEntity>> ruleMap,
                                      String mapAdCode, Integer buildingType) {
        BigDecimal dailyPrice = details.getDailyPriceInput();
        if (Objects.isNull(dailyPrice)) {
            return;
        }

        String code = "buildingPrice";

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        CityRentEntity cityRent = cityRentService.getRent(mapAdCode);
        if (Objects.isNull(cityRent)) {
            if (buildingType.equals(0)) {
                rules.stream()
                        .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(6)) == 0)
                        .findFirst()
                        .ifPresent(rule -> details.setValueId(code + "id", rule.getId()));
                return;
            } else {
                rules.stream()
                        .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(7)) == 0)
                        .findFirst()
                        .ifPresent(rule -> details.setValueId(code + "id", rule.getId()));
                return;
            }

        }

        try {
            BigDecimal avgRent;
            if (buildingType.equals(0)) {
                BuildingCityRentEntity buildingCityRentEntity = buildingCityRentService.lambdaQuery()
                        .eq(BuildingCityRentEntity::getAdCode, mapAdCode)
                        .one();
                if (Objects.nonNull(buildingCityRentEntity)) {
                    avgRent = buildingCityRentEntity.getOfficeRentDaily();
                } else {
                    rules.stream()
                            .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(6)) == 0)
                            .findFirst()
                            .ifPresent(rule -> details.setValueId(code + "id", rule.getId()));
                    return;
                }
            } else {
                avgRent = cityRent.getOfficeRentDaily();
            }

            rules.stream()
                    .filter(rule -> matchRentRule(rule.getParameterRule(), dailyPrice, avgRent))
                    .findFirst()
                    .ifPresent(rule -> details.setValueId(code + "id", rule.getId()));
        } catch (NumberFormatException e) {
            log.warn("日租金格式转换异常: {}", dailyPrice, e);
        }
    }

    public void processAiDailyRentMatch(BuildingDetailsEntity details, Map<String, List<BuildingParameterEntity>> ruleMap,
                                        String mapAdCode, Integer buildingType) {
        // 优先使用中国房价行情网日租金
        String value = Objects.nonNull(details.getCreDailyPrice())
                ? details.getCreDailyPrice().toString() : details.getThirdDailyPrice();

        if (aiBaseScoreProperties.isInvalid(value)) {
            log.info("AI数据（{}）无效，设置基础分", value);
            details.setThirdBuildingPriceId(aiBaseScoreProperties.getLatestScore().id());
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get("buildingPrice");
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        CityRentEntity cityRent = cityRentService.getRent(mapAdCode);
        if (Objects.isNull(cityRent)) {
            if (buildingType.equals(0)) {
                rules.stream()
                        .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(6)) == 0)
                        .findFirst()
                        .ifPresent(rule -> details.setThirdBuildingPriceId(rule.getId()));
                return;
            } else {
                rules.stream()
                        .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(7)) == 0)
                        .findFirst()
                        .ifPresent(rule -> details.setThirdBuildingPriceId(rule.getId()));
                return;
            }

        }

        try {
            BigDecimal price = new BigDecimal(value.replaceAll("[^0-9\\.]", ""));
            BigDecimal avgRent;
            if (buildingType.equals(0)) {
                BuildingCityRentEntity buildingCityRentEntity = buildingCityRentService.lambdaQuery()
                        .eq(BuildingCityRentEntity::getAdCode, mapAdCode)
                        .one();
                if (Objects.nonNull(buildingCityRentEntity)) {
                    avgRent = buildingCityRentEntity.getOfficeRentDaily();
                } else {
                    rules.stream()
                            .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(6)) == 0)
                            .findFirst()
                            .ifPresent(rule -> details.setThirdBuildingPriceId(rule.getId()));
                    return;
                }
            } else {
                avgRent = cityRent.getOfficeRentDaily();
            }

            rules.stream()
                    .filter(rule -> matchRentRule(rule.getParameterRule(), price, avgRent))
                    .findFirst()
                    .ifPresent(rule -> details.setThirdBuildingPriceId(rule.getId()));
        } catch (NumberFormatException e) {
            log.warn("日租金格式转换异常，设置基础分: {}", value, e);
            details.setThirdBuildingPriceId(aiBaseScoreProperties.getLatestScore().id());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleAiRating(Boolean isSubmit, String buildingNo, String operatorWno) {
        log.info("开始处理AI评级: {}", buildingNo);

        if (StrUtil.isBlank(buildingNo)) {
            log.warn("处理AI评级，参数buildingNo为空");
            return;
        }

        BuildingRatingEntity ratingEntity = lambdaQuery()
                .select(BuildingRatingEntity::getId,
                        BuildingRatingEntity::getBuildingNo,
                        BuildingRatingEntity::getRatingVersion,
                        BuildingRatingEntity::getBuildingName,
                        BuildingRatingEntity::getBuildingType,
                        BuildingRatingEntity::getUpdateTime,
                        BuildingRatingEntity::getStatus,
                        BuildingRatingEntity::getMapCity,
                        BuildingRatingEntity::getMapAdCode,
                        BuildingRatingEntity::getMapRegion,
                        BuildingRatingEntity::getMapAddress,
                        BuildingRatingEntity::getMapLatitude,
                        BuildingRatingEntity::getMapLongitude)
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo)
                .last("limit 1")
                .one();
        if (Objects.isNull(ratingEntity)) {
            log.warn("楼宇评级不存在: {}", buildingNo);
            return;
        }

        if (isSubmit ? BuildingRatingEntity.Status.WAIT_AUDIT.getValue() != ratingEntity.getStatus()
                : BuildingRatingEntity.Status.AUDITED.getValue() != ratingEntity.getStatus()) {
            log.warn("楼宇评级状态不符合，buildingNo：{}，status：{}", buildingNo,
                    BuildingRatingEntity.Status.getNameByValue(ratingEntity.getStatus()));
            return;
        }

        BuildingDetailsEntity detailEntity = buildingDetailsService.lambdaQuery()
                .eq(BuildingDetailsEntity::getBuildingNo, buildingNo)
                .last("limit 1")
                .one();
        if (Objects.isNull(detailEntity)) {
            log.warn("楼宇详情不存在: {}", buildingNo);
            return;
        }

        // 清空AI数据
        detailEntity.emptyAiData();

        // AI评级，填充评级数据到detailsEntity
        RatingApplyDto ratingDto = new RatingApplyDto()
                .setBuildingNo(buildingNo)
                .setMapCity(ratingEntity.getMapCity())
                .setBuildingName(ratingEntity.getBuildingName())
                .setBuildingType(ratingEntity.getBuildingType())
                .setMapAdCode(ratingEntity.getMapAdCode())
                .setMapRegion(ratingEntity.getMapRegion());
        try {
            ratingDto.setMapLatitude(rsaExample.decryptByPrivate(ratingEntity.getMapLatitude()))
                    .setMapLongitude(rsaExample.decryptByPrivate(ratingEntity.getMapLongitude()))
                    .setMapAddress(rsaExample.decryptByPrivate(ratingEntity.getMapAddress()));
        } catch (Exception e) {
            log.warn("AI楼宇评级，解密数据异常，{}", buildingNo, e);
        }

        CalculateResultDTO result = aiRating(ratingDto, detailEntity);

        if (Objects.nonNull(result)) {
            // 设置AI评分，评级
            ratingEntity.setBuildingAiScore(result.getBuildingAiScore());
            ratingEntity.setProjectAiLevel(result.getProjectAiLevel());
        } else {
            // 置空AI评分，评级
            ratingEntity.setBuildingAiScore(BigDecimal.ZERO);
            ratingEntity.setProjectAiLevel("");
        }
        ratingEntity.setUpdateTime(ratingEntity.getUpdateTime());
        updateById(ratingEntity);

        // 回填AI评分到楼宇元数据
        buildingMetaService.fillAiData(ratingEntity.getBuildingNo(), ratingEntity.getBuildingAiScore(), ratingEntity.getProjectAiLevel());

        // 回填AI评级数据，楼宇类型
        buildingDetailsService.updateById(detailEntity);
        if (Objects.isNull(result) || Objects.isNull(detailEntity.getCreDailyPrice())) {
            // 置空中国房价行情网日租金
            LambdaUpdateWrapper<BuildingDetailsEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BuildingDetailsEntity::getId, detailEntity.getId());
            updateWrapper.set(BuildingDetailsEntity::getCreDailyPrice, null);
            buildingDetailsService.update(updateWrapper);
        }

        if (isSubmit) {
            // 提交审核
            submitApproval(ratingEntity, operatorWno);
            log.info("提交审核成功: {}", buildingNo);
        }

        log.info("处理AI评级完成: {}", buildingNo);
    }

    /**
     * AI评级方法
     *
     * @param param   评级申请参数
     * @param details 楼宇详情
     * @return 评级结果
     */
    @Override
    public CalculateResultDTO aiRating(RatingApplyDto param, BuildingDetailsEntity details) {
        // AI接口参数
        String aiParam = String.format("%s %s %s %s",
                param.getMapCity(), param.getMapRegion(), param.getBuildingName(), param.getMapAddress());
        // 获取AI楼宇类型
        List<String> aiBuildingTypes = douAiService.getBuildingTypes(aiParam);

        // 认证楼宇类型
        String buildingType = BuildingRatingEntity.BuildingType.getNameByValue(param.getBuildingType());

        if (CollUtil.isNotEmpty(aiBuildingTypes)) {
            // 设置AI楼宇类型
            details.setThirdBuildingType(String.join(",", aiBuildingTypes));
        }

        if (CollUtil.isEmpty(aiBuildingTypes) || !aiBuildingTypes.contains(buildingType)) {
            log.warn("AI楼宇类型{}与认证楼宇类型{}不匹配", aiBuildingTypes, buildingType);
            return null;
        }

        // 获取AI评级数据
        Map<String, Object> aiData = douAiService.getBuildingAppraiser(aiParam);

        if (CollUtil.isEmpty(aiData)) {
            log.warn("获取AI评级数据为空");
            return null;
        }

        // 设置AI数据
        setAiData(aiData, details);

        // 设置地理位置
        setLocation(param, details);

        // 设置中国房价行情网日租金
        setCrePrice(param, details);

        // 计算AI评分
        processThirdPartyData(details, param.getBuildingType(), param.getMapAdCode(), dataFlag);

        // 城市系数
        BigDecimal coefficient = getCityCoefficient(param.getMapAdCode());

        // 计算AI总分
        BigDecimal aiScore = calculateAIScoreByBuildingType(details, param.getBuildingType(), coefficient);

        // 计算AI评级
        String aiProjectLevel = calculateProjectLevel2(aiScore);

        // 构建返回结果
        CalculateResultDTO resultDTO = new CalculateResultDTO();
        resultDTO.setBuildingAiScore(aiScore.setScale(2, RoundingMode.HALF_UP));
        resultDTO.setProjectAiLevel(aiProjectLevel);

        return resultDTO;
    }

    private void setCrePrice(RatingApplyDto param, BuildingDetailsEntity details) {
        CreHousePriceRentEntity houseRentInfo = creHousePriceRentService.getHouseRentInfo(
                CreHousePriceRentRequest.builder()
                        .city(param.getMapCity())
                        .district(param.getMapRegion())
                        .location(param.getMapAddress())
                        .haName(param.getBuildingName())
                        .build());

        log.info("获取的中国房价行情网日租金，buildingNo：{}，{}", param.getBuildingNo(), JSON.toJSONString(houseRentInfo));

        details.setCreDailyPrice(Objects.isNull(houseRentInfo) ? null : houseRentInfo.getPrice());
    }

    private void setLocation(RatingApplyDto param, BuildingDetailsEntity details) {
        CoreAreaJudgeParam coreAreaJudgeParam = new CoreAreaJudgeParam();
        coreAreaJudgeParam.setLongitude(param.getMapLongitude());
        coreAreaJudgeParam.setLatitude(param.getMapLatitude());
        coreAreaJudgeParam.setCityName(param.getMapCity());
        log.info("核心区域判断参数：buildingNo：{}, param：{}", param.getBuildingNo(), JSON.toJSONString(coreAreaJudgeParam));
        ResultTemplate<Boolean> result;
        try {
            result = feignMehtWebRpc.judgeCoreArea(coreAreaJudgeParam);
            if (!IReturnCode.Default.SUCCESS.getErrCode().equals(result.getCode()) || !result.getSuccess()) {
                log.warn("核心区域判断失败，buildingNo：{}，result：{}", param.getBuildingNo(), JSON.toJSONString(result));
                return;
            }
            Boolean isCoreArea = result.getData();
            log.info("核心区域判断结果：{}，buildingNo：{}", isCoreArea, param.getBuildingNo());
            details.setThirdBuildingLocation(isCoreArea ? RatingConstants.CORE_AREA_YES : RatingConstants.CORE_AREA_NO);
        } catch (Exception e) {
            log.error("核心区域判断异常", e);
        }
    }

    @Deprecated
    @Override
    public BuildingTrialCalculateVO manualCalculate(RatingCalculateManualDto dto) {
        String userCode = UserThreadLocal.getUser().getWno();
        if (StringUtils.isBlank(userCode)) {
            throw new ServerException("当前登录人人员信息获取失败");
        }

        CopyOptions copyOptions = new CopyOptions();
        copyOptions.setIgnoreNullValue(true); // 只复制非空值

        // 处理前端提交的参数项
        BuildingDetailsEntity details = new BuildingDetailsEntity();
        BeanUtil.copyProperties(dto, details, copyOptions);

        BuildingRatingEntity buildingRating = this.lambdaQuery()
                .select(BuildingRatingEntity::getBuildingNo, BuildingRatingEntity::getDataFlag
                        , BuildingRatingEntity::getSmallScreenRatingFlag
                        , BuildingRatingEntity::getBuildingScore
                        , BuildingRatingEntity::getProjectLevel)
                .eq(BuildingRatingEntity::getBuildingNo, dto.getBuildingNo())
                .one();

        Integer finalDataFlag = buildingRating.getDataFlag();
        if (!buildingRating.getSmallScreenRatingFlag().equals(1)) {
            finalDataFlag = dataFlag;
        }

        log.info("新finalDataFlag:{}", finalDataFlag);
        // 获取所有评分参数
        List<BuildingParameterEntity> parameterList = buildingParameterService.list(Wrappers.<BuildingParameterEntity>lambdaQuery()
                .eq(BuildingParameterEntity::getDataFlag, finalDataFlag)
                .ne(BuildingParameterEntity::getParentId, 0)
                .eq(BuildingParameterEntity::getBuildingType, dto.getBuildingType()));
        Map<String, List<BuildingParameterEntity>> parameterMap = parameterList.stream()
                .collect(Collectors.groupingBy(BuildingParameterEntity::getParameterCode));

        // 计算手动输入的参数项：楼层，月租金，楼龄
        if (buildingRating.getSmallScreenRatingFlag().equals(0)) {
            processNumberMatch(details, "buildingNumber", parameterMap);
            processRentMatch(details, "buildingPrice", parameterMap, dto.getMapAdCode(), dto.getBuildingType());
            processNumberMatch(details, "buildingAge", parameterMap);
        }

        // 计算总分
        BigDecimal coefficient = getCityCoefficient(dto.getMapAdCode());
        BigDecimal score = calculateScoreByBuildingType(details, dto.getMapAdCode(), dto.getBuildingType(), finalDataFlag);
        String projectLevel = calculateProjectLevel2(score);
        if (buildingRating.getSmallScreenRatingFlag().equals(1)) {
            score = buildingRating.getBuildingScore();
            projectLevel = buildingRating.getProjectLevel();
        }

        // 构建计算结果DTO
        CalculateResultDTO resultDTO = new CalculateResultDTO();
        BeanUtil.copyProperties(details, resultDTO, copyOptions);

        resultDTO.setBuildingScore(score.setScale(2, RoundingMode.HALF_UP));
        resultDTO.setProjectLevel(projectLevel);

        // 构建返回VO
        BuildingTrialCalculateVO vo = new BuildingTrialCalculateVO();
        vo.setBuildingAiScore(score.setScale(2, RoundingMode.HALF_UP).toString());
        vo.setProjectAiLevel(projectLevel);
        vo.setBasicInfos(buildBasicInfos(details, dto));
        vo.setQualityInfos(buildQualityInfos(details, dto));
        vo.setFloorScores(calculateFloorScores(score, coefficient, resultDTO));

        // 缓存计算结果
        cacheCalculateResult(dto.getMapNo(), resultDTO);

        log.info("手动计算结果vo:{}，结果{}", JsonUtils.toJson(vo), JsonUtils.toJson(resultDTO));

        return vo;
    }

    public void setAiData(Map<String, Object> aiData, BuildingDetailsEntity details) {
        if (CollUtil.isEmpty(aiData)) {
            return;
        }

        details.setThirdBuildingGrade(aiData.getOrDefault("thirdBuildingGrade", "").toString());
        details.setThirdBuildingLocation(aiData.getOrDefault("thirdBuildingLocation", "").toString());
        details.setThirdBuildingNumber(aiData.getOrDefault("thirdBuildingNumber", "").toString());
        details.setThirdBuildingAge(aiData.getOrDefault("thirdBuildingAge", "").toString());
        details.setThirdBuildingExterior(aiData.getOrDefault("thirdBuildingExterior", "").toString());
        details.setThirdBuildingLobby(aiData.getOrDefault("thirdBuildingLobby", "").toString());
        details.setThirdBuildingGarage(aiData.getOrDefault("thirdBuildingGarage", "").toString());
        details.setThirdBuildingBrand(aiData.getOrDefault("thirdBuildingBrand", "").toString());
        details.setThirdBuildingRating(aiData.getOrDefault("thirdBuildingRate", "").toString());
        details.setThirdDailyPrice(aiData.getOrDefault("thirdDailyPrice", "").toString());
        details.setThirdDeliveryDate(aiData.getOrDefault("thirdDeliveryDate", "").toString());

        details.setThirdBuildingHall(aiData.getOrDefault("thirdBuildingHall", "").toString());
        details.setThirdBuildingSettled(aiData.getOrDefault("thirdBuildingSettled", "").toString());

        // 交付日期换算楼龄
        if (StrUtil.isNotBlank(details.getThirdDeliveryDate())) {
            log.info("AI交付日期:{}", details.getThirdDeliveryDate());
            Pattern pattern = Pattern.compile("(\\d{4})-(\\d{2})");
            Matcher matcher = pattern.matcher(details.getThirdDeliveryDate());
            if (matcher.find()) {
                // 拼接日期
                String thirdDeliveryDateStr = matcher.group() + "-01";
                try {
                    LocalDate thirdDeliveryDate = LocalDate.parse(thirdDeliveryDateStr);
                    int years = buildingDetailsService.getYears(thirdDeliveryDate);
                    details.setThirdBuildingAge(String.valueOf(years));
                } catch (Exception e) {
                    log.error("解析AI交付日期{}异常", thirdDeliveryDateStr, e);
                }
            }
        }
    }

    private List<BuildingTrialCalculateVO.InfoItem> buildBasicInfos(BuildingDetailsEntity details,
                                                                    RatingCalculateDto dto) {
        List<BuildingTrialCalculateVO.InfoItem> basicInfos = new ArrayList<>();
        switch (dto.getBuildingType()) {
            case 0:
                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("grade")
                        .setLabel("写字楼等级")
                        .setValue(dto.getBuildingGradeName()));
                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("location")
                        .setLabel("地理等级")
                        .setValue(dto.getBuildingLocationName()));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("floor")
                        .setLabel("楼层数")
                        .setValue(String.valueOf(dto.getBuildingNumberInput())));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("price")
                        .setLabel("月租金")
                        .setValue(String.valueOf(dto.getBuildingPriceInput())));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("age")
                        .setLabel("楼龄")
                        .setValue(String.valueOf(dto.getBuildingAgeInput())));
                break;
            case 1:
                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("location")
                        .setLabel("地理等级")
                        .setValue(dto.getBuildingLocationName()));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("floor")
                        .setLabel("楼层数")
                        .setValue(String.valueOf(dto.getBuildingNumberInput())));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("price")
                        .setLabel("月租金")
                        .setValue(String.valueOf(dto.getBuildingPriceInput())));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("age")
                        .setLabel("楼龄")
                        .setValue(String.valueOf(dto.getBuildingAgeInput())));
                break;
            case 2:
                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("brand")
                        .setLabel("综合体品牌")
                        .setValue(dto.getBuildingBrandName()));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("location")
                        .setLabel("地理等级")
                        .setValue(dto.getBuildingLocationName()));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("rating")
                        .setLabel("点评评分")
                        .setValue(dto.getBuildingRatingName()));
                break;
            case 3:
                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("location")
                        .setLabel("地理等级")
                        .setValue(dto.getBuildingLocationName()));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("floor")
                        .setLabel("楼层数")
                        .setValue(String.valueOf(dto.getBuildingNumberInput())));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("age")
                        .setLabel("楼龄")
                        .setValue(String.valueOf(dto.getBuildingAgeInput())));

                basicInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("settled")
                        .setLabel("入驻率")
                        .setValue(String.valueOf(dto.getBuildingSettledName())));
                break;
        }

        return basicInfos;
    }

    private List<BuildingTrialCalculateVO.InfoItem> buildQualityInfos(BuildingDetailsEntity details,
                                                                      RatingCalculateDto dto) {
        List<BuildingTrialCalculateVO.InfoItem> qualityInfos = new ArrayList<>();
        switch (dto.getBuildingType()) {
            case 0:
                qualityInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("exterior")
                        .setLabel("外墙材料")
                        .setValue(dto.getBuildingExteriorName()));

                qualityInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("lobby")
                        .setLabel("大堂高度及装修")
                        .setValue(dto.getBuildingLobbyName()));

                qualityInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("garage")
                        .setLabel("地下车库")
                        .setValue(dto.getBuildingGarageName()));
                break;
            case 1:
                qualityInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("exterior")
                        .setLabel("外墙材料")
                        .setValue(dto.getBuildingExteriorName()));

                qualityInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("lobby")
                        .setLabel("候梯厅装修")
                        .setValue(dto.getBuildingHallName()));

                qualityInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("garage")
                        .setLabel("地下车库")
                        .setValue(dto.getBuildingGarageName()));
                break;
            case 2:
                break;
            case 3:
                qualityInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("exterior")
                        .setLabel("外墙材料")
                        .setValue(dto.getBuildingExteriorName()));

                qualityInfos.add(new BuildingTrialCalculateVO.InfoItem()
                        .setKey("hall")
                        .setLabel("候梯厅装修")
                        .setValue(dto.getBuildingHallName()));

                break;
        }

        return qualityInfos;
    }

    private List<BuildingTrialCalculateVO.InfoItem> calculateFloorScores(BigDecimal aiScore, BigDecimal coefficient, CalculateResultDTO resultDTO) {
        List<BuildingTrialCalculateVO.InfoItem> floorScores = new ArrayList<>();

        // 获取配置
        Map<String, BigDecimal> configMap = getFloorScoreConfig();
        BigDecimal unqualifiedScore = configMap.getOrDefault("floor_unqualified_score", BigDecimal.ZERO);

        // 计算各楼层分数
        BigDecimal firstFloorExclusiveScore = aiScore.multiply(configMap.get("firstFloorExclusive"))
                .multiply(coefficient);
        resultDTO.setFirstFloorExclusiveScore(firstFloorExclusiveScore);
        addFloorScore(floorScores, "一楼独享", "floor1-invididual",
                firstFloorExclusiveScore, unqualifiedScore);

        BigDecimal firstFloorShareScore = aiScore.multiply(configMap.get("firstFloorShare")).multiply(coefficient);
        resultDTO.setFirstFloorShareScore(firstFloorShareScore);
        addFloorScore(floorScores, "一楼共享", "floor1-shared",
                firstFloorShareScore, unqualifiedScore);

        BigDecimal negativeFirstFloorScore = aiScore.multiply(configMap.get("negativeFirstFloor"))
                .multiply(coefficient);
        resultDTO.setNegativeFirstFloorScore(negativeFirstFloorScore);
        addFloorScore(floorScores, "负一楼", "floor-1",
                negativeFirstFloorScore, unqualifiedScore);

        BigDecimal negativeTwoFloorScore = aiScore.multiply(configMap.get("negativeTwoFloor")).multiply(coefficient);
        resultDTO.setNegativeTwoFloorScore(negativeTwoFloorScore);
        addFloorScore(floorScores, "负二楼", "floor-2",
                negativeTwoFloorScore, unqualifiedScore);

        BigDecimal twoFloorAboveScore = aiScore.multiply(configMap.get("twoFloorAbove")).multiply(coefficient);
        resultDTO.setTwoFloorAboveScore(twoFloorAboveScore);
        addFloorScore(floorScores, "二楼及以上", "floor2-above",
                twoFloorAboveScore, unqualifiedScore);

        BigDecimal thirdFloorBelowScore = aiScore.multiply(configMap.get("thirdFloorBelow")).multiply(coefficient);
        resultDTO.setThirdFloorBelowScore(thirdFloorBelowScore);
        addFloorScore(floorScores, "负三楼及以下", "floor-3-below",
                thirdFloorBelowScore, unqualifiedScore);


        return floorScores;
    }

    private void addFloorScore(List<BuildingTrialCalculateVO.InfoItem> scores, String label, String key,
                               BigDecimal score, BigDecimal unqualifiedScore) {

        BuildingTrialCalculateVO.InfoItem item = new BuildingTrialCalculateVO.InfoItem()
                .setKey(key)
                .setLabel(label)
                .setValue(String.format("%.2f分", score));

        if (score.compareTo(unqualifiedScore) < 0) {
            item.setExtra("不合格");
        }

        scores.add(item);
    }

    public BigDecimal getCityCoefficient(String adCode) {
        CityCoefficientEntity coefficient = cityCoefficientService.getCoefficient(adCode);
        if (Objects.nonNull(coefficient)) {
            return coefficient.getCoefficient();
        } else {
            return BigDecimal.ONE;
        }
    }

    public String calculateProjectLevel2(BigDecimal score) {
        if (score.compareTo(new BigDecimal("8.5")) >= 0) {
            return RatingConstants.RATING_LEVEL_AAA;
        } else if (score.compareTo(new BigDecimal(7)) >= 0)
            return RatingConstants.RATING_LEVEL_AA;
        else if (score.compareTo(new BigDecimal(6)) >= 0) {
            return RatingConstants.RATING_LEVEL_A;
        } else {
            return RatingConstants.RATING_LEVEL_BELOW_A;
        }
    }

    @Override
    public void export(HttpServletResponse response, BuildingRatingExportDTO dto) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            // 编码文件名，解决中文乱码问题
            String fileName = URLEncoder.encode(
                    "楼宇评级数据_" + System.currentTimeMillis() + ".xlsx",
                    StandardCharsets.UTF_8.toString()
            ).replaceAll("\\+", "%20");

            // 设置Content-Disposition响应头
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);

            // 导出数据到Excel文件
            exportRatingData(response.getOutputStream(), dto);
        } catch (IOException e) {
            throw new RuntimeException("导出Excel失败", e);
        }
    }

    private void exportRatingData(ServletOutputStream outputStream, BuildingRatingExportDTO dto) {
        // 1. 查询数据
        List<BuildingRatingEntity> buildingMetaEntityList = queryBuildingData(dto);
        if (CollectionUtils.isEmpty(buildingMetaEntityList)) {
            throw new RuntimeException("没有可导出的数据");
        }
        List<String> allBuildingNo = buildingMetaEntityList.stream().map(BuildingRatingEntity::getBuildingNo)
                .collect(Collectors.toList());
        List<TempRating> allRatings = tempRatingService.lambdaQuery()
                .in(TempRating::getBuildingNo, allBuildingNo)
                .list();
        if (CollectionUtils.isEmpty(allRatings)) {
            throw new RuntimeException("temp_rating没有找到数据");
        }
        Map<String, BuildingRatingEntity> buildingRateMap = buildingMetaEntityList.stream()
                .collect(Collectors.toMap(BuildingRatingEntity::getBuildingNo, Function.identity()));

        // 2. 按building_no分组
        Map<String, List<TempRating>> groupedByBuildingNo = allRatings.stream()
                .collect(Collectors.groupingBy(TempRating::getBuildingNo));

        // 3. 转换为导出DTO
        List<RatingExportDTO> exportData = groupedByBuildingNo.values().stream()
                .map(ratings -> convertToExportDTO(ratings, buildingRateMap))
                .collect(Collectors.toList());

        // 4. 导出Excel
        EasyExcel.write(outputStream, RatingExportDTO.class)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet("楼宇评级数据")
                .doWrite(exportData);
    }

    private List<BuildingRatingEntity> queryBuildingData(BuildingRatingExportDTO dto) {
        LambdaQueryWrapper<BuildingRatingEntity> queryWrapper = new LambdaQueryWrapper<BuildingRatingEntity>()
                .eq(BuildingRatingEntity::getDeleted, 0)
                .in(BuildingRatingEntity::getStatus, Arrays.asList(0, 1, 2));

        if (CollectionUtils.isNotEmpty(dto.getBuildingNos())) {
            queryWrapper.in(BuildingRatingEntity::getBuildingNo, dto.getBuildingNos());
        }

        if (dto.getStartTime() != null && dto.getEndTime() != null) {
            queryWrapper.between(BuildingRatingEntity::getCreateTime, dto.getStartTime(), dto.getEndTime());
        }
        return buildingRatingService.list(queryWrapper);
    }

    private RatingExportDTO convertToExportDTO(List<TempRating> ratings, Map<String, BuildingRatingEntity> buildingRateMap) {
        RatingExportDTO dto = new RatingExportDTO();
        if (CollectionUtils.isEmpty(ratings)) {
            return dto;
        }

        // 按source分类处理
        ratings.forEach(rating -> {
            switch (rating.getSource()) {
                case 0:
                    setManualData(dto, rating, buildingRateMap);
                    break;
                case 1:
                    setDoubaoData(dto, rating);
                    break;
                case 2:
                    setDeepseekData(dto, rating);
                    break;
                case 3:
                    setQianwenData(dto, rating);
                    break;
                case 4:
                    setOpenAiData(dto, rating);
                    break;
                default:
                    break;
            }
        });

        return dto;
    }

    // 设置人工数据（不加前缀）
    private void setManualData(RatingExportDTO dto, TempRating rating, Map<String, BuildingRatingEntity> buildingRateMap) {
        BuildingRatingEntity building = buildingRateMap.get(rating.getBuildingNo());
        dto.setBuildingNo(rating.getBuildingNo());
        dto.setBuildingName(building.getBuildingName());
        dto.setMapProvince(building.getMapProvince());
        dto.setMapCity(building.getMapCity());
        dto.setMapRegion(building.getMapRegion());
        dto.setMapAddress(rsaExample.decryptByPrivate(building.getMapAddress()));
        dto.setBuildingType(rating.getBuildingType());
        dto.setBuildingScore(rating.getBuildingScore());
        dto.setProjectLevel(rating.getProjectLevel());
        dto.setBuildingGrade(rating.getBuildingGrade());
        dto.setBuildingGradeScore(rating.getBuildingGradeScore());
        dto.setBuildingLocation(rating.getBuildingLocation());
        dto.setBuildingLocationScore(rating.getBuildingLocationScore());
        dto.setBuildingNumber(rating.getBuildingNumber());
        dto.setBuildingNumberScore(rating.getBuildingNumberScore());
        dto.setBuildingPrice(rating.getBuildingPrice());
        dto.setBuildingPriceScore(rating.getBuildingPriceScore());
        dto.setBuildingAge(rating.getBuildingAge());
        dto.setBuildingAgeScore(rating.getBuildingAgeScore());
        dto.setBuildingExterior(rating.getBuildingExterior());
        dto.setBuildingExteriorScore(rating.getBuildingExteriorScore());
        dto.setBuildingLobby(rating.getBuildingLobby());
        dto.setBuildingLobbyScore(rating.getBuildingLobbyScore());
        dto.setBuildingGarage(rating.getBuildingGarage());
        dto.setBuildingGarageScore(rating.getBuildingGarageScore());
        dto.setBuildingHall(rating.getBuildingHall());
        dto.setBuildingHallScore(rating.getBuildingHallScore());
        dto.setBuildingBrand(rating.getBuildingBrand());
        dto.setBuildingBrandScore(rating.getBuildingBrandScore());
        dto.setBuildingRating(rating.getBuildingRating());
        dto.setBuildingRatingScore(rating.getBuildingRatingScore());
        dto.setBuildingSettled(rating.getBuildingSettled());
        dto.setBuildingSettledScore(rating.getBuildingSettledScore());
    }

    // 设置豆包数据（加db_前缀）
    private void setDoubaoData(RatingExportDTO dto, TempRating rating) {
        dto.setDbBuildingType(rating.getBuildingType());
        dto.setDbBuildingScore(rating.getBuildingScore());
        dto.setDbProjectLevel(rating.getProjectLevel());
        dto.setDbBuildingGrade(rating.getBuildingGrade());
        dto.setDbBuildingGradeScore(rating.getBuildingGradeScore());
        dto.setDbBuildingLocation(rating.getBuildingLocation());
        dto.setDbBuildingLocationScore(rating.getBuildingLocationScore());
        dto.setDbBuildingNumber(rating.getBuildingNumber());
        dto.setDbBuildingNumberScore(rating.getBuildingNumberScore());
        dto.setDbBuildingPrice(rating.getBuildingPrice());
        dto.setDbBuildingPriceScore(rating.getBuildingPriceScore());
        dto.setDbBuildingAge(rating.getBuildingAge());
        dto.setDbBuildingAgeScore(rating.getBuildingAgeScore());
        dto.setDbBuildingExterior(rating.getBuildingExterior());
        dto.setDbBuildingExteriorScore(rating.getBuildingExteriorScore());
        dto.setDbBuildingLobby(rating.getBuildingLobby());
        dto.setDbBuildingLobbyScore(rating.getBuildingLobbyScore());
        dto.setDbBuildingGarage(rating.getBuildingGarage());
        dto.setDbBuildingGarageScore(rating.getBuildingGarageScore());
        dto.setDbBuildingHall(rating.getBuildingHall());
        dto.setDbBuildingHallScore(rating.getBuildingHallScore());
        dto.setDbBuildingBrand(rating.getBuildingBrand());
        dto.setDbBuildingBrandScore(rating.getBuildingBrandScore());
        dto.setDbBuildingRating(rating.getBuildingRating());
        dto.setDbBuildingRatingScore(rating.getBuildingRatingScore());
        dto.setDbBuildingSettled(rating.getBuildingSettled());
        dto.setDbBuildingSettledScore(rating.getBuildingSettledScore());
    }

    // 设置deepseek数据（加ds_前缀）
    private void setDeepseekData(RatingExportDTO dto, TempRating rating) {
        dto.setDsBuildingType(rating.getBuildingType());
        dto.setDsBuildingScore(rating.getBuildingScore());
        dto.setDsProjectLevel(rating.getProjectLevel());
        dto.setDsBuildingGrade(rating.getBuildingGrade());
        dto.setDsBuildingGradeScore(rating.getBuildingGradeScore());
        dto.setDsBuildingLocation(rating.getBuildingLocation());
        dto.setDsBuildingLocationScore(rating.getBuildingLocationScore());
        dto.setDsBuildingNumber(rating.getBuildingNumber());
        dto.setDsBuildingNumberScore(rating.getBuildingNumberScore());
        dto.setDsBuildingPrice(rating.getBuildingPrice());
        dto.setDsBuildingPriceScore(rating.getBuildingPriceScore());
        dto.setDsBuildingAge(rating.getBuildingAge());
        dto.setDsBuildingAgeScore(rating.getBuildingAgeScore());
        dto.setDsBuildingExterior(rating.getBuildingExterior());
        dto.setDsBuildingExteriorScore(rating.getBuildingExteriorScore());
        dto.setDsBuildingLobby(rating.getBuildingLobby());
        dto.setDsBuildingLobbyScore(rating.getBuildingLobbyScore());
        dto.setDsBuildingGarage(rating.getBuildingGarage());
        dto.setDsBuildingGarageScore(rating.getBuildingGarageScore());
        dto.setDsBuildingHall(rating.getBuildingHall());
        dto.setDsBuildingHallScore(rating.getBuildingHallScore());
        dto.setDsBuildingBrand(rating.getBuildingBrand());
        dto.setDsBuildingBrandScore(rating.getBuildingBrandScore());
        dto.setDsBuildingRating(rating.getBuildingRating());
        dto.setDsBuildingRatingScore(rating.getBuildingRatingScore());
        dto.setDsBuildingSettled(rating.getBuildingSettled());
        dto.setDsBuildingSettledScore(rating.getBuildingSettledScore());
    }

    // 设置千问数据（加qw_前缀）
    private void setQianwenData(RatingExportDTO dto, TempRating rating) {
        dto.setQwBuildingType(rating.getBuildingType());
        dto.setQwBuildingScore(rating.getBuildingScore());
        dto.setQwProjectLevel(rating.getProjectLevel());
        dto.setQwBuildingGrade(rating.getBuildingGrade());
        dto.setQwBuildingGradeScore(rating.getBuildingGradeScore());
        dto.setQwBuildingLocation(rating.getBuildingLocation());
        dto.setQwBuildingLocationScore(rating.getBuildingLocationScore());
        dto.setQwBuildingNumber(rating.getBuildingNumber());
        dto.setQwBuildingNumberScore(rating.getBuildingNumberScore());
        dto.setQwBuildingPrice(rating.getBuildingPrice());
        dto.setQwBuildingPriceScore(rating.getBuildingPriceScore());
        dto.setQwBuildingAge(rating.getBuildingAge());
        dto.setQwBuildingAgeScore(rating.getBuildingAgeScore());
        dto.setQwBuildingExterior(rating.getBuildingExterior());
        dto.setQwBuildingExteriorScore(rating.getBuildingExteriorScore());
        dto.setQwBuildingLobby(rating.getBuildingLobby());
        dto.setQwBuildingLobbyScore(rating.getBuildingLobbyScore());
        dto.setQwBuildingGarage(rating.getBuildingGarage());
        dto.setQwBuildingGarageScore(rating.getBuildingGarageScore());
        dto.setQwBuildingHall(rating.getBuildingHall());
        dto.setQwBuildingHallScore(rating.getBuildingHallScore());
        dto.setQwBuildingBrand(rating.getBuildingBrand());
        dto.setQwBuildingBrandScore(rating.getBuildingBrandScore());
        dto.setQwBuildingRating(rating.getBuildingRating());
        dto.setQwBuildingRatingScore(rating.getBuildingRatingScore());
        dto.setQwBuildingSettled(rating.getBuildingSettled());
        dto.setQwBuildingSettledScore(rating.getBuildingSettledScore());
    }

    // 设置OpenAi数据（加oa_前缀）
    private void setOpenAiData(RatingExportDTO dto, TempRating rating) {
        dto.setOaBuildingType(rating.getBuildingType());
        dto.setOaBuildingScore(rating.getBuildingScore());
        dto.setOaProjectLevel(rating.getProjectLevel());
        dto.setOaBuildingGrade(rating.getBuildingGrade());
        dto.setOaBuildingGradeScore(rating.getBuildingGradeScore());
        dto.setOaBuildingLocation(rating.getBuildingLocation());
        dto.setOaBuildingLocationScore(rating.getBuildingLocationScore());
        dto.setOaBuildingNumber(rating.getBuildingNumber());
        dto.setOaBuildingNumberScore(rating.getBuildingNumberScore());
        dto.setOaBuildingPrice(rating.getBuildingPrice());
        dto.setOaBuildingPriceScore(rating.getBuildingPriceScore());
        dto.setOaBuildingAge(rating.getBuildingAge());
        dto.setOaBuildingAgeScore(rating.getBuildingAgeScore());
        dto.setOaBuildingExterior(rating.getBuildingExterior());
        dto.setOaBuildingExteriorScore(rating.getBuildingExteriorScore());
        dto.setOaBuildingLobby(rating.getBuildingLobby());
        dto.setOaBuildingLobbyScore(rating.getBuildingLobbyScore());
        dto.setOaBuildingGarage(rating.getBuildingGarage());
        dto.setOaBuildingGarageScore(rating.getBuildingGarageScore());
        dto.setOaBuildingHall(rating.getBuildingHall());
        dto.setOaBuildingHallScore(rating.getBuildingHallScore());
        dto.setOaBuildingBrand(rating.getBuildingBrand());
        dto.setOaBuildingBrandScore(rating.getBuildingBrandScore());
        dto.setOaBuildingRating(rating.getBuildingRating());
        dto.setOaBuildingRatingScore(rating.getBuildingRatingScore());
        dto.setOaBuildingSettled(rating.getBuildingSettled());
        dto.setOaBuildingSettledScore(rating.getBuildingSettledScore());
    }


    private void cacheCalculateResult(String mapNo, CalculateResultDTO details) {
        String jsonString = JSON.toJSONString(details);
        redisTemplate.opsForValue()
                .set(String.format("building_trial_calculate_%s", mapNo), jsonString, buildingTrialExpireTime, TimeUnit.SECONDS);
    }

    public void processNumberMatch(BuildingDetailsEntity details, String code,
                                   Map<String, List<BuildingParameterEntity>> ruleMap) {

        String value = details.getValue(code);
        if (StringUtils.isBlank(value)) {
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        try {
            BigDecimal number = new BigDecimal(value.replaceAll("[^0-9\\.]", ""));
            rules.stream()
                    .filter(rule -> matchNumberRule(rule.getParameterRule(), number))
                    .findFirst()
                    .ifPresent(rule -> details.setValueId(code + "id", rule.getId()));
        } catch (NumberFormatException e) {
            log.warn("数值格式转换异常: {}", value);
        }
    }

    private void processNumberMatch2(BuildingDetailsEntity details, String code,
                                     Map<String, List<BuildingParameterEntity>> ruleMap) {

        String value = details.getValue("third" + code);

        if (aiBaseScoreProperties.isInvalid(value)) {
            log.info("AI数据（{}）无效，设置基础分，code：{}", value, code);
            details.setValueId("third" + code + "id", aiBaseScoreProperties.getLatestScore().id());
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        try {
            BigDecimal number = new BigDecimal(value.replaceAll("[^0-9\\.]", ""));
            rules.stream()
                    .filter(rule -> matchNumberRule(rule.getParameterRule(), number))
                    .findFirst()
                    .ifPresent(rule -> details.setValueId("third" + code + "id", rule.getId()));
        } catch (NumberFormatException e) {
            log.warn("数值格式转换异常: {}", value);
        }
    }

    private boolean matchNumberRule(String rule, BigDecimal value) {
        String[] rules = rule.split(",");
        for (String r : rules) {
            r = r.trim();
            if (!evaluateCondition(r, value)) {
                return false;
            }
        }
        return true;
    }

    private boolean evaluateCondition(String condition, BigDecimal value) {
        if (condition.startsWith(">=")) {
            return value.compareTo(new BigDecimal(condition.substring(2))) >= 0;
        } else if (condition.startsWith("<=")) {
            return value.compareTo(new BigDecimal(condition.substring(2))) <= 0;
        } else if (condition.startsWith(">")) {
            return value.compareTo(new BigDecimal(condition.substring(1))) > 0;
        } else if (condition.startsWith("<")) {
            return value.compareTo(new BigDecimal(condition.substring(1))) < 0;
        } else if (condition.startsWith("=")) {
            return value.compareTo(new BigDecimal(condition.substring(1))) == 0;
        }
        return false;
    }

    public void processRentMatch(BuildingDetailsEntity details, String code,
                                 Map<String, List<BuildingParameterEntity>> ruleMap, String mapAdCode, Integer buildingType) {

        String value = details.getValue(code);
        if (StringUtils.isBlank(value)) {
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        CityRentEntity cityRent = cityRentService.getRent(mapAdCode);
        if (Objects.isNull(cityRent)) {
            if (buildingType.equals(0)) {
                rules.stream()
                        .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(6)) == 0)
                        .findFirst()
                        .ifPresent(rule -> details.setValueId(code + "id", rule.getId()));
                return;
            } else {
                rules.stream()
                        .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(7)) == 0)
                        .findFirst()
                        .ifPresent(rule -> details.setValueId(code + "id", rule.getId()));
                return;
            }

        }

        try {
            BigDecimal price = new BigDecimal(value.replaceAll("[^0-9]\\.", ""));
            BigDecimal avgRent;
            if (buildingType.equals(0)) {
                BuildingCityRentEntity buildingCityRentEntity = buildingCityRentService.lambdaQuery()
                        .eq(BuildingCityRentEntity::getAdCode, mapAdCode)
                        .one();
                if (Objects.nonNull(buildingCityRentEntity)) {
                    avgRent = buildingCityRentEntity.getOfficeRent();
                } else {
                    rules.stream()
                            .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(6)) == 0)
                            .findFirst()
                            .ifPresent(rule -> details.setValueId(code + "id", rule.getId()));
                    return;
                }
            } else {
                avgRent = cityRent.getOfficeRent();
            }

            rules.stream()
                    .filter(rule -> matchRentRule(rule.getParameterRule(), price, avgRent))
                    .findFirst()
                    .ifPresent(rule -> details.setValueId(code + "id", rule.getId()));
        } catch (NumberFormatException e) {
            log.warn("租金格式转换异常: {}", value, e);
        }
    }

    @Deprecated
    private void processRentMatch2(BuildingDetailsEntity details, String code,
                                   Map<String, List<BuildingParameterEntity>> ruleMap, String mapAdCode, Integer buildingType) {

        String value = details.getValue("third" + code);
        if (StringUtils.isBlank(value)) {
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        CityRentEntity cityRent = cityRentService.getRent(mapAdCode);
        if (Objects.isNull(cityRent)) {
            if (buildingType.equals(0)) {
                rules.stream()
                        .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(6)) == 0)
                        .findFirst()
                        .ifPresent(rule -> details.setValueId("third" + code + "id", rule.getId()));
                return;
            } else {
                rules.stream()
                        .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(7)) == 0)
                        .findFirst()
                        .ifPresent(rule -> details.setValueId("third" + code + "id", rule.getId()));
                return;
            }

        }

        try {
            BigDecimal price = new BigDecimal(value.replaceAll("[^0-9\\.]", ""));
            BigDecimal avgRent;
            if (buildingType.equals(0)) {
                BuildingCityRentEntity buildingCityRentEntity = buildingCityRentService.lambdaQuery()
                        .eq(BuildingCityRentEntity::getAdCode, mapAdCode)
                        .one();
                if (Objects.nonNull(buildingCityRentEntity)) {
                    avgRent = buildingCityRentEntity.getOfficeRent();
                } else {
                    rules.stream()
                            .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(6)) == 0)
                            .findFirst()
                            .ifPresent(rule -> details.setValueId("third" + code + "id", rule.getId()));
                    return;
                }
            } else {
                avgRent = cityRent.getOfficeRent();
            }

            rules.stream()
                    .filter(rule -> matchRentRule(rule.getParameterRule(), price, avgRent))
                    .findFirst()
                    .ifPresent(rule -> details.setValueId("third" + code + "id", rule.getId()));
        } catch (NumberFormatException e) {
            log.warn("租金格式转换异常: {}", value);
        }
    }


    /**
     * 校验商机是否完成评级
     *
     * @param dtoList
     * @return
     */
    @Override
    public int isFinishRating(List<VerifyRatingDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return 0;
        }
        log.info("校验商机是否完成评级 {}", JSON.toJSONString(dtoList));
        List<BusinessOpportunityEntity> opportunityEntityList = businessOpportunityService.lambdaQuery()
                .in(BusinessOpportunityEntity::getCode, dtoList.stream().map(VerifyRatingDTO::getBusinessCode).toList())
                .list();
        if (CollUtil.isEmpty(opportunityEntityList)) {
            return 0;
        }
        log.info("opportunityEntityList:{}", JSON.toJSONString(opportunityEntityList));
        List<String> buildingRatingCodes = opportunityEntityList.stream().map(BusinessOpportunityEntity::getBuildingNo)
                .toList();
        Map<String, BuildingRatingEntity> ratingMap = buildingRatingService.lambdaQuery()
                .in(BuildingRatingEntity::getBuildingNo, buildingRatingCodes).list().stream()
                .collect(Collectors.toMap(BuildingRatingEntity::getBuildingNo, Function.identity(), (o, n) -> n));

        Map<String, List<CompleteRatingEntity>> completeRatingMap = completeRatingService.lambdaQuery()
                .select(CompleteRatingEntity::getBuildingRatingNo, CompleteRatingEntity::getLargeScreenFlag)
                .eq(CompleteRatingEntity::getStatus, BuildingRatingEntity.Status.WAIT_AUDIT.getValue())
                .in(CompleteRatingEntity::getBuildingRatingNo, buildingRatingCodes)
                .list().stream().collect(Collectors.groupingBy(CompleteRatingEntity::getBuildingRatingNo));

        if (CollUtil.isEmpty(ratingMap)) {
            return 0;
        }
        Map<String, Boolean> containsLargeMap = dtoList.stream()
                .collect(Collectors.toMap(VerifyRatingDTO::getBusinessCode, VerifyRatingDTO::getContainsLarge));
        log.info("ratingMap:{}", JSON.toJSONString(ratingMap));
        for (BusinessOpportunityEntity opportunityEntity : opportunityEntityList) {
            BuildingRatingEntity buildingRating = ratingMap.get(opportunityEntity.getBuildingNo());
            List<Integer> list = completeRatingMap.getOrDefault(opportunityEntity.getBuildingNo(),
                    java.util.Collections.emptyList()).stream().map(CompleteRatingEntity::getLargeScreenFlag).toList();
            if (Objects.isNull(buildingRating)) {
                return 0;
            }
            if (containsLargeMap.get(opportunityEntity.getCode())) {
                if (buildingRating.getLargeScreenRatingFlag().equals(0) || (list.contains(2) || list.contains(3))) {
                    return 2;
                }
            } else {
                if (buildingRating.getSmallScreenRatingFlag().equals(0) || list.contains(1)) {
                    return 3;
                }
            }
        }
        return 1;
    }

    private boolean matchRentRule(String rule, BigDecimal value, BigDecimal avgRent) {
        String[] rules = rule.split(",");
        for (String r : rules) {
            r = r.trim();
            if (!evaluateRentCondition(r, value, avgRent)) {
                return false;
            }
        }
        return true;
    }

    private boolean evaluateRentCondition(String condition, BigDecimal value, BigDecimal avgRent) {
        if (condition.startsWith(">=")) {
            return value.compareTo(new BigDecimal(condition.substring(2)).multiply(avgRent)) >= 0;
        } else if (condition.startsWith("<=")) {
            return value.compareTo(new BigDecimal(condition.substring(2)).multiply(avgRent)) <= 0;
        } else if (condition.startsWith(">")) {
            return value.compareTo(new BigDecimal(condition.substring(1)).multiply(avgRent)) > 0;
        } else if (condition.startsWith("<")) {
            return value.compareTo(new BigDecimal(condition.substring(1)).multiply(avgRent)) < 0;
        } else if (condition.startsWith("=")) {
            return value.compareTo(new BigDecimal(condition.substring(1)).multiply(avgRent)) == 0;
        }
        return false;
    }

    private Map<String, BigDecimal> getFloorScoreConfig() {
        Map<String, BigDecimal> configMap = new HashMap<>();
        List<SysConfigEntity> configs = sysConfigService.list();

        List<String> keys = Arrays.asList("firstFloorExclusive", "firstFloorShare", "negativeFirstFloor", "negativeTwoFloor", "twoFloorAbove", "thirdFloorBelow", "floor_unqualified_score");

        configs.forEach(config -> {
            if (StringUtils.isNotBlank(config.getValue())) {
                if (keys.contains(config.getKey())) {
                    configMap.put(config.getKey(), new BigDecimal(config.getValue()));
                }
            }
        });

        return configMap;
    }

    /**
     * 获取关联了crm流程的楼宇记录列表
     */
    @Override
    public List<BuildingRatingEntity> listCrmFlow() {
        // 查询customerId和crmFlowId不为空的记
        LambdaQueryWrapper<BuildingRatingEntity> lambdaQueryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda()
                .isNotNull(BuildingRatingEntity::getCustomerId).or().isNotNull(BuildingRatingEntity::getCrmFlowId);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public BuildingRatingPicVO buildingPic(BuildingPicReq buildingPicReq) {
        BuildingRatingPicVO buildingRatingPicVO = new BuildingRatingPicVO();
        String buildingNo = "";
        if (StringUtils.isNotBlank(buildingPicReq.getBuildingNo())) {
            buildingNo = buildingPicReq.getBuildingNo();
        } else if (StringUtils.isNotBlank(buildingPicReq.getBusinessCode())) {
            BusinessOpportunityEntity businessOpportunityEntity = businessOpportunityService.getOne(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                    .eq(BusinessOpportunityEntity::getCode, buildingPicReq.getBusinessCode()));
            if (Objects.isNull(businessOpportunityEntity)) {
                return buildingRatingPicVO;
            }
            buildingNo = businessOpportunityEntity.getBuildingNo();
        }
        BuildingRatingEntity buildingRating = this.getOne(Wrappers.<BuildingRatingEntity>lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo));
        if (Objects.isNull(buildingRating)) return buildingRatingPicVO;
        buildingRatingPicVO.setBuildingName(buildingRating.getBuildingName());
        buildingRatingPicVO.setMapCity(buildingRating.getMapCity());
        buildingRatingPicVO.setMapProvince(buildingRating.getMapProvince());
        buildingRatingPicVO.setMapRegion(buildingRating.getMapRegion());

        BuildingMetaEntity buildingMetaEntity = buildingMetaService.getOne(Wrappers.<BuildingMetaEntity>lambdaQuery()
                .eq(BuildingMetaEntity::getBuildingRatingNo, buildingNo));
        List<BuildingMetaImgRelationEntity> buildingMetaImgRelationEntityList = buildingMetaImgRelationService.list(Wrappers.<BuildingMetaImgRelationEntity>lambdaQuery()
                .eq(BuildingMetaImgRelationEntity::getBuildingMetaNo, buildingMetaEntity.getBuildingMetaNo()));
        List<String> urls = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(buildingMetaImgRelationEntityList)) {
            List<String> buildingExteriorPic = new ArrayList<>();
            List<String> buildingLobbyPic = new ArrayList<>();
            List<String> buildingHallPic = new ArrayList<>();
            List<String> otherPic = new ArrayList<>();
            buildingMetaImgRelationEntityList.forEach(entity -> {
                switch (entity.getImgType()) {
                    case 1:
                        buildingExteriorPic.add(entity.getImgUrl());
                        break;
                    case 2:
                        buildingLobbyPic.add(entity.getImgUrl());
                        break;
                    case 3:
                        buildingHallPic.add(entity.getImgUrl());
                        break;
                    case 4:
                    case 5:
                    case 6:
                    case 7:
                        otherPic.add(entity.getImgUrl());
                        break;
                    default:
                        break;
                }
            });
            urls.addAll(buildingExteriorPic);
            urls.addAll(buildingLobbyPic);
            urls.addAll(buildingHallPic);
            urls.addAll(otherPic);
        }
        buildingRatingPicVO.setPic(urls);
        return buildingRatingPicVO;
    }

    @Override
    @Transactional
    public Boolean transferCustomer(TransferReq req) {
        log.info("转移客户: {}", UserThreadLocal.getUser());
        boolean flag = false;
        try {
            BuildingRatingEntity buildingRating = this.getOne(Wrappers.<BuildingRatingEntity>lambdaQuery()
                    .eq(BuildingRatingEntity::getBuildingNo, req.getBuildingNo()));
            List<BuildingMetaEntity> buildingMetaEntities = buildingMetaService.list(Wrappers.<BuildingMetaEntity>lambdaQuery()
                    .eq(BuildingMetaEntity::getBuildingRatingNo, req.getBuildingNo()));
            List<BusinessOpportunityEntity> businessOpportunityEntities = businessOpportunityService.list(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                    .eq(BusinessOpportunityEntity::getBuildingNo, req.getBuildingNo()));
            List<PriceApplyEntity> priceApplyEntities = priceApplyService.list(Wrappers.<PriceApplyEntity>lambdaQuery()
                    .eq(PriceApplyEntity::getBuildingNo, req.getBuildingNo()));
            List<PointEntity> pointEntities = pointService.list(Wrappers.<PointEntity>lambdaQuery()
                    .eq(PointEntity::getBuildingRatingNo, req.getBuildingNo()));
            List<PointPlanEntity> pointPlanEntities = pointPlanService.list(Wrappers.<PointPlanEntity>lambdaQuery()
                    .eq(PointPlanEntity::getBuildingRatingNo, req.getBuildingNo()));

            if (CollectionUtils.isNotEmpty(pointPlanEntities)) {
                pointPlanEntities.forEach(pointPlanEntity -> {
                    pointPlanEntity.setCreateBy(req.getUserCode());
                    pointPlanService.updateById(pointPlanEntity);
                });
            }

            if (CollectionUtils.isNotEmpty(pointEntities)) {
                pointEntities.forEach(pointEntity -> {
                    pointEntity.setCreateBy(req.getUserCode());
                    pointService.updateById(pointEntity);
                });
            }

            if (Objects.nonNull(buildingRating)) {
                buildingRating.setSubmitUser(req.getUserCode());
                this.updateById(buildingRating);
            }

            if (CollectionUtils.isNotEmpty(buildingMetaEntities)) {
                buildingMetaEntities.forEach(buildingMetaEntity -> {
                    buildingMetaEntity.setManager(req.getUserCode());
                    buildingMetaService.updateById(buildingMetaEntity);
                });
            }

            if (CollectionUtils.isNotEmpty(businessOpportunityEntities)) {
                businessOpportunityEntities.forEach(businessOpportunityEntity -> {
                    businessOpportunityEntity.setSubmitUser(req.getUserCode());
                    businessOpportunityEntity.setOwner(req.getUserCode());
                    businessOpportunityService.updateById(businessOpportunityEntity);
                });
            }

            if (CollectionUtils.isNotEmpty(priceApplyEntities)) {
                priceApplyEntities.forEach(priceApplyEntity -> {
                    priceApplyEntity.setCreateBy(req.getUserCode());
                    priceApplyService.updateById(priceApplyEntity);
                });
            }
            flag = true;
        } catch (Exception e) {
            log.error("转移客户异常: {}", e.getMessage());
            throw new RuntimeException(e);
        }
        return flag;
    }

    @Override
    @Transactional
    public Boolean transferCustomerBatch(TransferReq req) {
        log.info("批量转移客户{}", UserThreadLocal.getUser());
        boolean flag = false;
        try {
            this.lambdaUpdate()
                    .set(BuildingRatingEntity::getSubmitUser, req.getUserCode())
                    .eq(BuildingRatingEntity::getSubmitUser, req.getTransferUserCode())
                    .update();
            buildingMetaService.lambdaUpdate()
                    .set(BuildingMetaEntity::getManager, req.getUserCode())
                    .eq(BuildingMetaEntity::getManager, req.getTransferUserCode())
                    .update();
            businessOpportunityService.lambdaUpdate()
                    .set(BusinessOpportunityEntity::getSubmitUser, req.getUserCode())
                    .set(BusinessOpportunityEntity::getOwner, req.getUserCode())
                    .eq(BusinessOpportunityEntity::getSubmitUser, req.getTransferUserCode())
                    .update();
            priceApplyService.lambdaUpdate()
                    .set(PriceApplyEntity::getCreateBy, req.getUserCode())
                    .eq(PriceApplyEntity::getCreateBy, req.getTransferUserCode())
                    .update();
            pointService.lambdaUpdate()
                    .set(PointEntity::getCreateBy, req.getUserCode())
                    .eq(PointEntity::getCreateBy, req.getTransferUserCode())
                    .update();
            pointPlanService.lambdaUpdate()
                    .set(PointPlanEntity::getCreateBy, req.getUserCode())
                    .eq(PointPlanEntity::getCreateBy, req.getTransferUserCode())
                    .update();
            flag = true;
        } catch (Exception e) {
            log.error("转移客户异常: {}", e.getMessage());
            throw new RuntimeException(e);
        }
        return flag;
    }

    @Override
    public Boolean hasContract(String mapNo) {
        List<BuildingRatingEntity> list = this.lambdaQuery()
                .eq(BuildingRatingEntity::getMapNo, mapNo)
                .eq(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.AUDITED.value)
                .eq(BuildingRatingEntity::getBuildingStatus, BuildingRatingEntity.BuildingStatus.CONFIRMED.value)
                .list();
        if (CollectionUtils.isEmpty(list)) return false;

        List<BusinessOpportunityEntity> businessOpportunityEntities = businessOpportunityService.lambdaQuery()
                .in(BusinessOpportunityEntity::getBuildingNo, list.stream().map(BuildingRatingEntity::getBuildingNo)
                        .collect(Collectors.toSet()))
                .list();
        if (CollectionUtils.isEmpty(businessOpportunityEntities)) return false;

        ResultTemplate<Boolean> booleanResultTemplate = feignCmsRpc.hasContract(businessOpportunityEntities.stream()
                .map(BusinessOpportunityEntity::getCode).collect(Collectors.toList()));
        return booleanResultTemplate.getData();
    }

    @Override
    public List<String> buildingCIty() {

        ResultTemplate<List<DictCodeVO>> listResultTemplate = feignAuthorityRpc.selectList();
        log.info("城市数据listResultTemplate:{}", JsonUtils.toJson(listResultTemplate));
        if (Objects.isNull(listResultTemplate.getData()) || CollectionUtil.isEmpty(listResultTemplate.getData()))
            return Collections.emptyList();

        List<String> list = listResultTemplate.getData().stream().map(DictCodeVO::getName).toList();

        ResultTemplate<UserDataAccessV2DTO> userDataAccessV2 = feignCmsRpc.getUserDataAccessV2();
        log.info("cms权限数据userDataAccessV2:{}", JsonUtils.toJson(userDataAccessV2));
        if (Objects.isNull(userDataAccessV2.getData()) || CollectionUtil.isEmpty(userDataAccessV2.getData()
                .getCityIds()))
            return list;

        List<Integer> cityIds = userDataAccessV2.getData().getCityIds();
        ResultTemplate<List<CodeNameVO>> city = feignAuthorityRpc.listCityByIds(cityIds);
        log.info("城市权限数据city:{}", JsonUtils.toJson(city));
        List<String> cityName = city.getData().stream().map(CodeNameVO::getName).collect(Collectors.toList());

        List<String> newCity = list.stream().filter(s -> !cityName.contains(s)).toList();
        cityName.addAll(newCity);

        return cityName;
    }

    @Override
    public BuildingStatusVO buildingStatus(String mapNo) {
        BuildingStatusVO buildingStatusVO = new BuildingStatusVO();
        List<BuildingRatingEntity> buildingRatingEntities = this.lambdaQuery()
                .eq(BuildingRatingEntity::getMapNo, mapNo)
                .ne(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.DRAFT.getValue())
                .orderByDesc(BuildingRatingEntity::getCreateTime)
                .list();
        if (CollectionUtil.isEmpty(buildingRatingEntities)) {
            return buildingStatusVO;
        }

        BuildingRatingEntity buildingRating = buildingRatingEntities.get(0);

        List<BusinessOpportunityEntity> businessOpportunityEntities = businessOpportunityService.lambdaQuery()
                .eq(BusinessOpportunityEntity::getBuildingNo, buildingRating.getBuildingNo())
                .list();
        if (CollectionUtils.isNotEmpty(businessOpportunityEntities)) {
            List<String> collect = businessOpportunityEntities.stream().map(BusinessOpportunityEntity::getStatus)
                    .distinct().toList();
            List<String> list = collect.stream().sorted().toList();
            buildingStatusVO.setBusinessStatus(BusinessChangeStatusEnum.getByCode(list.get(list.size() - 1)));
        }

        ResultTemplate<String> stringResultTemplate = feignCmsRpc.hasContractStatus(businessOpportunityEntities.stream()
                .map(BusinessOpportunityEntity::getCode).collect(Collectors.toList()));
        buildingStatusVO.setContractStatus(stringResultTemplate.getData());

        // 公海标识
        buildingStatusVO.setHighSeaFlag(buildingRating.getHighSeaFlag());
        // 楼宇id
        buildingStatusVO.setId(buildingRating.getId());

        return buildingStatusVO;
    }

    @Override
    public BuildingMarginVO buildingMargin(String mapNo) {
        BuildingMarginVO buildingMarginVO = new BuildingMarginVO();
        List<BuildingMetaEntity> list = buildingMetaService.lambdaQuery()
                .select(BuildingMetaEntity::getBuildingTypeAi, BuildingMetaEntity::getProjectLevelAi)
                .eq(BuildingMetaEntity::getMapNo, mapNo)
                .list();
        if (CollectionUtil.isNotEmpty(list)) {
            buildingMarginVO.setBuildingType(list.get(0).getBuildingTypeAi());
            buildingMarginVO.setProjectLevel(list.get(0).getProjectLevelAi());
        }
        return buildingMarginVO;
    }

    @Override
    public String importAiData(MultipartFile file) {
        List<BuildingRatingDTO> allExcelData = readCsExcelInfo(file);
        log.info("共有{}条数据", allExcelData.size());
        int count = 0;
        for (BuildingRatingDTO entity : allExcelData) {
            log.info("正在处理第{}条数据", count++);
            try {
                buildingRatingService.lambdaUpdate()
                        .eq(BuildingRatingEntity::getBuildingNo, entity.getBuildingNo())
                        .set(BuildingRatingEntity::getBuildingAiScore, entity.getBuildingAiScore() == null ? 0 : entity.getBuildingAiScore())
                        .set(BuildingRatingEntity::getProjectAiLevel, StringUtils.isEmpty(entity.getProjectLevelAi()) ? "" : entity.getProjectLevelAi())
                        .update();
            } catch (Exception e) {
                log.info("处理ai评分报错实体:{}", JSON.toJSONString(entity));
            }
        }
        return null;
    }

    @Override
    public String topLevel(String buildingNo) {
        BuildingRatingEntity buildingRating = this.getOne(Wrappers.<BuildingRatingEntity>lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, buildingNo));
        BuildingTopQueryParam buildingTopQueryParam = new BuildingTopQueryParam();
        buildingTopQueryParam.setBuildingName(buildingRating.getBuildingName());
        buildingTopQueryParam.setProvince(buildingRating.getMapProvince());
        buildingTopQueryParam.setCity(buildingRating.getMapCity());
        String buildingTypeName = switch (buildingRating.getBuildingType()) {
            case 0 -> "写字楼";
            case 1 -> "商住楼";
            case 2 -> "综合体";
            case 3 -> "产业园区";
            default -> "";
        };
        buildingTopQueryParam.setBuildingType(buildingTypeName);
        log.info("topLevel请求参数:{}", JsonUtils.toJson(buildingTopQueryParam));
        ResultTemplate<BuildingTopEntity> unique = feignMehtWebRpc.getUnique(buildingTopQueryParam);
        log.info("topLevel返回结果:{}", JsonUtils.toJson(unique));
        if (Objects.nonNull(unique.getData())) {
            return unique.getData().getTopLevel();
        }
        return "";
    }

    private List<BuildingRatingDTO> readCsExcelInfo(MultipartFile file) {
        List<BuildingRatingDTO> bankData = new ArrayList<>();

        try {
            // Reset input stream for second sheet
            InputStream fileInputStream = file.getInputStream();

            // 读第二个表单
            EasyExcel.read(fileInputStream, BuildingRatingDTO.class, new AnalysisEventListener<BuildingRatingDTO>() {
                private int rowIndex = 0;

                @Override
                public void invoke(BuildingRatingDTO data, AnalysisContext context) {
                    rowIndex++;
                    bankData.add(data);
                    log.info("Sheet 2 行号：{}", rowIndex);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Sheet 2 读取完成，总行数：{}", rowIndex);
                }
            }).sheet(0).doRead();

            return bankData;
        } catch (IOException e) {
            log.error("读取Excel文件失败", e);
            throw new RuntimeException("读取Excel文件失败", e);
        }
    }

    @Deprecated
    @Override
    public String largeScreenCalculate(BigScreenCalculateDTO param) {
        log.info("大屏点位系数计算用户工号:{},mapNo:{},入参:{}", UserThreadLocal.getUser()
                .getWno(), param.getMapNo(), JSON.toJSONString(param));
        String coefficient = largeScreenCalculator.calculate(param);
        param.setCoefficient(coefficient);
        redisTemplate.opsForValue().set(String.format("building_trial_calculate_screen_%s", param.getMapNo()),
                JSON.toJSONString(param), buildingTrialExpireTime, TimeUnit.SECONDS);
        log.info("大屏点位系数计算结果:{}", coefficient);
        return coefficient;
    }

    @Override
    public BuildingVO getBuildingLocation(String buildingNo) {
        if (StringUtils.isBlank(buildingNo)) {
            return null;
        }

        BuildingDetailsEntity details = buildingDetailsService.getDetailsByBuildingNo(buildingNo);
        if (details == null) {
            return null;
        }
        BuildingVO vo = new BuildingVO();
        vo.setBuildingNo(buildingNo);
        vo.setAiBuildingLocation(details.getThirdBuildingLocation());

        Long buildingLocation = details.getBuildingLocation();
        BuildingParameterEntity parameter = null;
        if (buildingLocation != null) {
            parameter = buildingParameterService.getById(buildingLocation);
            if (parameter != null) {
                vo.setBuildingLocation(parameter.getParameterName());
            }
        }
        return vo;
    }

    @Override
    public List<String> getHighSeaCities() {
        return baseMapper.getHighSeaCities();
    }

    public BuildingDetailsEntity processDetailData(List<BuildingParameterEntity> parameterList, BuildingDetailsEntity details, Integer buildingType, String mapAdCode) {
        if (CollectionUtils.isEmpty(parameterList)) {
            return details;
        }

        Map<String, List<BuildingParameterEntity>> ruleMap = parameterList.stream()
                .filter(e -> e.getParentId() != 0)
                .collect(Collectors.groupingBy(BuildingParameterEntity::getParameterCode));

        // 处理等级
        processSimpleMatch(details, "buildingGrade", ruleMap,
                (rule, value) -> rule.getParameterRule().equals(value.strip()));

        // 处理地理位置
        processSimpleMatch(details, "buildingLocation", ruleMap,
                (rule, value) -> rule.getParameterRule().equals(value.strip()));

        // 处理楼层数
        processNumberMatch2(details, "buildingNumber", ruleMap);

        // 处理月租金
        processRentMatch2(details, "buildingPrice", ruleMap, mapAdCode, buildingType);

        // 处理楼龄
        processNumberMatch2(details, "buildingAge", ruleMap);

        // 处理外立面
        processSimilarityMatch(details, "buildingExterior", ruleMap, 0.2);

        // 处理大堂
        processSimilarityMatch(details, "buildingLobby", ruleMap, 0.2);

        // 处理品牌
        processSimpleMatch(details, "buildingBrand", ruleMap,
                (rule, value) -> rule.getParameterRule().equals(value.strip()));

        // 处理评分
        processNumberMatch2(details, "buildingRating", ruleMap);

        // 处理地下车库
        processSimilarityMatch(details, "buildingGarage", ruleMap, 0.6);

        return details;
    }

    /**
     * 根据商机查询楼宇信息
     *
     * @param businessCode 商机编码
     * @return CmsBusinessDto
     */
    @Override
    public CmsBusinessDto getBuildingByOpportunityCode(String businessCode) {
        // 根据商机确定buildingNo
        BusinessOpportunityEntity opportunityEntity = businessOpportunityService.lambdaQuery()
                .eq(BusinessOpportunityEntity::getCode, businessCode)
                .last("limit 1").one();
        if (Objects.isNull(opportunityEntity) || StringUtils.isBlank(opportunityEntity.getBuildingNo())) {
            return null;
        }
        // 获取楼宇信息
        BuildingRatingEntity ratingEntity = this.lambdaQuery()
                .eq(BuildingRatingEntity::getBuildingNo, opportunityEntity.getBuildingNo())
                .last("limit 1").one();
        if (Objects.isNull(ratingEntity)) {
            return null;
        }
        // 获取核心区数据
        BuildingDetailsEntity detailsEntity = buildingDetailsDao.selectOne(Wrappers.<BuildingDetailsEntity>lambdaQuery()
                .select(BuildingDetailsEntity::getBuildingNo, BuildingDetailsEntity::getLocationName)
                .eq(BuildingDetailsEntity::getBuildingNo, opportunityEntity.getBuildingNo())
                .eq(BuildingDetailsEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .last("limit 1"));
        // 封装数据
        CmsBusinessDto businessDto = buildingRatingConvert.convertToCmsBusinessDto(ratingEntity);
        return businessDto
                .setCustomerId(opportunityEntity.getCustomerId())
                .setBusinessCode(opportunityEntity.getCode())
                .setBusinessName(opportunityEntity.getName())
                .setProjectLevel(StrUtil.isNotBlank(ratingEntity.getProjectReviewLevel())
                        ? ratingEntity.getProjectReviewLevel() : ratingEntity.getProjectLevel())
                .setLocationName(detailsEntity.getLocationName());
    }

    @Override
    public PageResponseVO<BuildingRating2WorkOrderVO> list2workOrder(PageRequestVO<BuildingSelectQuery> query) {
        Page<BuildingRatingEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        this.lambdaQuery()
                .select(BuildingRatingEntity::getId, BuildingRatingEntity::getBuildingName, BuildingRatingEntity::getMapCity,
                        BuildingRatingEntity::getBuildingNo, BuildingRatingEntity::getBuildingType,
                        BuildingRatingEntity::getSubmitUser)
                .like(StrUtil.isNotBlank(query.getQuery()
                        .getName()), BuildingRatingEntity::getBuildingName, query.getQuery().getName())
                .eq(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.AUDITED.getValue())
                .eq(StrUtil.isNotBlank(query.getQuery().getCity()), BuildingRatingEntity::getMapCity, query.getQuery()
                        .getCity())
                .eq(StrUtil.isNotBlank(query.getQuery()
                        .getSubmitUser()), BuildingRatingEntity::getSubmitUser, query.getQuery().getSubmitUser())
                .page(page);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            Map<String, CachedUser> userMap = userCacheHelper.getUserByWnos(page.getRecords().stream().map(BuildingRatingEntity::getSubmitUser).toList());
            List<BuildingRating2WorkOrderVO> row = page.getRecords().stream().map(record -> {
                BuildingRating2WorkOrderVO vo = BuildingRating2WorkOrderVO.builder()
                        .buildingNo(record.getBuildingNo())
                        .buildingName(record.getBuildingName())
                        .buildingType(record.getBuildingType()).city(record.getMapCity())
                        .buildingTypeName(BuildingRatingEntity.BuildingType.getNameByValue(record.getBuildingType()))
                        .build();
                if (StrUtil.isNotBlank(record.getSubmitUser())) {
                    CachedUser cachedUser = userMap.get(record.getSubmitUser());
                    if (Objects.nonNull(cachedUser)) {
                        vo.setFollowerName(record.getSubmitUser() + " (" + cachedUser.getName() + ")");
                        vo.setFollowerPhone(cachedUser.getMobile());
                    }
                }
                return vo;
            }).toList();
            return new PageResponseVO<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), row, page.getTotal());
        }
        return new PageResponseVO<>();
    }

    @Override
    public List<BuildingFollowerDTO> getBuildingFollowers(List<String> buildingNos) {
        return buildingRatingService.lambdaQuery()
                .select(BuildingRatingEntity::getBuildingNo, BuildingRatingEntity::getSubmitUser)
                .in(BuildingRatingEntity::getBuildingNo, buildingNos)
                .list()
                .stream()
                .filter(record -> StrUtil.isNotBlank(record.getSubmitUser()))
                .map(record -> BuildingFollowerDTO.builder()
                        .buildingNo(record.getBuildingNo())
                        .followerWno(record.getSubmitUser())
                        .build())
                .toList();
    }
}
