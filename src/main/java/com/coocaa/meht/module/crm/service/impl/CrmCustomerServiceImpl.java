package com.coocaa.meht.module.crm.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Sets;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.KeyValue;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.PermissionDTO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.constants.TopicConstants;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.common.handler.PermissionHandler;
import com.coocaa.meht.kafka.KafkaProducerService;
import com.coocaa.meht.module.building.entity.CompleteRatingEntity;
import com.coocaa.meht.module.building.service.CompleteRatingService;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpAddReq;
import com.coocaa.meht.module.crm.dto.req.CrmFollowUpListReq;
import com.coocaa.meht.module.crm.dto.req.CustomerListReq;
import com.coocaa.meht.module.crm.enums.BusinessTypeEnum;
import com.coocaa.meht.module.crm.enums.DataAccessTypeEnum;
import com.coocaa.meht.module.crm.enums.SceneTypeEnum;
import com.coocaa.meht.module.crm.service.CrmCustomerService;
import com.coocaa.meht.module.crm.vo.CustomerListVO;
import com.coocaa.meht.module.crm.vo.FollowUpVO;
import com.coocaa.meht.module.sys.entity.SysUserEntity;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dto.crm.CrmFieldResultDto;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingGeneEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.CustomerFollowRecordEntity;
import com.coocaa.meht.module.web.entity.FollowRecordPicEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.enums.FollowTypeEnum;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingGeneService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.CustomerFollowRecordService;
import com.coocaa.meht.module.web.service.FollowRecordPicService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.IBuildingStatusChangeLogService;
import com.coocaa.meht.module.web.vo.BusinessStatusChangeVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.dto.UserDataAccessV2DTO;
import com.coocaa.meht.utils.JsonUtils;
import com.coocaa.meht.utils.RsaExample;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * CRM 客户
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-25
 */
@Service
@Slf4j
public class CrmCustomerServiceImpl extends CrmBaseService implements CrmCustomerService {


    /**
     * 添加跟进记录
     */
    private static final String CUSTOMER_ADD_FOLLOWUP_URL = "/crmActivity/addCrmActivityRecord";

    /**
     * 修改跟进记录
     */
    private static final String CUSTOMER_UPDATE_FOLLOWUP_URL = "/crmActivity/updateActivityRecord";

    @Resource
    private BuildingRatingService buildingRatingService;

    @Resource
    private CustomerFollowRecordService customerFollowRecordService;

    @Autowired
    private IBuildingStatusChangeLogService changeLogService;

    @Resource
    private BusinessOpportunityService businessOpportunityService;

    @Resource
    private KafkaProducerService kafkaProducerService;

    @Resource
    private IBuildingMetaService buildingMetaService;

    @Resource
    private FeignCmsRpc feignCmsRpc;

    @Resource
    private FeignAuthorityRpc feignAuthorityRpc;

    @Resource
    private BuildingDetailsService buildingDetailsService;

    @Resource
    private RsaExample rsaExample;

    @Resource
    private SysUserService userService;

    @Resource
    private FollowRecordPicService followRecordPicService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private BuildingGeneService buildingGeneService;

    @Resource
    private CompleteRatingService completeRatingService;

    @Resource
    private PermissionHandler permissionHandler;

    @Override
    public PageResult<CustomerListVO> listCrmCustomer(CustomerListReq req) {
        PermissionDTO cmsPermission = permissionHandler.getCmsPermission(null, null);
        if (Objects.isNull(cmsPermission.getCities()) || Objects.isNull(cmsPermission.getUserCodes())) {
            // 没有符合权限的条件，直接返回空数据
            return new PageResult<>(Collections.emptyList(), 0);
        }

        Page<BuildingRatingEntity> page = new Page<>(req.getPage(), req.getLimit());
        Set<Integer> buildingStatus = Sets.newHashSet(BuildingRatingEntity.BuildingStatus.CONFIRMED.value, BuildingRatingEntity.BuildingStatus.CONFIRMING.value);
        Set<Integer> status = Sets.newHashSet(BuildingRatingEntity.Status.AUDITED.value, BuildingRatingEntity.Status.REJECTED.value, BuildingRatingEntity.Status.WAIT_AUDIT.value);

        // 处理toSeaTimeStart和toSeaTimeEnd
        if (StringUtils.isNotBlank(req.getToSeaTimeStart()) && StringUtils.isNotBlank(req.getToSeaTimeEnd())) {
            req.setToSeaTimeStart(req.getToSeaTimeStart() + " 00:00:00");
            req.setToSeaTimeEnd(req.getToSeaTimeEnd() + " 23:59:59");
        }

        LambdaQueryWrapper<BuildingRatingEntity> queryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda()
                .in(BuildingRatingEntity::getBuildingStatus, buildingStatus)
                .in(BuildingRatingEntity::getStatus, status)
                .eq(BuildingRatingEntity::getHighSeaFlag, BuildingRatingEntity.HighSeaFlagEnum.NO.getCode())
                .ge(StringUtils.isNotBlank(req.getToSeaTimeStart()), BuildingRatingEntity::getEnterSeaTime, req.getToSeaTimeStart())
                .le(StringUtils.isNotBlank(req.getToSeaTimeEnd()), BuildingRatingEntity::getEnterSeaTime, req.getToSeaTimeEnd())
                .like(StringUtils.isNotBlank(req.getSearch()), BuildingRatingEntity::getBuildingName, req.getSearch())
                .in(BuildingRatingEntity::getMapCity, cmsPermission.getCities())
                .orderByDesc(BaseEntity::getCreateTime);

        if (req.getSceneType().equals(SceneTypeEnum.MY_CUSTOMERS.name())) {
            queryWrapper.eq(BuildingRatingEntity::getSubmitUser, UserThreadLocal.getUser().getWno());
        } else if (req.getSceneType().equals(SceneTypeEnum.SUBORDINATE_CUSTOMERS.name())) {
            List<String> userCodes = cmsPermission.getUserCodes();
            if (CollUtil.isNotEmpty(userCodes)) {
                // 排除自己
                userCodes.remove(UserThreadLocal.getUser().getWno());
                if (CollUtil.isEmpty(userCodes)) {
                    // 为空，表示没有符合条件的下属
                    return new PageResult<>(Collections.emptyList(), 0);
                }
                queryWrapper.in(BuildingRatingEntity::getSubmitUser, userCodes);
            } else {
                // 为空表示有全部的权限，只排除自己
                queryWrapper.ne(BuildingRatingEntity::getSubmitUser, UserThreadLocal.getUser().getWno());
            }
        } else if (req.getSceneType().equals(SceneTypeEnum.ALL_CUSTOMERS.name())) {
            List<String> userCodes = cmsPermission.getUserCodes();
            queryWrapper.in(CollectionUtil.isNotEmpty(userCodes), BuildingRatingEntity::getSubmitUser, cmsPermission.getUserCodes());
        }

        Page<BuildingRatingEntity> entityPage = buildingRatingService.page(page, queryWrapper);
        if (CollUtil.isEmpty(entityPage.getRecords())) {
            return new PageResult<>(Collections.emptyList(), 0);
        }

        List<CustomerListVO> customerList = new ArrayList<>(entityPage.getRecords().size());
        List<String> buildingNoList = entityPage.getRecords().stream()
                .map(BuildingRatingEntity::getBuildingNo)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<BuildingDetailsEntity> detailsEntityList = buildingDetailsService.listDetailsByBuildingNo(buildingNoList);
        Map<String, BuildingDetailsEntity> detailsMap = new HashMap<>();
        if (CollUtil.isNotEmpty(detailsEntityList)) {
            detailsMap = detailsEntityList.stream().collect(Collectors.toMap(BuildingDetailsEntity::getBuildingNo, Function.identity(), (k1, k2) -> k2));
        }
        //填充楼宇主数据名称
        List<BuildingMetaEntity> metaEntities = buildingMetaService.lambdaQuery()
                .select(BuildingMetaEntity::getBuildingRatingNo, BuildingMetaEntity::getBuildingName, BuildingMetaEntity::getProjectLevel)
                .in(BuildingMetaEntity::getBuildingRatingNo, buildingNoList).list();

        Map<String, BuildingMetaEntity> collect = metaEntities.stream().collect(Collectors.toMap(BuildingMetaEntity::getBuildingRatingNo, e -> e));

        // 查询基因表规格
        Map<String, BuildingGeneEntity> geneMapping = buildingGeneService.lambdaQuery()
                .select(BuildingGeneEntity::getBuildingRatingNo, BuildingGeneEntity::getSpec)
                .in(BuildingGeneEntity::getBuildingRatingNo, buildingNoList)
                .list()
                .stream()
                .collect(Collectors.toMap(BuildingGeneEntity::getBuildingRatingNo, Function.identity()));

        Set<String> userCode = entityPage.getRecords().stream().map(BuildingRatingEntity::getSubmitUser).collect(Collectors.toSet());
        Map<String, LoginUser> users = userService.getUsers(userCode);

        //完善编码
        List<CompleteRatingEntity> completeRatingEntities = completeRatingService.lambdaQuery()
                .select(CompleteRatingEntity::getBuildingRatingNo, CompleteRatingEntity::getStatus, CompleteRatingEntity::getCompleteRatingNo)
                .in(CompleteRatingEntity::getBuildingRatingNo, buildingNoList)
                .in(CompleteRatingEntity::getStatus, BuildingRatingEntity.Status.DRAFT.getValue(), BuildingRatingEntity.Status.WAIT_AUDIT.getValue())
                .list();

        //草稿
        Map<String, String> draftCompletemap = completeRatingEntities.stream()
                .filter(e -> e.getStatus().equals(BuildingRatingEntity.Status.DRAFT.getValue()))
                .collect(Collectors.toMap(
                        CompleteRatingEntity::getBuildingRatingNo,
                        CompleteRatingEntity::getCompleteRatingNo,
                        (existing, replacement) -> replacement
                ));

        //审核中
        Map<String, String> waitAuditCompletemap = completeRatingEntities.stream()
                .filter(e -> e.getStatus().equals(BuildingRatingEntity.Status.WAIT_AUDIT.getValue()))
                .collect(Collectors.toMap(
                        CompleteRatingEntity::getBuildingRatingNo,
                        CompleteRatingEntity::getCompleteRatingNo,
                        (existing, replacement) -> replacement
                ));

        for (BuildingRatingEntity record : entityPage.getRecords()) {
            CustomerListVO customerListVO = new CustomerListVO();
            customerListVO.setId(record.getId());
            customerListVO.setBuildingNo(record.getBuildingNo());
            customerListVO.setBuildingTypeName(BuildingRatingEntity.BuildingType.getNameByValue(record.getBuildingType()));
            customerListVO.setCity(record.getMapCity());

            BuildingDetailsEntity buildingDetailsEntity = detailsMap.get(record.getBuildingNo());
            customerListVO.setCreateUserName(Objects.nonNull(users.get(record.getSubmitUser())) ? users.get(record.getSubmitUser()).getUserName() : "");
            customerListVO.setBuildingNumber(Objects.nonNull(buildingDetailsEntity) && StringUtils.isNotEmpty(buildingDetailsEntity.getBuildingNumberInput()) ? Long.valueOf(buildingDetailsEntity.getBuildingNumberInput()) : null);
            customerListVO.setBuildingPrice(Objects.nonNull(buildingDetailsEntity) && StringUtils.isNotEmpty(buildingDetailsEntity.getBuildingPriceInput()) ? Long.valueOf(buildingDetailsEntity.getBuildingPriceInput()) : null);
            customerListVO.setCreateBy(record.getSubmitUser());
            customerListVO.setCustomerName(Objects.nonNull(collect.get(record.getBuildingNo())) ? collect.get(record.getBuildingNo()).getBuildingName() : "");
            customerListVO.setLevel(Objects.nonNull(collect.get(record.getBuildingNo())) ? collect.get(record.getBuildingNo()).getProjectLevel() : "");
            customerListVO.setFieldCakork(rsaExample.decryptByPrivate(record.getMapAddress()));
            // 是否显示完善评级标识
            customerListVO.setImproveRatingFlag(isImproveRatingFlag(record, waitAuditCompletemap));
            customerListVO.setStatus(record.getStatus());
            customerListVO.setBuildingType(record.getBuildingType());
            customerListVO.setEnterSeaTime(record.getEnterSeaTime());
            customerListVO.setRatingVersion(record.getRatingVersion());

            //完善评级编码
            customerListVO.setCompleteRatingNo(draftCompletemap.get(record.getBuildingNo()));
            customerList.add(customerListVO);
        }
        return new PageResult<>(customerList, entityPage.getTotal());
    }

    public boolean isImproveRatingFlag(BuildingRatingEntity record, Map<String, String> waitAuditCompleteMapping) {
        boolean improveRatingFlag = false;
        if (!record.getBuildingType().equals(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue())
        && record.getSmallScreenRatingFlag().equals(0)) {
            improveRatingFlag = true;
        } else if (record.getBuildingType().equals(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue())
        && record.getSmallScreenRatingFlag().equals(0) && record.getLargeScreenRatingFlag().equals(0)) {
            improveRatingFlag = true;
        }else if (record.getBuildingType().equals(BuildingRatingEntity.BuildingType.OFFICE_BUILDING.getValue())
        && record.getSmallScreenRatingFlag().equals(1) && StringUtils.isBlank(waitAuditCompleteMapping.get(record.getBuildingNo()))
        && record.getLargeScreenRatingFlag().equals(0)){
            improveRatingFlag = true;
        }
        return improveRatingFlag;
    }

    @Override
    public PageResult<CustomerListVO> customerList(CustomerListReq req) {
        Page<BuildingRatingEntity> page = new Page<>(req.getPage(), req.getLimit());
        Set<Integer> status = Sets.newHashSet(BuildingRatingEntity.Status.AUDITED.value);

        LambdaQueryWrapper<BuildingRatingEntity> queryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda()
                .eq(BuildingRatingEntity::getSubmitUser, UserThreadLocal.getUser().getWno())
                .in(BuildingRatingEntity::getStatus, status)
                .eq(BuildingRatingEntity::getHighSeaFlag, BuildingRatingEntity.HighSeaFlagEnum.NO.getCode())
                .like(StringUtils.isNotBlank(req.getSearch()), BuildingRatingEntity::getBuildingName, req.getSearch())
                .orderByDesc(BaseEntity::getCreateTime);

        Page<BuildingRatingEntity> entityPage = buildingRatingService.page(page, queryWrapper);
        if (CollUtil.isEmpty(entityPage.getRecords())) {
            return new PageResult<>(Collections.emptyList(), 0);
        }

        List<CustomerListVO> customerList = new ArrayList<>(entityPage.getRecords().size());
        List<String> buildingNoList = entityPage.getRecords().stream().map(BuildingRatingEntity::getBuildingNo).collect(Collectors.toList());

        //填充楼宇主数据名称
        List<BuildingMetaEntity> metaEntities = buildingMetaService.lambdaQuery()
                .select(BuildingMetaEntity::getBuildingRatingNo, BuildingMetaEntity::getBuildingName)
                .in(BuildingMetaEntity::getBuildingRatingNo, buildingNoList).list();

        Map<String, BuildingMetaEntity> collect = metaEntities.stream().collect(Collectors.toMap(BuildingMetaEntity::getBuildingRatingNo, e -> e));

        for (BuildingRatingEntity record : entityPage.getRecords()) {
            CustomerListVO customerListVO = new CustomerListVO();
            customerListVO.setId(record.getId());
            customerListVO.setBuildingNo(record.getBuildingNo());
            customerListVO.setCustomerName(Objects.nonNull(collect.get(record.getBuildingNo())) ? collect.get(record.getBuildingNo()).getBuildingName() : "");
            customerListVO.setStatus(record.getStatus());
            customerListVO.setBuildingType(record.getBuildingType());
            customerList.add(customerListVO);
        }
        return new PageResult<>(customerList, entityPage.getTotal());
    }

    @Override
    public PageResult<FollowUpVO> listCrmFollowup(CrmFollowUpListReq req) {
        Page<FollowUpVO> customerFollowRecord = customerFollowRecordService.getCustomerFollowRecord(req);
        PageResult<FollowUpVO> followUpDtoPageResult = new PageResult<>(customerFollowRecord.getRecords(), customerFollowRecord.getTotal());
        // 查询跟进图片
        if (CollUtil.isNotEmpty(followUpDtoPageResult.getList())) {
            List<Integer> ids = followUpDtoPageResult.getList().stream().mapToInt(FollowUpVO::getId).boxed().toList();
            Map<Integer, List<FollowRecordPicEntity>> map = followRecordPicService.lambdaQuery()
                    .in(FollowRecordPicEntity::getFollowId, ids)
                    .list().stream().collect(Collectors.groupingBy(FollowRecordPicEntity::getFollowId));
            for (FollowUpVO followUpVO : followUpDtoPageResult.getList()) {
                followUpVO.setCustomerFollowRecordPicList(map.get(followUpVO.getId()));
            }
        }
        return followUpDtoPageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateCrmFollowUp(CrmFollowUpAddReq req) {
        CustomerFollowRecordEntity customerFollowRecordEntity = new CustomerFollowRecordEntity();
        customerFollowRecordEntity.setVisitType("面访".equals(req.getCategory())
                ? FollowTypeEnum.INTERVIEW.name()
                : FollowTypeEnum.PHONE.name());
        customerFollowRecordEntity.setBusinessCode(req.getBusinessCode());
        customerFollowRecordEntity.setVisitPurpose(req.getContent());
        customerFollowRecordEntity.setPhone(req.getPhone());
        customerFollowRecordEntity.setRole(req.getRole());
        for (KeyValue<String, String> keyValue : req.getParamList()) {
            String key = keyValue.getKey();
            String value = keyValue.getValue();
            if ("沟通时间".equals(key)) {
                // 定义日期时间格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String[] split = value.split(":");
                if (split.length == 2) {
                    value = value + ":00";
                }
                // 将字符串解析为 LocalDateTime
                LocalDateTime localDateTime = LocalDateTime.parse(value, formatter);
                customerFollowRecordEntity.setVisitTime(localDateTime);
            } else if ("拜访对象".equals(key)) {
                customerFollowRecordEntity.setVisitObjects(value);
            } else if ("沟通结果".equals(key)) {
                customerFollowRecordEntity.setVisitResult(value);
            }
        }
        // 跟进方式为电话时,图片非必填
        if (FollowTypeEnum.INTERVIEW.name().equals(customerFollowRecordEntity.getVisitType())
                && CollUtil.isEmpty(req.getCustomerFollowRecordPicList())) {
            throw new ServerException("面访跟进必须上传图片");
        }
        // 设置代理或自营
        setBusinessType(customerFollowRecordEntity);
        // 设置是否有效跟进
        setValid(customerFollowRecordEntity);

        BusinessOpportunityEntity businessOpportunity = businessOpportunityService.lambdaQuery()
                .eq(BusinessOpportunityEntity::getCode, req.getBusinessCode())
                .one();
        if (Objects.isNull(req.getId())) {
            if (BusinessChangeStatusEnum.CLOSE.getCode().equals(businessOpportunity.getStatus())) {
                throw new ServerException("该商机已关闭,不能进行跟进");
            }
            //保存跟进记录
            customerFollowRecordService.save(customerFollowRecordEntity);
            //保存跟进图片
            saveCustomerFollowRecordPic(req.getCustomerFollowRecordPicList(), customerFollowRecordEntity.getId(), customerFollowRecordEntity.getVisitType());
            //发商机变更消息
            long count = customerFollowRecordService.count(Wrappers.<CustomerFollowRecordEntity>lambdaQuery()
                    .eq(CustomerFollowRecordEntity::getBusinessCode, req.getBusinessCode()));
            // 商机变更记录
            if (count == 1 && BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode().equals(businessOpportunity.getStatus())) {
                CachedUser user = UserThreadLocal.getUser();
                BusinessStatusChangeVO businessStatusChangeVO = new BusinessStatusChangeVO();
                businessStatusChangeVO.setBusinessCode(req.getBusinessCode());
                businessStatusChangeVO.setStatus(BusinessChangeStatusEnum.PRELIMINARY_NEGOTIATIONS.getCode());
                businessStatusChangeVO.setOperatorId(user.getId());
                businessStatusChangeVO.setOperatorWno(user.getWno());
                businessStatusChangeVO.setOperatorName(user.getName());
                kafkaProducerService.sendMessageAfterCommit(TopicConstants.BUSINESS_STATUS_CHANGE, JSONObject.toJSONString(businessStatusChangeVO));
            }
            return;
        }
        // 校验 只可编辑和删除拜访日期是今天的数据
        checkIsToday(req.getId(), "只可编辑拜访日期是今天的数据");


        //修改本地库
        customerFollowRecordService.lambdaUpdate()
                .set(CustomerFollowRecordEntity::getBusinessCode, customerFollowRecordEntity.getBusinessCode())
                .set(CustomerFollowRecordEntity::getVisitResult, customerFollowRecordEntity.getVisitResult())
                .set(CustomerFollowRecordEntity::getVisitTime, customerFollowRecordEntity.getVisitTime())
                .set(CustomerFollowRecordEntity::getVisitObjects, customerFollowRecordEntity.getVisitObjects())
                .set(CustomerFollowRecordEntity::getVisitPurpose, customerFollowRecordEntity.getVisitPurpose())
                .set(CustomerFollowRecordEntity::getVisitType, customerFollowRecordEntity.getVisitType())
                .set(CustomerFollowRecordEntity::getValid, customerFollowRecordEntity.getValid())
                .eq(CustomerFollowRecordEntity::getId, req.getId())
                .update();

        //修改跟进图片
        updateFollowPic(req, customerFollowRecordEntity);
    }

    private void updateFollowPic(CrmFollowUpAddReq req, CustomerFollowRecordEntity customerFollowRecordEntity) {
        LambdaQueryWrapper<FollowRecordPicEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(FollowRecordPicEntity::getFollowId, req.getId());
        followRecordPicService.remove(queryWrapper);
        saveCustomerFollowRecordPic(req.getCustomerFollowRecordPicList(), req.getId(), customerFollowRecordEntity.getVisitType());
    }

    private static void setValid(CustomerFollowRecordEntity customerFollowRecordEntity) {

        if (FollowTypeEnum.INTERVIEW.name().equals(customerFollowRecordEntity.getVisitType())) {
            customerFollowRecordEntity.setValid(BooleFlagEnum.YES.getCode());
        } else {
            customerFollowRecordEntity.setValid(BooleFlagEnum.NO.getCode());
        }
    }

    private void setBusinessType(CustomerFollowRecordEntity customerFollowRecordEntity) {
        SysUserEntity user = sysUserService.getByCode(UserThreadLocal.getUser().getWno());
        if (Objects.nonNull(user)) {
            customerFollowRecordEntity.setBusinessType(BusinessTypeEnum.SELF.getCode());
        } else {
            customerFollowRecordEntity.setBusinessType(BusinessTypeEnum.PROXY.getCode());
        }
    }

    private void checkIsToday(Integer customerFollowRecordId, String msg) {
        CustomerFollowRecordEntity customerFollowRecordEntity = customerFollowRecordService.getById(customerFollowRecordId);
        if (customerFollowRecordEntity == null) {
            throw new ServerException("当前跟进记录已不存在");
        }
        // 比较两个日期是否在同一天
        boolean sameDay = LocalDateTimeUtil.isSameDay(customerFollowRecordEntity.getCreateTime(), LocalDateTime.now());
        if (!sameDay) {
            throw new ServerException(msg);
        }
    }

    private void saveCustomerFollowRecordPic(List<String> picList, Integer followRecordId, String visitType) {

        if (FollowTypeEnum.INTERVIEW.getDesc().equals(FollowTypeEnum.valueOf(visitType).getDesc())
                && CollUtil.isEmpty(picList)) {
            throw new ServerException("请上传跟进图片");
        }
        if (CollUtil.isEmpty(picList)) {
            return;
        }
        List<FollowRecordPicEntity> customerFollowRecordPicEntities = picList.stream()
                .map(item -> new FollowRecordPicEntity().setFollowId(followRecordId).setPicUrl(item)).toList();
        followRecordPicService.saveBatch(customerFollowRecordPicEntities);
    }

    @Override
    public void addBusinessChangeLog(Integer businessId, String businessCode, String status) {
        BuildingStatusChangeLogEntity changeLogEntity = new BuildingStatusChangeLogEntity();
        changeLogEntity.setBizId(businessId.longValue());
        changeLogEntity.setBizCode(businessCode);
        changeLogEntity.setType(BuildingStatusChangeLogEntity.BizType.BUSINESS.getCode());
        changeLogEntity.setStatus(status);
        changeLogEntity.setChangeTime(LocalDateTime.now());

        // 查询用户id
        CachedUser user = UserThreadLocal.getUser();
        changeLogEntity.setOperatorWno(user.getWno());
        changeLogEntity.setOperatorName(user.getName());
        changeLogEntity.setOperator(Optional.ofNullable(user.getId()).map(Long::valueOf).orElse(null));

        // 逻辑删除老数据后新增
        changeLogService.update(new LambdaUpdateWrapper<BuildingStatusChangeLogEntity>()
                .eq(BuildingStatusChangeLogEntity::getBizId, changeLogEntity.getBizId())
                .eq(BuildingStatusChangeLogEntity::getType, changeLogEntity.getType())
                .eq(BuildingStatusChangeLogEntity::getStatus, changeLogEntity.getStatus())
                .eq(BuildingStatusChangeLogEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .set(BuildingStatusChangeLogEntity::getDeleteFlag, BooleFlagEnum.YES.getCode()));
        changeLogService.save(changeLogEntity);
    }

    @Override
    public void deleteCrmFollowUp(Integer id) {
        log.info("[deleteCrmFollowUp][删除跟进记录,跟进记录id:{}]", id);
        checkIsToday(id, "只可删除拜访日期是今天的数据");
        customerFollowRecordService.lambdaUpdate()
                .eq(CustomerFollowRecordEntity::getId, id)
                .set(CustomerFollowRecordEntity::getDeleted, true)
                .update();
    }

    @Override
    public Result<List<KeyValue<String, String>>> listQueryScenes() {
        return Result.ok(super.listQueryScenes("2", Sets.newHashSet("全部", "我负责", "下属负责")));
    }


    /**
     * 根据字段名获取字段信息
     *
     * @param fieldName
     * @return
     */
    private CrmFieldResultDto.DataDto getFieldByName(List<CrmFieldResultDto.DataDto> fieldList, String fieldName) {
        List<CrmFieldResultDto.DataDto> list = fieldList.stream().filter(a -> fieldName.equals(a.getName())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public void deleteFollowupPic(Integer id) {
        FollowRecordPicEntity followRecordPicEntity = followRecordPicService.getById(id);
        if (followRecordPicEntity == null) {
            throw new ServerException("当前跟进图片已不存在");
        }
        checkIsToday(followRecordPicEntity.getFollowId(), "只可删除拜访日期是今天的数据");

        followRecordPicService.removeById(id);
    }

    @Override
    public List<FollowRecordPicEntity> getFollowupPic(Integer id) {
        return followRecordPicService.lambdaQuery().eq(FollowRecordPicEntity::getFollowId, id).list();
    }
}
