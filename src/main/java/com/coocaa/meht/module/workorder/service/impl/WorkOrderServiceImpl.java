package com.coocaa.meht.module.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.PermissionDTO;
import com.coocaa.meht.common.bean.RpcUtils;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.common.handler.PermissionHandler;
import com.coocaa.meht.converter.ConverterFactory;
import com.coocaa.meht.module.web.dao.PointMapper;
import com.coocaa.meht.module.web.dto.point.PointDTO;
import com.coocaa.meht.module.web.dto.point.PointDetail;
import com.coocaa.meht.module.web.dto.point.UpdateTreeParam;
import com.coocaa.meht.module.web.dto.point.WaitingHallDTO;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.PointEntity;
import com.coocaa.meht.module.web.enums.PointNodeTypeEnum;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.PointService;
import com.coocaa.meht.module.web.service.WaitingHallService;
import com.coocaa.meht.module.web.vo.common.UserVO;
import com.coocaa.meht.module.workorder.dto.BuildingWorkOrderDetailQueryDTO;
import com.coocaa.meht.module.workorder.dto.OrderPointUpdateDTO;
import com.coocaa.meht.module.workorder.dto.PointCreateCheckQueryDTO;
import com.coocaa.meht.module.workorder.dto.PointListQueryDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderCancelRequestDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderNoticeDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderPageQueryDTO;
import com.coocaa.meht.module.workorder.service.WorkOrderService;
import com.coocaa.meht.module.workorder.vo.BuildingDetailVO;
import com.coocaa.meht.module.workorder.vo.PointCreateCheckVO;
import com.coocaa.meht.module.workorder.vo.PointResultVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderBuildingVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderCancelResultVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderDetailVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.FeignSspRpc;
import com.coocaa.meht.rpc.FeignWorkOrderRpc;
import com.coocaa.meht.rpc.dto.ContractWithPointsVO;
import com.coocaa.meht.rpc.dto.UserFeiShuMessageParam;
import com.coocaa.meht.rpc.dto.WorkOrderPageResult;
import com.coocaa.meht.utils.RsaExample;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工单功能服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WorkOrderServiceImpl implements WorkOrderService {

    /**
     * 飞书应用编码（字典0124）
     */
    private static final String APP_CODE = "0124-1";

    private final FeignWorkOrderRpc workOrderRpc;
    private final FeignCmsRpc cmsRpc;
    private final FeignSspRpc sspRpc;
    private final ConverterFactory converterFactory;
    private final PermissionHandler permissionHandler;
    private final BuildingRatingService buildingRatingService;
    private final PointMapper pointMapper;
    private final UserCacheHelper userCacheHelper;
    private final FeignAuthorityRpc authorityRpc;
    private final WaitingHallService waitingHallService;
    private final PointService pointService;
    private final RsaExample rsaExample;

    @Override
    public List<PointResultVO> create(WorkOrderDTO workOrderDTO) {
        workOrderDTO.setPointCount(workOrderDTO.getPointDetailList().size());
        fillBuildingInfo(workOrderDTO);
        log.info("创建工单参数：{}", JSON.toJSONString(workOrderDTO));
        Result<List<PointResultVO>> result = workOrderRpc.addInstallRemoveWorkOrder(workOrderDTO);
        log.info("创建工单结果：{}", JSON.toJSONString(result));
        return result.getData();
    }

    private void fillBuildingInfo(WorkOrderDTO workOrderDTO) {
        BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(workOrderDTO.getBuildingMetaNo());
        if (buildingRating == null) {
            throw new ServerException("未找到该楼宇信息");
        }
        workOrderDTO.setBuildingName(buildingRating.getBuildingName());
        workOrderDTO.setBuildingType(BuildingRatingEntity.BuildingType.getNameByValue(buildingRating.getBuildingType()));
        if (StrUtil.isNotBlank(buildingRating.getMapCity())) {
            CodeNameVO city = RpcUtils.unBox(authorityRpc.getByCityName(buildingRating.getMapCity()));
            workOrderDTO.setCityId(city.getId());
            workOrderDTO.setCityName(city.getName());
        }
        if (StrUtil.isNotBlank(buildingRating.getSubmitUser())) {
            workOrderDTO.setFollowerWno(buildingRating.getSubmitUser());
            workOrderDTO.setFollowerName(userCacheHelper.getUser(buildingRating.getSubmitUser()).getName());
        }
        if (StrUtil.isNotBlank(buildingRating.getMapAddress())) {
            workOrderDTO.setAddress(rsaExample.decryptByPrivate(buildingRating.getMapAddress()));
        }
    }

    @Override
    public WorkOrderPageResult<WorkOrderBuildingVO> list(WorkOrderPageQueryDTO workOrderPageQueryDTO) {
        PermissionDTO cmsPermission = permissionHandler.getCmsPermission();
        workOrderPageQueryDTO.getQuery().setCityIdList(cmsPermission.getCityIds());
        workOrderPageQueryDTO.getQuery().setFollowerWnoList(cmsPermission.getUserCodes());
        log.info("查询工单列表参数：{}", JSON.toJSONString(workOrderPageQueryDTO));
        Result<WorkOrderPageResult<WorkOrderBuildingVO>> result = workOrderRpc.pageQueryBuildingWorkOrders(workOrderPageQueryDTO);
        log.info("查询工单列表结果：{}", JSON.toJSONString(result));
        return result.getData();
    }

    @Override
    public BuildingDetailVO buildingDetail(String buildingMetaNo, String workOrderType) {
        BuildingWorkOrderDetailQueryDTO dto = BuildingWorkOrderDetailQueryDTO.builder()
                .workOrderType(workOrderType).buildingMetaNo(buildingMetaNo).build();
        log.info("查询工单楼宇详情参数：{}", JSON.toJSONString(dto));
        BuildingDetailVO data = workOrderRpc.getBuildingWorkOrderDetail(dto).getData();
        log.info("查询工单楼宇详情结果：{}", JSON.toJSONString(data));
        BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(buildingMetaNo);
        if (buildingRating != null && !UserThreadLocal.getUser().getWno().equals(buildingRating.getSubmitUser())) {
            data.setCanRevoke(false);
        }

        return data;
    }

    @Override
    public WorkOrderDetailVO workOrderDetail(String workOrderNo) {
        log.info("查询工单详情参数：{}", workOrderNo);
        Result<WorkOrderDetailVO> workOrderDetail = workOrderRpc.getWorkOrderDetail(workOrderNo);
        log.info("查询工单详情结果：{}", JSON.toJSONString(workOrderDetail));
        return workOrderDetail.getData();
    }

    @Override
    public boolean noticeWorkOrderFinished(WorkOrderNoticeDTO workOrderNoticeDTO) {

        try {
            String followerWno = workOrderNoticeDTO.getFollowerWno();
            if (StringUtils.isBlank(followerWno)) {
                log.warn("工单通知失败，关注人WNO为空，工单信息: {}", JSON.toJSONString(workOrderNoticeDTO));
                return false;
            }

            List<UserVO> userVOS = RpcUtils.unBox(authorityRpc.listUserByWnos(Collections.singletonList(followerWno)));
            if (CollectionUtils.isEmpty(userVOS)) {
                log.warn("工单通知失败，未找到关注人，WNO: {}, 工单ID: {}", followerWno, workOrderNoticeDTO.getWorkOrderNo());
                return false;
            }

            UserFeiShuMessageParam param = new UserFeiShuMessageParam();
            param.setAppCode(APP_CODE);
            param.setTitle("工单状态变更通知");
            param.setContent(buildMessageContent(workOrderNoticeDTO));
            // 支持多用户通知
            param.setReceiveUserIds(userVOS.stream().map(UserVO::getId).collect(Collectors.toSet()));

            authorityRpc.sendFeishuMessage(param);
            return true;
        } catch (Exception e) {
            log.error("工单完成通知异常, 工单ID: {}, 错误信息: {}", workOrderNoticeDTO.getWorkOrderNo(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建飞书消息内容
     *
     * @param workOrderNoticeDTO 工单信息
     * @return 工单通知信息   如：你发起的「项目名称：XX；点位编码：XX」的【安装/拆除】工单，当前已经「完成」，请及时跟进后续事务处理
     */
    private String buildMessageContent(WorkOrderNoticeDTO workOrderNoticeDTO) {
        String projectName = workOrderNoticeDTO.getBuildingName();
        String pointCode = workOrderNoticeDTO.getPointCode();
        Integer workOrderType = workOrderNoticeDTO.getWorkOrderType();

        // 工单类型 0安装工单 1拆除工单
        String workOrderTypeName = switch (workOrderType) {
            case 0 -> "安装";
            case 1 -> "拆除";
            default -> throw new BusinessException("未知工单类型");
        };

        String workOrderStatus = workOrderNoticeDTO.getWorkOrderStatusName();

        return "你发起的「项目名称："
                + projectName + "，点位编码："
                + pointCode + "」的【"
                + workOrderTypeName + "】工单，当前已经「"
                + workOrderStatus + "」，请及时跟进后续事务处理";
    }


    @Override
    public List<ContractWithPointsVO> projectContractPoints(String buildingNo, String workOrderType) {
        List<ContractWithPointsVO> contractWithPoints = RpcUtils.unBox(cmsRpc.queryProjectContractPoints(buildingNo));
        if (CollUtil.isEmpty(contractWithPoints)) {
            return List.of();
        }

        // 获取楼宇下所有点位Map
        Map<String, PointDetail> allPointMap = getBuildingPointMap(buildingNo);
        if (allPointMap.isEmpty()) {
            return List.of();
        }

        // 填充合同点位详情
        fillPointDetail(contractWithPoints, allPointMap);

        // 获取所有点位详情进行统一处理
        List<PointDetail> allPointDetails = contractWithPoints.stream()
                .filter(Objects::nonNull)
                .map(ContractWithPointsVO::getPointDetails)
                .filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 处理点位：检查安装状态、排序、字典转换
        processPointsWithInstallCheck(allPointDetails, workOrderType);

        return contractWithPoints;
    }

    @Override
    public List<PointDetail> queryPointDetails(PointListQueryDTO queryDTO) {
        if (queryDTO == null || CollUtil.isEmpty(queryDTO.getPointCodes())) {
            log.warn("查询点位详情参数为空");
            return List.of();
        }

        log.info("查询点位详情，楼宇编码: {}, 点位数量: {}", queryDTO.getBuildingNo(), queryDTO.getPointCodes().size());

        // 获取楼宇下所有点位Map
        Map<String, PointDetail> pointMap = getBuildingPointMap(queryDTO.getBuildingNo());
        if (pointMap.isEmpty()) {
            return List.of();
        }

        // 根据请求的点位编码过滤点位详情
        List<PointDetail> filteredPoints = queryDTO.getPointCodes().stream()
                .filter(Objects::nonNull)
                .map(pointMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(filteredPoints)) {
            log.warn("未找到匹配的点位详情，楼宇编码: {}", queryDTO.getBuildingNo());
            return List.of();
        }

        // 处理点位：检查安装状态、排序、字典转换
        processPointsWithInstallCheck(filteredPoints, queryDTO.getWorkOrderType());

        converterFactory.convert(filteredPoints);
        log.info("查询点位详情完成，返回点位数量: {}", filteredPoints.size());
        return filteredPoints;
    }

    @Override
    public List<WorkOrderCancelResultVO> cancelWorkOrders(WorkOrderCancelRequestDTO request) {
        BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(request.getBuildingNo());
        if (buildingRating == null) {
            log.warn("未找到楼宇信息，楼宇编码: {}", request.getBuildingNo());
            throw new ServerException("未找到楼宇信息");
        }
        if (!UserThreadLocal.getUser().getWno().equals(buildingRating.getSubmitUser())) {
            throw new ServerException("不是当前楼宇负责人  无法撤回");
        }
        log.info("取消工单，参数: {}", request);
        Result<List<WorkOrderCancelResultVO>> result = workOrderRpc.cancelWorkOrders(request);
        log.info("取消工单，结果: {}", result);
        return result.getData();
    }

    @Override
    public void workOrderPointEdit(OrderPointUpdateDTO param) {
        //更新楼栋
        if (!Objects.equals(param.getBuildingName(), param.getBuildingOriginVale())) {
            UpdateTreeParam updateTreeBuildingParam = new UpdateTreeParam();
            updateTreeBuildingParam.setType(PointNodeTypeEnum.BUILDING.getCode());
            updateTreeBuildingParam.setBuildingName(param.getBuildingName());
            updateTreeBuildingParam.setOriginVale(param.getBuildingOriginVale());
            updateTreeBuildingParam.setBuildingRatingNo(param.getBuildingRatingNo());
            waitingHallService.updateTreeNode(updateTreeBuildingParam);
        }


        //更新单元
        if (!Objects.equals(param.getUnitName(), param.getUnitOriginVale())) {
            UpdateTreeParam updateTreeUnitParam = new UpdateTreeParam();
            updateTreeUnitParam.setType(PointNodeTypeEnum.UNIT.getCode());
            updateTreeUnitParam.setUnitName(param.getUnitName());
            updateTreeUnitParam.setOriginVale(param.getUnitOriginVale());
            updateTreeUnitParam.setBuildingRatingNo(param.getBuildingRatingNo());
            waitingHallService.updateTreeNode(updateTreeUnitParam);
        }


        //更新楼层
        if (!Objects.equals(param.getFloor(), param.getFloorOriginVale())) {
            UpdateTreeParam updateTreeFloorParam = new UpdateTreeParam();
            updateTreeFloorParam.setType(PointNodeTypeEnum.FLOOR.getCode());
            updateTreeFloorParam.setFloorName(param.getFloor());
            updateTreeFloorParam.setOriginVale(param.getFloorOriginVale());
            updateTreeFloorParam.setBuildingRatingNo(param.getBuildingRatingNo());
            waitingHallService.updateTreeNode(updateTreeFloorParam);
        }


        // 更新等候厅
        WaitingHallDTO waitingHallDTO = getWaitingHallDTO(param);
        waitingHallService.handleWaitingHall(waitingHallDTO);

        //更新点位
        PointDTO pointDTO = getPointDTO(param);
        pointService.handlePoint(pointDTO);

    }

    private WaitingHallDTO getWaitingHallDTO(OrderPointUpdateDTO param) {
        WaitingHallDTO waitingHallDTO = new WaitingHallDTO();
        waitingHallDTO.setBuildingRatingNo(param.getBuildingRatingNo());
        waitingHallDTO.setWaitingHall(param.getWaitingHallName());
        waitingHallDTO.setWaitingHallType(param.getWaitingHallType());
        waitingHallDTO.setWaitingHallId(param.getWaitingHallId());
        waitingHallDTO.setBuildingName(param.getBuildingName());
        waitingHallDTO.setUnitName(param.getUnitName());
        waitingHallDTO.setFloor(param.getFloor());
        return waitingHallDTO;
    }

    private PointDTO getPointDTO(OrderPointUpdateDTO param) {
        PointDTO updatePointParam = new PointDTO();
        updatePointParam.setDescription(param.getPointRemark());
        updatePointParam.setPointId(param.getPointId());
        updatePointParam.setWaitingHallId(param.getWaitingHallId());
        updatePointParam.setImages(param.getPointPics());
        updatePointParam.setDeviceSize(param.getDeviceSize());
        updatePointParam.setCode(param.getPointCode());
        return updatePointParam;
    }


    /**
     * 检查点位安装状态
     *
     * @param pointDetails  点位详情列表
     * @param workOrderType 工单类型
     */
    private void checkPointInstallStatus(List<PointDetail> pointDetails, String workOrderType) {
        if (CollUtil.isEmpty(pointDetails)) {
            return;
        }

        // 构建检查请求
        PointCreateCheckQueryDTO request = new PointCreateCheckQueryDTO();
        request.setWorkOrderType(workOrderType);
        List<String> pointCodeList = pointDetails.stream()
                .filter(Objects::nonNull)
                .map(PointDetail::getPointCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        request.setPointCodeList(pointCodeList);

        // 获取点位创建检查数据
        List<PointCreateCheckVO> checkData;
        log.info("检查点位安装状态，参数: {}", request);
        checkData = workOrderRpc.batchQueryPointCanCreateWorkOrder(request).getData();
        log.info("检查点位安装状态，结果: {}", checkData);
        Map<String, String> deviceMap = pointMapper.listByPointCodes(pointCodeList).stream()
                .filter(Objects::nonNull)
                .filter(point -> StrUtil.isNotBlank(point.getPointCode()))
                .collect(Collectors.toMap(
                        PointDetail::getPointCode,
                        PointDetail::getDeviceSize,
                        (existing, replacement) -> replacement));

        LambdaQueryWrapper<PointEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PointEntity::getCode, pointCodeList);
        Map<String, PointEntity> pointMap = pointMapper.selectList(queryWrapper)
                .stream().collect(Collectors.toMap(
                        PointEntity::getCode,
                        Function.identity(),
                        (existing, replacement) -> replacement));


        // 将检查数据转换为Map
        Map<String, PointCreateCheckVO> pointCheckMap = checkData.stream()
                .filter(Objects::nonNull)
                .filter(check -> StrUtil.isNotBlank(check.getPointCode()))
                .collect(Collectors.toMap(
                        PointCreateCheckVO::getPointCode,
                        Function.identity(),
                        (existing, replacement) -> replacement
                ));

        // 更新点位安装状态和消息
        pointDetails.forEach(point -> {
            if (point != null && StrUtil.isNotBlank(point.getPointCode())) {
                point.setDeviceSize(deviceMap.get(point.getPointCode()));
                PointEntity pointEntity = pointMap.get(point.getPointCode());
                if (pointEntity != null) {
                    point.setBuildingRatingNo(pointEntity.getBuildingRatingNo());
                    point.setBusinessCode(pointEntity.getBusinessCode());
                }
                PointCreateCheckVO checkVO = pointCheckMap.get(point.getPointCode());
                if (checkVO != null) {
                    point.setCanInstall(Boolean.TRUE.equals(checkVO.getCanCreate()));
                    point.setMessage(checkVO.getMessage());

                } else {
                    point.setCanInstall(false);
                    point.setMessage("点位信息未找到");
                }
            }
        });

        // 如果检查数据为空，所有点位都设置为不可安装
        if (CollUtil.isEmpty(checkData)) {
            pointDetails.forEach(point -> {
                point.setCanInstall(false);
                point.setMessage("无法获取安装状态信息");
            });
        }
    }

    /**
     * 获取楼宇下所有点位的Map
     *
     * @param buildingNo 楼宇编码
     * @return 点位Map，key为点位编码，value为点位详情
     */
    private Map<String, PointDetail> getBuildingPointMap(String buildingNo) {
        List<PointDetail> allPoints = RpcUtils.unBox(sspRpc.pointList(buildingNo));
        if (CollUtil.isEmpty(allPoints)) {
            log.warn("楼宇 {} 下未找到任何点位信息", buildingNo);
            return Map.of();
        }

        return allPoints.stream()
                .filter(Objects::nonNull)
                .filter(point -> point.getPointCode() != null)
                .collect(Collectors.toMap(
                        PointDetail::getPointCode,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 处理点位详情：检查安装状态、排序、字典转换
     *
     * @param points        点位详情列表
     * @param workOrderType 工单类型
     */
    private void processPointsWithInstallCheck(List<PointDetail> points, String workOrderType) {
        if (CollUtil.isEmpty(points)) {
            return;
        }

        // 检查安装状态
        checkPointInstallStatus(points, workOrderType);

        points.sort((p1, p2) -> Boolean.compare(
                Boolean.FALSE.equals(p2.getCanInstall()),
                Boolean.FALSE.equals(p1.getCanInstall())
        ));

        // 字典转换
        converterFactory.convert(points);
    }

    /**
     * 填充合同点位详情
     *
     * @param contractWithPoints 合同点位列表
     * @param allPointMap        所有点位Map，key为点位编码，value为点位详情
     */
    private void fillPointDetail(List<ContractWithPointsVO> contractWithPoints, Map<String, PointDetail> allPointMap) {
        if (CollUtil.isEmpty(contractWithPoints) || allPointMap.isEmpty()) {
            return;
        }

        contractWithPoints.stream()
                .filter(Objects::nonNull)
                .filter(contract -> CollUtil.isNotEmpty(contract.getPoints()))
                .forEach(contract -> {
                    List<PointDetail> pointDetails = contract.getPoints().stream()
                            .filter(Objects::nonNull)
                            .map(allPointMap::get)
                            .filter(Objects::nonNull) // 过滤掉在allPointMap中不存在的点位
                            .toList();
                    contract.setPointDetails(pointDetails);
                });
    }


}
