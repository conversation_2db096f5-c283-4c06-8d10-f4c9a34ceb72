package com.coocaa.meht.rpc;

import com.coocaa.meht.rpc.dto.CreHousePriceRentResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 房价行情网接口客户端
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@FeignClient(name = "cre-house-price-api", url = "${cre-house-price.domain}")
public interface CreHousePriceRpc {

    /**
     * 获取房价信息
     *
     * @param city     城市名称或国标码
     * @param district 行政区名称或国标码
     * @param haName   楼盘名称
     * @param location 小区地址
     * @param apiKey   接口密钥
     * @return 响应结果
     */
    @GetMapping("/open/cshimedia/survey")
    CreHousePriceRentResponse getHouseRent(
            @RequestParam("city") String city,
            @RequestParam("district") String district,
            @RequestParam("haName") String haName,
            @RequestParam("location") String location,
            @RequestParam("apiKey") String apiKey);
}