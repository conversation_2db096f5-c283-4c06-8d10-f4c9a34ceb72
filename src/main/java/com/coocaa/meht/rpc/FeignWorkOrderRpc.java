package com.coocaa.meht.rpc;

import com.coocaa.ad.common.config.FeignConfig;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.workorder.dto.BuildingWorkOrderDetailQueryDTO;
import com.coocaa.meht.module.workorder.dto.ChangeBuildingFollowerDTO;
import com.coocaa.meht.module.workorder.dto.PointCreateCheckQueryDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderCancelRequestDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderDTO;
import com.coocaa.meht.module.workorder.dto.WorkOrderPageQueryDTO;
import com.coocaa.meht.module.workorder.vo.BuildingDetailVO;
import com.coocaa.meht.module.workorder.vo.PointCreateCheckVO;
import com.coocaa.meht.module.workorder.vo.PointResultVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderBuildingVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderCancelResultVO;
import com.coocaa.meht.module.workorder.vo.WorkOrderDetailVO;
import com.coocaa.meht.rpc.dto.WorkOrderPageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 工单系统API接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-07-16
 */
@FeignClient(name = "cheese-bcs-api", value = "cheese-bcs-api", configuration = FeignConfig.class)
public interface FeignWorkOrderRpc {

    /**
     * 新建安装/拆除工单
     *
     * @param request 工单请求参数
     * @return 工单创建结果
     */
    @PostMapping("/bcs/api/installRemove/workOrder/add")
    Result<List<PointResultVO>> addInstallRemoveWorkOrder(@RequestBody WorkOrderDTO request);

    /**
     * 分页查询楼宇工单信息
     *
     * @param request 查询参数
     * @return 楼宇工单信息分页结果
     */
    @PostMapping("/bcs/api/installRemove/workOrder/page")
    Result<WorkOrderPageResult<WorkOrderBuildingVO>> pageQueryBuildingWorkOrders(@RequestBody WorkOrderPageQueryDTO request);

    /**
     * 根据楼宇编码获取楼宇下的工单信息
     *
     * @param request 查询参数
     * @return 楼宇工单详情
     */
    @PostMapping("/bcs/api/installRemove/workOrder/buildingDetail")
    Result<BuildingDetailVO> getBuildingWorkOrderDetail(@RequestBody BuildingWorkOrderDetailQueryDTO request);

    /**
     * 根据工单编号获取工单详情
     *
     * @param workOrderNo 工单编号
     * @return 工单详情
     */
    @GetMapping("/bcs/api/installRemove/workOrder/detail/{workOrderNo}")
    Result<WorkOrderDetailVO> getWorkOrderDetail(@PathVariable("workOrderNo") String workOrderNo);

    /**
     * 批量查询点位编码是否可以创建工单
     *
     * @param request 查询参数
     * @return 点位创建检查结果列表
     */
    @PostMapping("/bcs/api/installRemove/workOrder/queryCanCreate")
    Result<List<PointCreateCheckVO>> batchQueryPointCanCreateWorkOrder(@RequestBody PointCreateCheckQueryDTO request);

    /**
     * 根据工单编号撤销工单
     *
     * @param request 包含工单编号集合的请求对象
     * @return 撤销结果列表
     */
    @PostMapping("/bcs/api/installRemove/workOrder/cancelled")
    Result<List<WorkOrderCancelResultVO>> cancelWorkOrders(@RequestBody WorkOrderCancelRequestDTO request);

    /**
     * 更改楼宇负责人
     *
     * @param request 更改负责人请求参数
     * @return 操作结果
     */
    @PostMapping("/bcs/api/installRemove/workOrder/changeFollower")
    Result<Object> changeBuildingFollower(@RequestBody ChangeBuildingFollowerDTO request);
}
