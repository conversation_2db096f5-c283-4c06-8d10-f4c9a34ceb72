package com.coocaa.meht.rpc.dto;

import com.coocaa.meht.common.bean.ProjectAddParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/31
 */

@Data
public class WaitingHallAddParam {

    @Schema(description = "项目id")
    private Integer projectId;

    @NotBlank
    @Schema(description = "楼栋名称")
    private String buildingName;

    @Schema(description = "楼栋编码")
    private String buildingRatingNo;

    @NotBlank
    @Schema(description = "单元名称")
    private String unitName;

    @Schema(description = "楼层字典")
    private String floor;

    @NotBlank
    @Schema(description = "等候厅名称")
    private String waitingHallName;


    @NotBlank
    @Schema(description = "等候厅类型，字典code 0003")
    private String waitingHallType;

    /**
     * 组装项目信息
     */
    private ProjectAddParam projectAddParam;

    @Schema(description = "是否允许重复，true：允许")
    private Boolean repeatFlag = true;

}
