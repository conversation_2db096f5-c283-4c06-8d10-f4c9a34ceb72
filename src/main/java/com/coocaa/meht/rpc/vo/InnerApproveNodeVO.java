package com.coocaa.meht.rpc.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
public class InnerApproveNodeVO {

    @Schema(description = "节点ID")
    private Integer id;

    @Schema(description = "规则编号")
    private Integer ruleCode;

    @Schema(description = "审批实例code")
    private String instanceCode;

    @Schema(description = "规则审批人员表ID")
    private Integer personId;

    @Schema(description = "审核人员排序")
    private Integer rank;

    @Schema(description = "审批人员ID")
    private Integer userId;

    @Schema(description = "取消原因（字典0140）")
    private String cancelReason;

    @Schema(description = "任务状态（字典0139）")
    private String nodeStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "节点开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "节点结束时间")
    private LocalDateTime endTime;

    @Schema(description = "是否为审批节点，0：提交人节点，1：审批节点，2：结束节点")
    private Integer approvalFlag;

    @Schema(description = "审批结果，字典0138")
    private String approvalResult;

    @Schema(description = "审批意见")
    private String comment;
}
