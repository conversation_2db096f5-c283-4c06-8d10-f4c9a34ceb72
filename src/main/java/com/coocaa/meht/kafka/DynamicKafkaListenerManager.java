package com.coocaa.meht.kafka;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.listener.BatchMessageListener;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class DynamicKafkaListenerManager {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory;

    private final Map<String, ConcurrentMessageListenerContainer<String, String>> containerMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void initializeListeners() {
        // 查找所有带有 @TopicHandler 注解的 Bean
        Map<String, Object> beans = applicationContext.getBeansWithAnnotation(TopicHandler.class);
        for (Object bean : beans.values()) {
            TopicHandler annotation = AopUtils.getTargetClass(bean).getAnnotation(TopicHandler.class);
            String topic = annotation.value();

            if (bean instanceof TopicCallback || bean instanceof TopicCallbackMultipleMsg) {
                // 为每个 Topic 动态创建 Kafka Listener
                createListenerForTopic(topic, bean);
            } else {
                throw new IllegalArgumentException("Bean " + bean.getClass().getName()
                        + " 必须实现 TopicCallback/TopicCallbackMultipleMsg 接口");
            }
        }
    }

    private void createListenerForTopic(String topic, Object callback) {
        ConcurrentMessageListenerContainer<String, String> container =
                kafkaListenerContainerFactory.createContainer(topic);
        container.setConcurrency(5);

        if (callback instanceof TopicCallback) {
            container.getContainerProperties().setMessageListener((BatchMessageListener<String, String>) records -> {
                processSingleMessages(topic, (TopicCallback) callback, records);
            });
        } else if (callback instanceof TopicCallbackMultipleMsg) {
            container.getContainerProperties().setMessageListener((BatchMessageListener<String, String>) records -> {
                processBatchMessages(topic, (TopicCallbackMultipleMsg) callback, records);
            });
        } else {
            throw new IllegalArgumentException("Unsupported callback type: " + callback.getClass().getName());
        }

        container.start();
        containerMap.put(topic, container);
        log.info("Kafka listener created and started for topic: {}", topic);
    }

    // 单消息处理逻辑
    private void processSingleMessages(String topic, TopicCallback callback, List<ConsumerRecord<String, String>> records) {
        try {
            log.info("Processing batch messages from topic {}: {}", topic, records.size());
            for (ConsumerRecord<String, String> record : records) {
                log.info("Processing message: {}", record.value());
                callback.process(record.value());
            }
        } catch (Exception e) {
            log.error("Error while processing batch messages from topic {}: {}", topic, e.getMessage(), e);
        }
    }

    // 批量消息处理逻辑
    private void processBatchMessages(String topic, TopicCallbackMultipleMsg callback, List<ConsumerRecord<String, String>> records) {
        try {
            log.info("Processing batch messages from topic {}: {}", topic, records.size());
            List<String> messages = new ArrayList<>();
            for (ConsumerRecord<String, String> record : records) {
                log.info("Processing message: {}", record.value());
                messages.add(record.value());
            }
            callback.process(messages);
        } catch (Exception e) {
            log.error("Error while processing batch messages from topic {}: {}", topic, e.getMessage(), e);
        }
    }

    // 停止所有动态创建的 Kafka Listener
    public void stopAllListeners() {
        containerMap.values().forEach(container -> {
            try {
                container.stop();
                log.info("Kafka listener for topic {} stopped.", container.getContainerProperties().getTopics());
            } catch (Exception e) {
                log.error("Error while stopping Kafka listener: {}", e.getMessage(), e);
            }
        });
        containerMap.clear();
        log.info("All Kafka listeners stopped.");
    }

    // 在 Spring 容器关闭时执行清理工作
    @PreDestroy
    public void cleanup() {
        log.info("Shutting down DynamicKafkaListenerManager...");
        stopAllListeners();
    }
}
