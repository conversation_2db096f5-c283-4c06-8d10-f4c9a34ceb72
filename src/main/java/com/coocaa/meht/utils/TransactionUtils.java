package com.coocaa.meht.utils;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Supplier;

/**
 * 事务工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-17 15:59
 */
@Component
public class TransactionUtils {
    /**
     * 多个方法封装到一个事务中
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean doBatch(List<Supplier<Boolean>> suppliers, Supplier<String> errorSupplier) {
        try {
            for (Supplier<Boolean> supplier : suppliers) {
                if (!supplier.get()) {
                    throw new RuntimeException(errorSupplier.get());
                }
            }
        } catch (RuntimeException e) {
            throw new RuntimeException("操作已回滚。" + e.getMessage());
        }

        return true;
    }
}
