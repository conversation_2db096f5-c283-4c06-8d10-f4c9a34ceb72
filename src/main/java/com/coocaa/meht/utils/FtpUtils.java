package com.coocaa.meht.utils;

import com.coocaa.meht.common.exception.ServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Ftp 文件上传
 *
 * <AUTHOR>
 * @Date 2023-12-11 20:49
 */
@Slf4j
@Component
public class FtpUtils {
    public static final Set<String> FILE_TYPE = new HashSet<>(Arrays.asList(
            "mp3", "ogg", "wav", "wma", "ape", "flac", "aac", "ac3", "mmf", "amr", "m4a", "m4r", "wv", "mp2", "mp4a",
            "acc", "ape", "flac", "mp4", "ts", "mov", "mxf", "mpg", "flv", "wmv", "avi", "m4v", "f4v", "mpeg", "3gp", "asf", "mkv",
            "navi", "divx", "rm", "rmvb", "dv-avi", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "rar", "zip", "7z", "cab", "arj",
            "lzh", "tar", "gz", "ace", "uue", "bz2", "jar", "bmp", "jpg", "png", "tif", "gif", "pcx", "tga", "exif", "fpx", "svg", "psd", "cdr", "pcd", "dxf",
            "ufo", "eps", "ai", "raw", "wmf", "webp", "avif", "apng", "xmind", "log", "xlt", "pdf", "wps", "wpt", "pptm", "html", "jpeg", "icon", "ico",
            "properties", "md", "chm", "txt"
    ));

    private static String host;
    private static int port;
    private static String username;
    private static String password;
    private static String domain;
    private static String activePath;

    /**
     * 上传
     *
     * @param filename
     * @param is
     */
    public static void upload(String filename, InputStream is) {
        FTPClient ftpClient = null;
        try {
            ftpClient = create();
            if (!ftpClient.changeWorkingDirectory(activePath)) {
                if (!ftpClient.makeDirectory(activePath)) {
                    throw new ServerException("文件服务器路径异常");
                }
                ftpClient.changeWorkingDirectory(activePath);
            }
            if (!ftpClient.storeFile(filename, is)) {
                throw new ServerException("服务器异常：上传失败");
            }
        } catch (IOException e) {
            log.error("Ftp上传异常:", e);
            throw new ServerException("服务器异常：上传错误");
        } finally {
            close(ftpClient);
        }
    }

    /**
     * 下载
     *
     * @param path
     * @param out
     */
    public static void download(String path, OutputStream out) {
        String filename = path.substring(path.lastIndexOf("/") + 1);
        FTPClient ftpClient = null;
        try {
            ftpClient = create();
            if (ftpClient.changeWorkingDirectory(activePath)) {
                String[] fileNames = ftpClient.listNames();
                if (fileNames != null) {
                    for (String name : fileNames) {
                        if (StringUtils.equals(filename, name)) {
                            try (InputStream is = ftpClient.retrieveFileStream(name)) {
                                IoUtils.copy(is, out);
                            }
                            break;
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error("Ftp下载异常:", e);
        } finally {
            close(ftpClient);
        }
    }

    private static FTPClient create() throws IOException {
        FTPClient ftpClient = new FTPClient();
        ftpClient.setDefaultTimeout(15000);
        ftpClient.setConnectTimeout(10000);
        //数据传输的超时时间
        ftpClient.setDataTimeout(Duration.ofMillis(60000));
        ftpClient.connect(host, port);
        if (!ftpClient.login(username, password)) {
            close(ftpClient);
            throw new ServerException("文件服务器登录失败");
        }
        ftpClient.setBufferSize(8 * 1024);
        ftpClient.setControlEncoding("UTF-8");
        //设置传输类型（二进制）
        ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
        //被动模式
        ftpClient.enterLocalPassiveMode();
        return ftpClient;
    }

    public static void close(FTPClient ftpClient) {
        if (ftpClient != null) {
            try {
                ftpClient.logout();
            } catch (IOException ignored) {
            }
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException ignored) {
                }
            }
        }
    }

    public static String getPath(String filename) {
        return domain + "/" + activePath + "/" + filename;
    }

    @Value("${ftp.host:}")
    public void setHost(String host) {
        FtpUtils.host = host;
    }

    @Value("${ftp.port:21}")
    public void setPort(int port) {
        FtpUtils.port = port;
    }

    @Value("${ftp.username:}")
    public void setUsername(String username) {
        FtpUtils.username = username;
    }

    @Value("${ftp.password:}")
    public void setPassword(String password) {
        FtpUtils.password = password;
    }

    @Value("${ftp.domain:}")
    public void setDomain(String domain) {
        FtpUtils.domain = domain;
    }

    @Value("${spring.profiles.active:test}")
    public void setActivePath(String activePath) {
        FtpUtils.activePath = activePath;
    }

}
