package com.coocaa.meht.utils;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023-04-20 15:25
 */
@Component
public class SpringBeanUtils implements BeanFactoryPostProcessor {

    private static ConfigurableListableBeanFactory beanFactory;

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory factory) throws BeansException {
        SpringBeanUtils.beanFactory = factory;
    }

    /**
     * @param name
     */
    public static Object getBean(String name) {
        return beanFactory.getBean(name);
    }

    /**
     * @param clz
     */
    public static <T> T getBean(Class<T> clz) {
        return beanFactory.getBean(clz);
    }

    /**
     * 如果BeanFactory包含一个与所给名称匹配的bean定义，则返回truen
     */
    public static boolean containsBean(String name) {
        return beanFactory.containsBean(name);
    }

    /**
     * 判断以给定名字注册的bean定义是一个singleton还是一个prototype。
     */
    public static boolean isSingleton(String name) {
        return beanFactory.isSingleton(name);
    }

    /**
     * 获取类型
     *
     * @param name
     * @return Class 注册对象的类型
     */
    public static Class<?> getType(String name) {
        return beanFactory.getType(name);
    }

}
