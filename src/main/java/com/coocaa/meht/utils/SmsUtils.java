package com.coocaa.meht.utils;

import com.coocaa.meht.common.exception.ServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.StringWriter;
import java.util.Map;
import java.util.Objects;

/**
 * 短信
 */
@Slf4j
public class SmsUtils {
    private static final String SMS_DOMAIN = "http://sms.coocaatv.com/sms/down/index.php";
    private static final String SMS_ACCOUNT = "179", SMS_PASSWORD = "efc6854dde515934b58991aea013bf60";

    /**
     * 短信发送
     *
     * @param phone
     * @param content
     */
    public static void send(String phone, String content) {
        String body = HttpUtils.post(SMS_DOMAIN, null, createContentXML(phone, content),
                ContentType.APPLICATION_SOAP_XML);
        Map<String, String> bodyMap = XmlUtils.xmlToMap(body);
        if (!Objects.equals("0000", Converts.toStr(bodyMap.get("RESULT_CODE")))) {
            throw new ServerException("发送短信失败，请联系管理员");
        }
    }

    /**
     * 构建xml
     *
     * @param phone
     * @param content
     * @return
     */
    public static String createContentXML(String phone, String content) {
        org.w3c.dom.Document document;
        try {
            document = XmlUtils.newDocument();
        } catch (ParserConfigurationException e) {
            throw new RuntimeException(e);
        }
        document.setXmlStandalone(true);
        org.w3c.dom.Element root = document.createElement("data");
        document.appendChild(root);
        org.w3c.dom.Element bid = document.createElement("bid");
        bid.appendChild(document.createTextNode(SMS_ACCOUNT));
        root.appendChild(bid);
        org.w3c.dom.Element pwd = document.createElement("pwd");
        pwd.appendChild(document.createTextNode(SMS_PASSWORD));
        root.appendChild(pwd);
        org.w3c.dom.Element phonenumber = document.createElement("phonenumber");
        phonenumber.appendChild(document.createTextNode(phone));
        root.appendChild(phonenumber);
        org.w3c.dom.Element sendcontent = document.createElement("sendcontent");
        sendcontent.appendChild(document.createTextNode(content));
        root.appendChild(sendcontent);
        try (StringWriter writer = new StringWriter()) {
            Transformer transformer = TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
            //输出是否换行
            transformer.setOutputProperty(OutputKeys.INDENT, "YES");
            transformer.transform(new DOMSource(document), new StreamResult(writer));
            return writer.getBuffer().toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
