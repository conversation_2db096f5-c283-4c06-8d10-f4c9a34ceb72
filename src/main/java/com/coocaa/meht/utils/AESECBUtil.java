package com.coocaa.meht.utils;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;

public class AESECBUtil {

    private static final String KEY = "dI65+IaUQHk2VrYhI8btcA==";


    private static final cn.hutool.crypto.symmetric.AES AES = new AES(Mode.ECB, Padding.PKCS5Padding, KEY.getBytes());

    public static String encryptBase64(String content) {
        return AES.encryptBase64(content);
    }


    public static String encryptHex(String content) {
        return AES.encryptHex(content);
    }

    public static String decryptStr(String decryptContent) {
        return AES.decryptStr(decryptContent);
    }

//    public static void main(String[] args) {
//        String password = "mX1tJ0lE3s";
//        String content = encryptBase64(password);
//        System.out.println(content);
//        System.out.println(decryptStr(content));
//    }


}
