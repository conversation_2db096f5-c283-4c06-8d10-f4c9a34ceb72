package com.coocaa.meht.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class KafkaProducer {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(String topic, String message) {
        kafkaTemplate.send(topic, message)
            .whenComplete((result, exception) -> {
                if (exception != null) {
                    log.error("消息发送失败: " + exception.getMessage());
                } else {
                    log.info("消息发送成功，topic:{}, message:{} ", topic, message);
                }
            });
    }
}
