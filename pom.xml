<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.coocaa.ad</groupId>
    <artifactId>cheese-cms</artifactId>
    <version>1.0.0</version>
    <name>cheese-cms</name>
    <packaging>pom</packaging>

    <modules>
        <module>cheese-cms-common</module>
        <module>cheese-cms-api</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <maven-compiler-plugin>3.8.1</maven-compiler-plugin>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.boot.version>3.2.9</spring.boot.version>
        <spring.cloud.version>2023.0.0</spring.cloud.version>
        <spring.cloud.alibaba.version>2023.0.0.0-RC1</spring.cloud.alibaba.version>
        <mysql.version>8.0.33</mysql.version>
        <mybatis.version>3.5.7</mybatis.version>
        <p6spy.version>3.9.1</p6spy.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <junit.version>4.13.2</junit.version>
        <hutool.version>5.8.32</hutool.version>
        <fastjson2.version>2.0.52</fastjson2.version>
        <guava.version>33.3.0-jre</guava.version>
        <commons.lang3.version>3.17.0</commons.lang3.version>
        <joda.version>2.10.14</joda.version>
        <cheese.tools.version>1.0.0</cheese.tools.version>
        <cheese.db.version>1.0.0</cheese.db.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--Spring Boot-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--Spring Cloud-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--Spring Cloud Alibaba-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--Mybatis-plus-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>
            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2-extension-spring6</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-cms-common-tools</artifactId>
                <version>${cheese.tools.version}</version>
            </dependency>
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-cms-common-db</artifactId>
                <version>${cheese.db.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>
            <!-- 汉语拼音, 生成点位编码需要得到城市首字母 -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>2.5.1</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda.version}</version>
            </dependency>
            <!-- mail -->
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>mailapi</artifactId>
                <version>1.5.1</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>smtp</artifactId>
                <version>1.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>1.12.2</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>6.1.0</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-anno</artifactId>
                <version>2.7.6</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>2.6.0</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>4.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos-sts_api</artifactId>
                <version>3.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>5.6.229</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>2.3.1</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>1.12.0</version>
            </dependency>

            <!-- 自定义公共组件 -->
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-common-base</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-common-data</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-common-web</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-downloader-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-translate-starter</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <!-- 日志相关 -->
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>logback-config</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 配置将项目发布到 nexus私服中 没有则注释 -->
    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>http://172.20.135.19:8080/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://172.20.135.19:8080/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
