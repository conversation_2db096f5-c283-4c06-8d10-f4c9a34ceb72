package com.coocaa.meht.api.cheese.controller;

import com.coocaa.meht.api.cheese.bean.building.BuildingMetaQueryDTO;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.service.BuildingMetaApiService;
import com.coocaa.meht.api.cheese.service.MehtWebRpcService;
import com.coocaa.meht.api.cheese.vo.building.BuildingMetaListVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @program: cheese-meht-web-api
 * @ClassName MehtWebRpcController
 * @description:
 * @author: zhangbinxian
 * @create: 2025-02-26 15:50
 * @Version 1.0
 **/

@RestController
@RequestMapping("/rpc")
public class MehtWebRpcController {

    @Autowired
    private MehtWebRpcService mehtWebRpcService;

    @Operation(summary = "查询全量的楼宇数据")
    @PostMapping("/building-meta-list")
    public ResultTemplate<List<BuildingMetaListVO>> listByBuildingMetas(@RequestBody PageRequestVo<BuildingMetaQueryDTO> pageRequestVo) {
        return ResultTemplate.success(mehtWebRpcService.listByBuildingMetas(pageRequestVo));
    }

}
