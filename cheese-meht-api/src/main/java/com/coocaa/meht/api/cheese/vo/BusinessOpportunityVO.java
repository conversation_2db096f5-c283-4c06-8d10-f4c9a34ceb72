package com.coocaa.meht.api.cheese.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.coocaa.meht.api.cheese.utils.converter.Convert;
import com.coocaa.meht.api.cheese.utils.converter.ConvertType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-27
 */
@Data
public class BusinessOpportunityVO {

    @ExcelIgnore
    @Schema(description = "商机id")
    private Long id;

    @ColumnWidth(18)
    @ExcelProperty("商机编码")
    @Schema(description = "商机编码")
    private String code;

    @ColumnWidth(20)
    @ExcelProperty("商机名称")
    @Schema(description = "商机名称")
    private String businessOpportunityName;

    @ExcelIgnore
    @Schema(description = "楼宇编码")
    private String buildingNo;

    @ColumnWidth(20)
    @ExcelProperty("楼宇名称")
    @Schema(description = "楼宇名称")
    private String buildingName;

    /**
     * 楼宇类型字典
     */
    @ExcelIgnore
    @Schema(hidden = true)
    @Convert(type = ConvertType.BUILDING_TYPE)
    private Integer buildingType;

    @ColumnWidth(11)
    @ExcelProperty("楼宇类型")
    @Schema(description = "楼宇类型")
    private String buildingTypeName;

    @ColumnWidth(11)
    @ExcelProperty("楼宇评级")
    @Schema(description = "楼宇评级")
    private String projectLevel;

    @ColumnWidth(10)
    @ExcelProperty("城市")
    @Schema(description = "城市")
    private String mapCity;

    @ColumnWidth(8)
    @ExcelProperty("负责人")
    @Schema(description = "负责人")
    private String submitUserName;

    @ColumnWidth(17)
    @ExcelProperty("更新时间")
    @Schema(description = "更新时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ExcelIgnore
    @Schema(description = "商机状态字典key")
    @Convert(type = ConvertType.DICT)
    private String status;

    @ColumnWidth(11)
    @ExcelProperty("商机状态")
    @Schema(description = "商机状态")
    private String statusName;

    @ColumnWidth(17)
    @ExcelProperty("创建时间")
    @Schema(description = "创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelIgnore
    @Schema(description = "负责人工号")
    private String submitUserCode;

    @ExcelIgnore
    @Schema(description = "登陆人是否是数据负责人标识：true-是，false-不是")
    private Boolean responsibleFlag;

}
