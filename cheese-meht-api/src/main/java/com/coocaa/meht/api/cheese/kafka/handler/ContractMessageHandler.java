package com.coocaa.meht.api.cheese.kafka.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.coocaa.meht.api.cheese.common.db.dto.ContractDTO;
import com.coocaa.meht.api.cheese.common.db.dto.ContractProjectDTO;
import com.coocaa.meht.api.cheese.common.db.dto.UpdatePointStatusDTO;
import com.coocaa.meht.api.cheese.common.db.entity.PointEntity;
import com.coocaa.meht.api.cheese.common.db.service.IPointPlanService;
import com.coocaa.meht.api.cheese.common.db.service.IPointService;
import com.coocaa.meht.api.cheese.common.tools.enums.ContractStatusEnum;
import com.coocaa.meht.api.cheese.common.tools.enums.PointPlanStatusEnum;
import com.coocaa.meht.api.cheese.common.tools.enums.PointStatusEnum;
import com.coocaa.meht.api.cheese.common.tools.utils.RpcUtils;
import com.coocaa.meht.api.cheese.kafka.TopicCallbackMultipleMsg;
import com.coocaa.meht.api.cheese.kafka.TopicHandler;
import com.coocaa.meht.api.cheese.rpc.FeignSspRpc;
import com.coocaa.meht.api.cheese.service.PointContractSnapshotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同审批状态发送消息处理器
 */
@Slf4j
@Component
@TopicHandler("cheese-venue-contract-point")
public class ContractMessageHandler implements TopicCallbackMultipleMsg {
    @Autowired
    private IPointService pointService;

    @Autowired
    FeignSspRpc feignSspRpc;

    @Autowired
    private IPointPlanService pointPlanService;
    @Autowired
    private PointContractSnapshotService pointContractSnapshotService;

    @Override
    public void process(List<String> messages) {
        log.info("收到合同审批消息");
        if (CollectionUtil.isEmpty(messages)) {
            return;
        }

        log.info("合同审批消息内容:{}", messages);
        messages.forEach(this::handleMessage);
    }

    /**
     * 处理单条消息
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleMessage(String message) {
        try {
            ContractDTO contractDTO = JSON.parseObject(message, new TypeReference<>() {
            });
            ContractStatusEnum status = ContractStatusEnum.getByCode(contractDTO.getStatus());
            if (status == null) {
                log.error("合同审批状态错误:{}", message);
                return;
            }

            // 根据不同状态处理
            switch (status) {
                case SUBMIT_APPROVAL:
                    handleSubmitApproval(contractDTO);
                    break;
                case APPROVED:
                    handleApproved(contractDTO);
                    break;
                case REJECTED:
                    handleRejected(contractDTO);
                    break;
                case INVALID:
                    handleRejected(contractDTO);
                    break;
                case WITHDRAW:
                    handleRejected(contractDTO);
                    break;
                default:
                    log.error("未处理的合同状态: {}", status);
            }
        } catch (Exception e) {
            log.error("处理合同消息异常: {}", message, e);
        }
    }

    /**
     * 处理提交审批
     */
    private void handleSubmitApproval(ContractDTO contractDTO) {
        List<ContractProjectDTO> projects = contractDTO.getProjects();
        if (CollectionUtil.isNotEmpty(projects)) {
            projects.forEach(e -> {
                String code = e.getCode();
                List<String> pointCodes = e.getPointCodes();
                if (CollectionUtil.isNotEmpty(pointCodes)) {
                    //调用ssp更新点位数据
                    log.info("提交合同更新点位：{}状态为：{}", pointCodes, PointStatusEnum.SIGNING.getCode());
                    UpdatePointStatusDTO statusDTO = UpdatePointStatusDTO.builder().pointCodes(pointCodes).status(PointStatusEnum.SIGNING.getCode()).build();
                    RpcUtils.unBox(feignSspRpc.updatePointStatus(statusDTO));

                    //删除合同没有选择的点位（关系）
                    List<PointEntity> pointEntities = pointService.listByBusinessCodeAndNotPointCodes(code, pointCodes);
                    log.info("删除合同未选中的点位：{}", pointEntities);
                    if (ObjectUtil.isNotEmpty(pointEntities)) {
                        List<String> codeList = pointEntities.stream().map(PointEntity::getCode).collect(Collectors.toList());
                        //调用ssp删除点位数据
                        RpcUtils.unBox(feignSspRpc.deleteByPointCodes(codeList));
                        //删除关系表中的数据
                        pointService.deleteByBuildingNoAndPointCodes(code, codeList);
                    }
                    //更新点位以及点位方案的状态为签约中
                    pointPlanService.updateStatus(code, pointCodes, PointPlanStatusEnum.SIGNING, null);
                }
            });
        }
    }

    /**
     * 处理审核通过
     */
    private void handleApproved(ContractDTO contractDTO) {
        List<ContractProjectDTO> projects = contractDTO.getProjects();
        if (CollectionUtil.isNotEmpty(projects)) {
            projects.forEach(e -> {
                String code = e.getCode();
                List<String> pointCodes = e.getPointCodes();
                //更新点位以及点位方案的状态为已签约
                pointPlanService.updateStatus(code, pointCodes, PointPlanStatusEnum.SIGNED, e.getEndDate());
                //调用ssp更新点位数据
                log.info("合同审核通过更新点位：{}状态为：{}", pointCodes, PointStatusEnum.WAITING.getCode());
                UpdatePointStatusDTO statusDTO = UpdatePointStatusDTO.builder().pointCodes(pointCodes).status(PointStatusEnum.WAITING.getCode()).build();
                RpcUtils.unBox(feignSspRpc.updatePointStatus(statusDTO));

//                pointService.updateStatus(code,pointCodes, PointStatusEnum.WAITING, e.getEndDate());
                //生成点位签约成功的快照
                pointContractSnapshotService.saveSnapshot(code, pointCodes);
            });
        }
    }

    /**
     * 处理审核驳回
     */
    private void handleRejected(ContractDTO contractDTO) {
        List<ContractProjectDTO> projects = contractDTO.getProjects();
        if (CollectionUtil.isNotEmpty(projects)) {
            projects.forEach(e -> {
                String code = e.getCode();
                List<String> pointCodes = e.getPointCodes();
                pointPlanService.updateStatus(code, pointCodes, PointPlanStatusEnum.WAITING_SIGN, null);
//                pointService.updateStatus(code,pointCodes, PointStatusEnum.PENDING_SIGNATURE, e.getEndDate());
                //调用ssp更新点位数据
                UpdatePointStatusDTO statusDTO = UpdatePointStatusDTO.builder().pointCodes(pointCodes).status(PointStatusEnum.PENDING_SIGNATURE.getCode()).build();
                RpcUtils.unBox(feignSspRpc.updatePointStatus(statusDTO));

            });
        }
    }

}
