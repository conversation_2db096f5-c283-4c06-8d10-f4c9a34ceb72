package com.coocaa.meht.api.cheese.bean.price;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class PriceApplyUpdateStatusParam {
    @Schema(description = "申请编号")
    @NotBlank(message = "申请编号不能为空")
    private String applyCode;

    @Schema(description = "状态", example = "0:草稿 1:待审核 2:已审核 3:审核不通过 4:放弃")
    @NotBlank(message = "状态不能为空")
    private Integer status;

    @Schema(description = "备注")
    private String remark;
}
