
package com.coocaa.meht.api.cheese.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 核心区域按城市分组
 *
 * <AUTHOR>
 * @since 2025-7-1
 */
@Data
@Accessors(chain = true)
@Schema(name = "核心区域按城市分组", description = "核心区域按城市分组")
@ExcelIgnoreUnannotated
public class CoreAreaCityVO {

    @Schema(description = "所属城市id")
    private Integer cityId;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "核心区域列表")
    List<CoreAreaVO> coreAreas;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;
}