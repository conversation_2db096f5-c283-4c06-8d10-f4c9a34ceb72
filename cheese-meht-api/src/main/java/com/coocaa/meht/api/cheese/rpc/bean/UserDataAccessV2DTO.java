package com.coocaa.meht.api.cheese.rpc.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-27
 */
@Data
@Accessors(chain = true)
public class UserDataAccessV2DTO {

    /**
     * 权限类型
     */
    private String accessType;
    /**
     * 代理商ID集合
     */
    private List<Integer> agentIds;

    /**
     * 用户ID集合
     */
    private List<Integer> userIds;

    /**
     * 城市ID集合
     */
    private List<Integer> cityIds;
}
