
package com.coocaa.meht.api.cheese.vo;

import com.coocaa.meht.api.cheese.common.tools.constant.Constants;
import com.coocaa.meht.api.cheese.utils.converter.Convert;
import com.coocaa.meht.api.cheese.utils.converter.ConvertType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * CoreAreaOperateRecordVO
 *
 * <AUTHOR>
 * @since 2025-7-1
 */
@Data
@Accessors(chain = true)
@Schema(name = "CoreAreaOperateRecordVO", description = "CoreAreaOperateRecordVO")
public class CoreAreaOperateRecordVO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "操作类型（新增、删除、编辑）字典0170", maxLength = 10)
    @Convert(type = ConvertType.DICT, targetFieldName = "operateTypeName")
    private String operateType;

    @Schema(description = "操作类型-翻译字段")
    private String operateTypeName;

    @Schema(description = "操作内容")
    private String content;

    @Schema(description = "城市id")
    @Convert(type = ConvertType.CITY, targetFieldName = "cityName")
    private Integer cityId;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "操作人")
    @Convert(type = ConvertType.USER_WNO, targetFieldName = "creatorName")
    private Integer creator;

    @Schema(description = "操作人姓名")
    private String creatorName;

    @Schema(description = "操作时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    /**
     * 核心区域id
     */
    private Long coreAreaId;

    /**
     * 核心区域名称
     */
    @Schema(description = "核心区域名称")
    private String coreAreaName;
}