package com.coocaa.meht.api.cheese.bean.building;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-04-14
 */
@Data
public class BuildingGeneQueryDTO {

    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "客户编号")
    @NotBlank(message = "客户编号不能为空")
    private String buildingRatingNo;


}
