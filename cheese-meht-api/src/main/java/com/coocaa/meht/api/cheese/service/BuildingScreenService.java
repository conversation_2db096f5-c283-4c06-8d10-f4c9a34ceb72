package com.coocaa.meht.api.cheese.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingScreenEntity;
import com.coocaa.meht.api.cheese.common.db.service.IBuildingScreenService;
import com.coocaa.meht.api.cheese.convert.BuildingScreenConvert;
import com.coocaa.meht.api.cheese.properties.LargeScreenProperties;
import com.coocaa.meht.api.cheese.utils.CodeNameHelper;
import com.coocaa.meht.api.cheese.vo.BuildingScreenVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 楼宇大屏业务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuildingScreenService {

    private final LargeScreenProperties largeScreenProperties;

    private final IBuildingScreenService screenService;

    private final BuildingScreenConvert screenConvert;

    private final CodeNameHelper codeNameHelper;

    /**
     * 根据楼宇编号获取楼宇大屏信息
     */
    public BuildingScreenVO getByBuildingRatingNo(String buildingNo) {
        if (StrUtil.isBlank(buildingNo)) {
            return null;
        }

        BuildingScreenEntity screenEntity = screenService.lambdaQuery()
                .eq(BuildingScreenEntity::getBuildingRatingNo, buildingNo)
                .one();
        return toBuildingScreenVO(screenEntity);
    }

    /**
     * 转换为BuildingScreenVO
     */
    public BuildingScreenVO toBuildingScreenVO(BuildingScreenEntity screenEntity) {
        if (Objects.isNull(screenEntity)) {
            return null;
        }

        BuildingScreenVO vo = screenConvert.toVo(screenEntity);
        vo.setBigScreenFlag(isLargeScreen(screenEntity));
        if (StrUtil.isNotBlank(vo.getSpec())) {
            List<String> codeList = JSON.parseArray(vo.getSpec(), String.class);
            vo.setSpecName(String.join(",", codeNameHelper.getDictMapping(codeList).values()));
        }
        return vo;
    }

    /*
     * 判断是否是大屏设备
     */
    public Boolean isLargeScreen(BuildingScreenEntity entity) {
        if (entity == null) {
            return false;
        }
        if (StrUtil.isNotBlank(entity.getSpec())) {
            List<String> codeList = JSON.parseArray(entity.getSpec(), String.class);
            List<String> largeDeviceKey = largeScreenProperties.getLargeDictKey();
            //判断largeDeviceKey与codeList是否有交集
            Collection<String> intersection = CollectionUtil.intersection(largeDeviceKey, codeList);
            return CollectionUtil.isNotEmpty(intersection);
        }
        return false;
    }


}
