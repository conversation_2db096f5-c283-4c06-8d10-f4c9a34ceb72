package com.coocaa.meht.api.cheese.bean.usertag;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 用户标签更新DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
public class UserTagQueryDTO {


    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String tagName;


    /**
     * 状态
     */
    @Schema(description = "状态 0:禁用 1:启用")
    private Integer status;
} 