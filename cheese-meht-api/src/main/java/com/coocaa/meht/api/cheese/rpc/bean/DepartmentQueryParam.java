package com.coocaa.meht.api.cheese.rpc.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 部门查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-03
 */
@Data
public class DepartmentQueryParam {
    /**
     * 是否被删除:true-是；false-否
     */
    private Boolean status;

    /**
     * 部门openId列表
     */
    @JsonProperty("departmentOpenIdList")
    private List<String> openDepartmentIds;
}
