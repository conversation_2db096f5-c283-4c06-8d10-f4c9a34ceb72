package com.coocaa.meht.api.cheese.convert;

import com.coocaa.meht.api.cheese.bean.building.BuildingGeneDTO;
import com.coocaa.meht.api.cheese.bean.building.BuildingGeneVO;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingGeneEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.control.DeepClone;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @since 2025-04-14
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface BuildingGeneConvert extends PageableConvert<BuildingGeneEntity, BuildingGeneVO> {

    /**
     * Entity 转 VO
     */
    BuildingGeneVO toVo(BuildingGeneEntity entity);

    /**
     * Entity 转 VOs
     */
    List<BuildingGeneVO> toVos(List<BuildingGeneEntity> entities);

    /**
     * VO 转 Entity
     *
     * @param vo
     * @return
     */
    @Mapping(target = "forbiddenIndustry", ignore = true)
    BuildingGeneEntity toEntity(BuildingGeneDTO vo);


}
