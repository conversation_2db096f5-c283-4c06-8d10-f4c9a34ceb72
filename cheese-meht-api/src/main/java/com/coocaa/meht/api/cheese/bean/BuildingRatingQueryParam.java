package com.coocaa.meht.api.cheese.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月13 11:57
 */
@Data
public class BuildingRatingQueryParam {

    private Long id;

    /**
     * 楼宇编码
     */
    @Schema(description = "楼宇编码")
    private String buildingNo;
    /**
     * 楼宇编码BC打头
     */
    private String bcBuildingNo;

    private String buildingName;
    /**
     * 楼宇认证状态：0未认证，1认证中 2冻结中  3已认证
     */
    @Schema(description = "楼宇认证状态")
    private Integer buildingStatus;

    /**
     * 楼宇状态：0待审核，1已审核 2已驳回 3审核不通过   4已放弃
     */
    @Schema(description = "楼宇申请状态：0待审核，1已审核 2已驳回 3审核不通过   4已放弃")
    private Integer status;
    /**
     * 楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区
     */
    @Schema(description = "楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区")
    private Integer buildingType;

    /**
     * 提交人工号
     */
    @Schema(description = "提交人工号")
    private String submitUser;


    /**
     * 等级评价
     */
    @Schema(description = "认证等级")
    private String projectLevel;


    @Schema(description = "城市")
    private Integer city;

    @Schema(description = "区县")
    private Integer district;


    @Schema(description = "行政区域")
    private List<String> mapAdCodes;



    /**
     * 是否查询所有数据
     */
    @Schema(description = "是否查询所有数据")
    private boolean all;
    /**
     * AI等级评价
     */
    @Schema(description = "AI等级评价")
    private String projectAiLevel;

    /**
     * 数据权限查询
     */
    List<String> userCodes;


}
