package com.coocaa.meht.api.cheese.convert;

import com.coocaa.meht.api.cheese.bean.price.ApplyDeviceVO;
import com.coocaa.meht.api.cheese.bean.price.PriceApplyDevicePointVO;
import com.coocaa.meht.api.cheese.bean.price.PriceApplyDeviceVO;
import com.coocaa.meht.api.cheese.bean.price.PriceApplyQueryParam;
import com.coocaa.meht.api.cheese.bean.price.PriceApplyVO;
import com.coocaa.meht.api.cheese.common.db.bean.PriceApplyQueryDTO;
import com.coocaa.meht.api.cheese.common.db.dto.PriceApplyDTO;
import com.coocaa.meht.api.cheese.common.db.entity.PriceApplyDeviceEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PriceApplyDevicePointEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PriceApplyEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface PriceApplyConvert {

    PriceApplyConvert INSTANCE = Mappers.getMapper(PriceApplyConvert.class);


    PriceApplyQueryDTO toPriceApplyDto(PriceApplyQueryParam priceApplyQueryParams);


    List<PriceApplyVO> toPriceApplyVos(List<PriceApplyDTO> priceApplyDTOs);

    @Mappings(value = {
            @Mapping(target = "buildingType", ignore = true),
            @Mapping(target = "finalCoefficient", ignore = true)
    })
    PriceApplyVO toPriceApplyVo(PriceApplyEntity priceApplyEntity);

    List<ApplyDeviceVO> toApplyDeviceVo(List<PriceApplyDeviceEntity> priceApplyDeviceEntities);

    List<PriceApplyDevicePointVO> toPriceApplyDevicePointVo(List<PriceApplyDevicePointEntity> priceApplyDevicePointEntities);

    List<PriceApplyDeviceVO> toPriceApplyDeviceVo(List<PriceApplyVO> applyDeviceVOS);


}
