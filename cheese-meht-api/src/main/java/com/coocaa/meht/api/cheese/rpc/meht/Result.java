package com.coocaa.meht.api.cheese.rpc.meht;

import com.coocaa.meht.api.cheese.rpc.meht.exception.ErrorCode;

/**
 * 响应数据
 */
public class Result<T> {
    private int code;
    private String msg;
    private T data;

    public Result() {
        this(ErrorCode.SUCCESS);
    }

    public Result(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Result(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.msg = errorCode.getMsg();
    }

    public static <T> Result<T> ok() {
        return new Result<>();
    }

    public static <T> Result<T> ok(T data) {
        Result<T> result = new Result<>();
        result.setData(data);
        return result;
    }

    public static <T> Result<T> error() {
        return new Result<>(ErrorCode.ERROR);
    }

    public static <T> Result<T> error(String msg) {
        Result<T> error = error();
        error.setMsg(msg);
        return error;
    }

    public static <T> Result<T> error(ErrorCode errorCode) {
        return new Result<>(errorCode.getCode(), errorCode.getMsg());
    }

    public int getCode() {
        return code;
    }

    public Result<T> setCode(int code) {
        this.code = code;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public Result<T> setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public T getData() {
        return data;
    }

    public Result<T> setData(T data) {
        this.data = data;
        return this;
    }
}