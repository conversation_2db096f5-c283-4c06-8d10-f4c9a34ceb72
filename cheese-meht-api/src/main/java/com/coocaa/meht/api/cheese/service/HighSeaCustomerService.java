package com.coocaa.meht.api.cheese.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.api.cheese.bean.building.HighSeaCustomerParam;
import com.coocaa.meht.api.cheese.bean.user.PermissionDTO;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingRatingEntity;
import com.coocaa.meht.api.cheese.common.db.service.IBuildingRatingService;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.convert.BuildingRatingConvert;
import com.coocaa.meht.api.cheese.handler.PermissionHandler;
import com.coocaa.meht.api.cheese.utils.CodeNameHelper;
import com.coocaa.meht.api.cheese.utils.RsaExample;
import com.coocaa.meht.api.cheese.utils.converter.ConverterFactory;
import com.coocaa.meht.api.cheese.vo.HighSeaCustomerVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-15
 */
@Service
public class HighSeaCustomerService {

    @Autowired
    private IBuildingRatingService buildingRatingService;

    @Autowired
    private RsaExample rsaExample;

    @Autowired
    private PermissionHandler permissionHandler;

    @Autowired
    private ConverterFactory converterFactory;

    @Autowired
    private CodeNameHelper codeNameHelper;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private AgentPersonnelService agentPersonnelService;

    /**
     * 系统触发，自动入公海原因没有配置字典
     */
    private static final String REASON_SYSTEM_TRIGGERED = "系统触发";

    public PageResponseVo<HighSeaCustomerVO> list(PageRequestVo<HighSeaCustomerParam> param) {
        // 访问权限
        Map<Integer, String> cityMapping = codeNameHelper.getCityMapping(param.getQuery().getCities());
        PermissionDTO cmsPermission = permissionHandler.getCmsPermission(null, cityMapping.values());
        if (Objects.isNull(cmsPermission.getCities())) {
            // 没有符合权限的条件，直接返回空数据
            return new PageResponseVo<>();
        }

        // 设置有权限的城市
        param.getQuery().setCityNames(cmsPermission.getCities());

        IPage<BuildingRatingEntity> page = new Page<>(
                Optional.ofNullable(param.getCurrentPage()).orElse(1L),
                Optional.ofNullable(param.getPageSize()).orElse(10));

        // 日期转时间
        param.getQuery().dateToTime();

        IPage<BuildingRatingEntity> pageResult = buildingRatingService.listHighSeaCustomer(page,
                BuildingRatingConvert.INSTANCE.toCustomerQueryDto(param.getQuery()));

        if (CollUtil.isEmpty(pageResult.getRecords())) {
            return new PageResponseVo<>();
        }

        Set<String> submitUserCodes = new HashSet<>(pageResult.getRecords().size());
        PageResponseVo<HighSeaCustomerVO> pageResponse = new PageResponseVo<>();
        if (CollUtil.isNotEmpty(pageResult.getRecords())) {
            pageResponse.setCurrentPage(pageResult.getCurrent());
            pageResponse.setTotalRows(pageResult.getTotal());
            pageResponse.setTotal(pageResult.getTotal());
            pageResponse.setPageSize(pageResult.getSize());
            pageResponse.setTotalPages(pageResult.getPages());
            pageResponse.setRows(pageResult.getRecords().stream().map(item -> {
                item.setMapAddress(rsaExample.decryptByPrivate(item.getMapAddress()));
                submitUserCodes.add(item.getSubmitUser());
                HighSeaCustomerVO highSeaCustomerVO =  BuildingRatingConvert.INSTANCE.toHighSeaCustomerVo(item);
//                if (StringUtils.isNotBlank(item.getProjectReviewLevel())) {
//                    highSeaCustomerVO.setProjectLevel(item.getProjectReviewLevel());
//                }
                return highSeaCustomerVO;
            }).collect(Collectors.toList()));
        }

        Map<String, String> nameMapping = sysUserService.getNameMapping(submitUserCodes);
        submitUserCodes.removeAll(nameMapping.keySet());
        nameMapping.putAll(agentPersonnelService.getNameMapping(submitUserCodes));
        pageResponse.getRows().forEach(vo -> {
            // 设置负责人名称
            if (nameMapping.containsKey(vo.getSubmitUser())) {
                vo.setSubmitUserName(nameMapping.get(vo.getSubmitUser()) + "(" + vo.getSubmitUser() + ")");
            } else {
                vo.setSubmitUserName(vo.getSubmitUser());
            }

            // 入公海原因“系统触发”没有配置字典，需要单独处理
            if (REASON_SYSTEM_TRIGGERED.equals(vo.getEnterSeaReason())) {
                vo.setEnterSeaReasonName(REASON_SYSTEM_TRIGGERED);
            }
        });

        converterFactory.convert(pageResponse.getRows());

        return pageResponse;
    }

}
