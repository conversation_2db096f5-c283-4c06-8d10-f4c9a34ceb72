package com.coocaa.meht.api.cheese.mqtt;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024-10-17 11:51:42
 * <p> mqtt工具 </p>
 */
public final class MqttUtils {

    /**
     * 获取消息id
     *
     * @return id
     */
    public static String getMsgId() {
        return MD5Encode.getMD5Str(System.currentTimeMillis() + UUID.randomUUID().toString());
    }


    /**
     * 拼接客户端id
     */
    public static String getClientId() {
        return "server-" + HostUtil.getHostName() + "-" + System.currentTimeMillis();
    }


}
