package com.coocaa.meht.api.cheese.bean.usertag;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 用户标签详情DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
public class UserTagRelationDTO {

    @Schema(description = "用户标签ID")
    @NotNull(message = "用户标签ID不能为空")
    private Long userTagId;

    @Schema(description = "工号列表")
    @NotEmpty(message = "工号列表不能为空")
    private List<String> empCodes;
} 