package com.coocaa.meht.api.cheese.bean.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BuildingMetaManagerVO {
    @Schema(description = "楼宇编码", type = "String", example = "BCXXXX")
    private String buildingMetaNo;

    @Schema(description = "姓名", type = "String", example = "张三")
    private String name;

    @Schema(description = "工号", type = "String", example = "CC2362")
    private String wno;

    @Schema(description = "手机号", type = "String", example = "13012345678")
    private String mobile;
}
