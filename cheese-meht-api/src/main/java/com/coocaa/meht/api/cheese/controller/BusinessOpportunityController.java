package com.coocaa.meht.api.cheese.controller;

import com.coocaa.meht.api.cheese.bean.ComboBoxVO;
import com.coocaa.meht.api.cheese.bean.building.BusinessOpportunityComboBoxQueryParam;
import com.coocaa.meht.api.cheese.bean.building.BusinessOpportunityModifyParam;
import com.coocaa.meht.api.cheese.bean.building.BusinessOpportunityQueryParam;
import com.coocaa.meht.api.cheese.bean.point.PointImportResultVO;
import com.coocaa.meht.api.cheese.bean.point.PointVO;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.service.BusinessOpportunityService;
import com.coocaa.meht.api.cheese.vo.BusinessOpportunityVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-27
 */
@Tag(name = "商机管理")
@RestController
@RequestMapping("/business-opportunity")
public class BusinessOpportunityController {

    @Autowired
    private BusinessOpportunityService businessOpportunityService;

    @Operation(summary = "分页查询商机列表")
    @PostMapping("/list-page")
    public ResultTemplate<PageResponseVo<BusinessOpportunityVO>> listPage(
            @RequestBody PageRequestVo<BusinessOpportunityQueryParam> param) {
        return ResultTemplate.success(businessOpportunityService.listPage(param));
    }

    @Operation(summary = "导出商机列表")
    @PostMapping("/export")
    public ResultTemplate<String> export(@RequestBody PageRequestVo<BusinessOpportunityQueryParam> param) {
        return ResultTemplate.success(businessOpportunityService.export(param));
    }

    @Operation(summary = "修改商机状态")
    @PutMapping("/modify-status")
    public ResultTemplate<Void> modifyStatus(@Validated @RequestBody BusinessOpportunityModifyParam param) {
        businessOpportunityService.modifyStatus(param);
        return ResultTemplate.success();
    }

    @Operation(summary = "分页查询商机相关下拉框数据列表")
    @PostMapping("/list-combo-box-page")
    public ResultTemplate<PageResponseVo<ComboBoxVO>> listComboBoxPage(
            @RequestBody PageRequestVo<BusinessOpportunityComboBoxQueryParam> param) {
        return ResultTemplate.success(businessOpportunityService.listComboBoxPage(param));
    }

    @Operation(summary = "导入点位明细")
    @PostMapping("/import-point")
    public ResultTemplate<PointImportResultVO> importPoint(
            @Schema(description = "商机id") @RequestParam("id") Integer id,
            @Schema(description = "Excel文件") @RequestParam("file") MultipartFile file) {
        return ResultTemplate.success(businessOpportunityService.importPoint(id, file));
    }

    @Operation(summary = "获取商机下的点位列表")
    @GetMapping("/{id}/list-point")
    public ResultTemplate<List<PointVO>> listPoint(@Schema(description = "商机id") @PathVariable("id") Integer id) {
        return ResultTemplate.success(businessOpportunityService.listPoint(id));
    }

    @Operation(summary = "下载导入模板")
    @GetMapping("/download-template")
    public ResultTemplate<String> downloadTemplate() {
        return ResultTemplate.success(businessOpportunityService.downloadTemplate());
    }

}
