package com.coocaa.meht.api.cheese.controller;

import com.coocaa.meht.api.cheese.bean.building.HighSeaCustomerParam;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.common.tools.utils.UserCodeUtils;
import com.coocaa.meht.api.cheese.rpc.FeignMehtRpc;
import com.coocaa.meht.api.cheese.rpc.bean.CustomerTransferIntoDTO;
import com.coocaa.meht.api.cheese.rpc.bean.CustomerTransferOutDTO;
import com.coocaa.meht.api.cheese.service.HighSeaCustomerService;
import com.coocaa.meht.api.cheese.vo.HighSeaCustomerVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-15
 */
@Tag(name = "公海客户管理")
@RestController
@RequestMapping("/high-sea-customer")
public class HighSeaCustomerController {

    @Autowired
    private HighSeaCustomerService highSeaCustomerService;

    @Autowired
    private FeignMehtRpc feignMehtRpc;

    @Operation(summary = "分页查询公海客户列表")
    @PostMapping("/list")
    public ResultTemplate<PageResponseVo<HighSeaCustomerVO>> list(@RequestBody PageRequestVo<HighSeaCustomerParam> param) {
        return ResultTemplate.success(highSeaCustomerService.list(param));
    }

    @PostMapping("/transfer-into")
    @Operation(summary = "批量放入公海")
    public ResultTemplate<Void> transferInto(@RequestBody CustomerTransferIntoDTO param) {
        param.setBachOperateFlag(true);
        return feignMehtRpc.transferInto(param);
    }

    @PostMapping("/transfer-out")
    @Operation(summary = "批量客户分配")
    public ResultTemplate<Void> transferOut(@RequestBody CustomerTransferOutDTO param) {
        param.setBachOperateFlag(true);
        return feignMehtRpc.transferOut(param);
    }

}
