package com.coocaa.meht.api.cheese.utils;

import com.coocaa.meht.api.cheese.exception.BusinessException;
import com.coocaa.meht.api.cheese.rpc.meht.Result;

/**
 * <AUTHOR>
 * @since 2024/12/21
 */

public class MehtResultUtils {
    public static <T> T unbox(Result<T> result) {
        int code = result.getCode();
        if(code==200){
            return result.getData();
        }
        throw new BusinessException("楼宇评级项目调用失败");
    }
}
