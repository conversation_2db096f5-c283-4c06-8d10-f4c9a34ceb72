package com.coocaa.meht.api.cheese.rpc.meht.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "点位图片返回对象")
public class SspPointPicVO {

    @Schema(description = "图片id")
    private Integer id;

    @Schema(description = "点位ID")
    private Integer pointId;

    @Schema(description = "图片URL")
    private String pic;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

} 