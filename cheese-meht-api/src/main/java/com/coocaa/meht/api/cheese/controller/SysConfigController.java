package com.coocaa.meht.api.cheese.controller;/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-13
 */

import com.coocaa.meht.api.cheese.common.db.entity.SysConfigEntity;
import com.coocaa.meht.api.cheese.common.db.service.ISysConfigService;
import com.coocaa.meht.api.cheese.common.tools.query.exception.CommonException;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 系统配置管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-13
 */
@Slf4j
@RestController
@Tag(name = "系统配置管理", description = "系统配置管理")
@RequestMapping("/sys/config")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SysConfigController {
    private final ISysConfigService sysConfigService;

    @Operation(summary = "修改配置", hidden = true)
    @PutMapping("/hack/change-value")
    public ResultTemplate<Boolean> changeConfig(
            @RequestParam(name = "id", required = false) Long id,
            @RequestParam(name = "key", required = false) String key,
            @RequestParam(name = "value") String value) {
        if (Objects.isNull(id) && StringUtils.isBlank(key)) {
            return ResultTemplate.fail("ID或Key不正确");
        }

        if (StringUtils.isBlank(value)) {
            return ResultTemplate.fail("配置的值不正确");
        }


        // 查数据
        List<SysConfigEntity> entities = sysConfigService.lambdaQuery()
                .select(SysConfigEntity::getId, SysConfigEntity::getKey)
                .eq(Objects.nonNull(id), SysConfigEntity::getId, id)
                .eq(StringUtils.isNotBlank(key), SysConfigEntity::getKey, key)
                .list();
        if (CollectionUtils.isEmpty(entities) || entities.size() > 1) {
            throw new CommonException("ID或Key不正确");
        }

        // 改数据
        boolean result = sysConfigService.lambdaUpdate()
                .set(SysConfigEntity::getValue, value.trim())
                .eq(SysConfigEntity::getId, entities.get(0).getId())
                .update();

        return ResultTemplate.success(result);
    }
}