package com.coocaa.meht.api.cheese.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.coocaa.meht.api.cheese.bean.CustomerFollowRecordQuery;
import com.coocaa.meht.api.cheese.common.db.dto.CustomerFollowRecordListDto;
import com.coocaa.meht.api.cheese.common.tools.bean.common.CodeNameVO;
import com.coocaa.meht.api.cheese.common.tools.easyexcel.EasyExcelUtils;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.exception.BusinessException;
import com.coocaa.meht.api.cheese.rpc.FeignCmsRpc;
import com.coocaa.meht.api.cheese.service.CustomerFollowRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-21
 */


@Tag(name = "客户跟进记录管理")
@RestController
@RequestMapping("/follow/record")
@Slf4j
public class FollowRecordController {

    @Resource
    private CustomerFollowRecordService customerFollowRecordService;

    @Resource
    private FeignCmsRpc feignCmsRpc;

    @Operation(summary = "分页获取客户跟进记录管理")
    @PostMapping("/list")
    public PageResponseVo<CustomerFollowRecordListDto> page(@RequestBody PageRequestVo<CustomerFollowRecordQuery> query) {
        return customerFollowRecordService.selectPage(query);
    }

    @Operation(summary = "导出客户跟进记录管理")
    @PostMapping("/export")
    public ResultTemplate<String> export(@RequestBody PageRequestVo<CustomerFollowRecordQuery> queryParam) {
        queryParam.setPageSize(-1);
        queryParam.setCurrentPage(1L);
        PageResponseVo<CustomerFollowRecordListDto> responseVo = customerFollowRecordService.selectPage(queryParam);
        if (responseVo == null || CollUtil.isEmpty(responseVo.getRows())) {
            throw new BusinessException("未查询出有效数据");
        }
        try {
            String url = EasyExcelUtils.createExcelToCos("客户跟进记录", responseVo.getRows(), null,
                    String.format("客户跟进记录-%s.xlsx", LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN)), "followRecord");
            return ResultTemplate.success(url);
        } catch (IOException e) {
            log.error("导出客户跟进记录失败", e);
            throw new BusinessException("导出客户跟进记录失败");
        }
    }

    @Operation(summary = "查看图片")
    @GetMapping("/{id}/images")
    public ResultTemplate<List<String>> viewPic(@PathVariable("id") Integer id) {
        return ResultTemplate.success(customerFollowRecordService.viewPic(id));
    }

    @Operation(summary = "模糊查询商机编码")
    @PostMapping("/query-business-code")
    public PageResponseVo<CodeNameVO> queryBusinessCode(@RequestBody PageRequestVo<String> requestVo) {
        return customerFollowRecordService.queryBusinessCode(requestVo.getCurrentPage().intValue(), requestVo.getPageSize(), requestVo.getQuery());
    }

    @Operation(summary = "模糊查询商机名称")
    @PostMapping("/query-business-name")
    public PageResponseVo<CodeNameVO> queryBusinessName(@RequestBody PageRequestVo<String> requestVo) {
        return customerFollowRecordService.queryBusinessName(requestVo.getCurrentPage().intValue(), requestVo.getPageSize(), requestVo.getQuery());
    }

    @Operation(summary = "模糊查询项目名称")
    @PostMapping("/query-project-name")
    public PageResponseVo<CodeNameVO> queryProjectName(@RequestBody PageRequestVo<String> requestVo) {
        if (StringUtils.isBlank(requestVo.getQuery())){
            return new PageResponseVo<>();
        }
        return customerFollowRecordService.queryProjectName(requestVo.getCurrentPage().intValue(), requestVo.getPageSize(), requestVo.getQuery());
    }

    @Operation(summary = "模糊查询项目城市")
    @PostMapping("/query-project-city")
    public PageResponseVo<CodeNameVO> queryProjectCity(@RequestBody PageRequestVo<String> requestVo) {
        return customerFollowRecordService.queryProjectCity(requestVo.getCurrentPage().intValue(), requestVo.getPageSize(), requestVo.getQuery());
    }

//    @Operation(summary = "模糊查询负责人")
//    @GetMapping("/query-submit-user/{name}")
//    public PageResponseVo<CodeNameVO> querySubmitUser(@RequestParam("currentPage") Integer currentPage, @RequestParam("pageSize") Integer pageSize, @PathVariable("name") String name) {
//        return customerFollowRecordService.queryProjectName(currentPage, pageSize, name);
//    }


    /**
     * 获取所有大区信息
     */
    @Operation(summary = "获取所有大区信息")
    @GetMapping("/region/all")
    ResultTemplate<List<String>> getAllRegionInfo() {
        return feignCmsRpc.getAllRegionInfo();
    }
}
