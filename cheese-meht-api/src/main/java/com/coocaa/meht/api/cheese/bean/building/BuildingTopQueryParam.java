package com.coocaa.meht.api.cheese.bean.building;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20
 */
@Data
public class BuildingTopQueryParam {
    /**
     * 楼宇名称
     */
    @Schema(description = "楼宇名称")
    private String buildingName;

    /**
     * 省份
     */
    @Schema(description = "省份")
    private String province;
    /**
     * 省份
     */
    @Schema(description = "省份")
    private List<String> provinces;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String city;
    /**
     * 城市
     */
    @Schema(description = "城市")
    private List<String> cities;

    /**
     * TOP等级
     */
    @Schema(description = "TOP等级")
    private String topLevel;
    /**
     * TOP等级
     */
    @Schema(description = "TOP等级")
    private List<String> topLevels;

    /**
     * 楼宇类型
     */
    @Schema(description = "楼宇类型")
    private String buildingType;
    /**
     * 楼宇类型
     */
    @Schema(description = "楼宇类型")
    private List<String> buildingTypes;

}
