package com.coocaa.meht.api.cheese.vo.building;

import com.coocaa.meht.api.cheese.utils.converter.Convert;
import com.coocaa.meht.api.cheese.utils.converter.ConvertType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 楼宇元数据列表返回对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "楼宇元数据列表返回对象")
public class BuildingMetaListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "楼宇编码(BC打头)")
    private String buildingMetaNo;

    @Schema(description = "楼宇认证状态(0:未认证,1:认证中,2:冻结中,3:已认证)")
    @Convert(type = ConvertType.BUILDING_STATUS)
    private Integer buildingStatus;
    private String buildingStatusName;

    @Schema(description = "认证楼宇类型(0:写字楼,1:商住楼,2:综合体,3:产业园区)")
    @Convert(type = ConvertType.BUILDING_TYPE)
    private Integer buildingType;
    private String buildingTypeName;

    /*@Schema(description = "AI楼宇类型(0:写字楼,1:商住楼,2:综合体,3:产业园区)")
    private Integer buildingTypeAi;*/

    @Schema(description = "楼宇名称")
    private String buildingName;

    @Schema(description = "地图楼宇名称-AI")
    private String buildingNameAi;

    @Schema(description = "地图UID")
    private String mapNo;

    @Schema(description = "省名称")
    private String mapProvince;

    @Schema(description = "市名称")
    private String mapCity;

    @Schema(description = "区名称")
    private String mapRegion;

    @Schema(description = "详细地址")
    private String mapAddress;

    @Schema(description = "纬度")
    private String mapLatitude;

    @Schema(description = "经度")
    private String mapLongitude;

    /*@Schema(description = "行政区域编码")
    private String mapAdCode;*/
    @Schema(description = "AI等级评价")
    private String projectLevelAi;

    @Schema(description = "AI等级评分")
    private BigDecimal buildingAiScore;

    @Schema(description = "认证得分")
    private BigDecimal buildingScore;

    @Schema(description = "认证等级")
    private String projectLevel;

    @Schema(description = "认证时间")
    private Date successTime;

    @Schema(description = "认证生效开始日期")
    private Date authenticationStart;

    @Schema(description = "认证失效时间")
    private Date authenticationEnd;

    @Schema(description = "最后一个成功认证的编号")
    private String buildingRatingNo;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "创建方式(人工提交/系统爬取)")
    @Convert(type = ConvertType.DICT)
    private String createType;
    private String createTypeName;

    @Schema(description = "目标点位数量")
    private Integer targetPointCount;

    @Schema(description = "竞媒点位数量")
    private Integer competitorPointCount;

    @Schema(description = "负责人")
    @Convert(type = ConvertType.USER_CODE)
    private String manager;
    private String managerName;

    @Schema(description = "最高楼层")
    private Integer floorTotalNumber;

    @Schema(description = "总楼栋数量")
    private Integer buildingTotalNumber;

    @Schema(description = "总单元数量")
    private Integer unitsTotalNumber;

    @Schema(description = "总等候厅数")
    private Integer hallTotalNumber;

    @Schema(description = "禁忌行业code", type = "String", example = "1")
    private List<String> forbiddenIndustry;

    @Schema(description = "禁忌行业名称", type = "String", example = "1")
    private String forbiddenIndustryName;

    @Schema(description = "平均租金")
    private BigDecimal averageRent;

    @Schema(description = "楼宇图片")
    private List<String> buildingImgList;

}
