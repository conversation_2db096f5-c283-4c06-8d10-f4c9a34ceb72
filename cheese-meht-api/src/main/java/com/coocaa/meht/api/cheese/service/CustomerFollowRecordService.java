package com.coocaa.meht.api.cheese.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.api.cheese.bean.CustomerFollowRecordQuery;
import com.coocaa.meht.api.cheese.common.db.bean.CustomerFollowRecordQueryDto;
import com.coocaa.meht.api.cheese.common.db.dto.CustomerFollowRecordListDto;
import com.coocaa.meht.api.cheese.common.db.entity.BusinessOpportunityEntity;
import com.coocaa.meht.api.cheese.common.db.entity.CustomerFollowRecordEntity;
import com.coocaa.meht.api.cheese.common.db.entity.FollowRecordPicEntity;
import com.coocaa.meht.api.cheese.common.db.entity.SysUserEntity;
import com.coocaa.meht.api.cheese.common.db.mapper.FollowRecordPicMapper;
import com.coocaa.meht.api.cheese.common.db.service.IBusinessOpportunityService;
import com.coocaa.meht.api.cheese.common.db.service.ICustomerFollowRecordService;
import com.coocaa.meht.api.cheese.common.db.service.ISysUserService;
import com.coocaa.meht.api.cheese.common.tools.bean.common.CodeNameVO;
import com.coocaa.meht.api.cheese.common.tools.enums.BooleFlagEnum;
import com.coocaa.meht.api.cheese.common.tools.query.exception.CommonException;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.common.tools.utils.AesUtils;
import com.coocaa.meht.api.cheese.common.tools.utils.RpcUtils;
import com.coocaa.meht.api.cheese.common.tools.utils.UserIdUtils;
import com.coocaa.meht.api.cheese.convert.FollowRecordConvert;
import com.coocaa.meht.api.cheese.enums.BusinessTypeEnum;
import com.coocaa.meht.api.cheese.enums.DataAccessTypeEnum;
import com.coocaa.meht.api.cheese.enums.FollowTypeEnum;
import com.coocaa.meht.api.cheese.rpc.FeignAuthorityRpc;
import com.coocaa.meht.api.cheese.rpc.FeignCmsRpc;
import com.coocaa.meht.api.cheese.rpc.bean.UserDataAccessV2DTO;
import com.coocaa.meht.api.cheese.utils.CodeNameHelper;
import com.coocaa.meht.api.cheese.vo.UserVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 客户跟进
 * @since 2025-03-21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerFollowRecordService {

    private final ICustomerFollowRecordService customerFollowRecordService;

    private final FollowRecordConvert followRecordConvert;

    private final FollowRecordPicMapper followRecordPicMapper;

    private final FeignAuthorityRpc feignAuthorityRpc;

    private final FeignCmsRpc feignCmsRpc;

    private final BuildingRatingService buildingRatingService;

    private final IBusinessOpportunityService businessOpportunityService;

    private final CodeNameHelper codeNameHelper;

    private final ISysUserService sysUserService;

    public PageResponseVo<CustomerFollowRecordListDto> selectPage(PageRequestVo<CustomerFollowRecordQuery> query) {
        // 设置默认查询条件
        setDefaultQuery(query);
        // 数据权限
        permissionHandle(query);
        // 地区查询条件
        regionHandler(query);
        CustomerFollowRecordQueryDto queryDto = followRecordConvert.toQueryDto(query.getQuery());
        Page<CustomerFollowRecordQueryDto> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        PageResponseVo<CustomerFollowRecordListDto> result = customerFollowRecordService.selectPage(page, queryDto);
        fillFiled(result);
        return result;
    }

    private void permissionHandle(PageRequestVo<CustomerFollowRecordQuery> query) {
        Integer userId = UserIdUtils.getUserId();
        UserVO userVO = RpcUtils.unBox(feignAuthorityRpc.getWno(userId));
        if (ObjectUtil.isNull(userVO) || StrUtil.isEmpty(userVO.getWno())) {
            throw new CommonException("获取用户信息失败");
        }
        UserDataAccessV2DTO dataAccessV2DTO = RpcUtils.unBox(feignCmsRpc.getUserDataAccessV2());
        log.info("权限数据userDataAccessV2:{}", JSON.toJSONString(dataAccessV2DTO));
        if (Objects.isNull(dataAccessV2DTO)) {
            throw new CommonException("未配置数据权限");
        }

        // 控制数据权限
        List<String> userCodes = new ArrayList<>();
        userCodes.add(userVO.getWno());
        String accessType = Optional.of(dataAccessV2DTO)
                .map(UserDataAccessV2DTO::getAccessType).orElse(DataAccessTypeEnum.SELF.getCode());
        if (StringUtils.equals(accessType, DataAccessTypeEnum.ALL.getCode())
                || StringUtils.equals(accessType, DataAccessTypeEnum.CITY_ALL.getCode())) {
            userCodes.clear();
        } else if (StringUtils.equals(accessType, DataAccessTypeEnum.SELF_AND_SUB.getCode())) {
            userCodes.addAll(Optional.ofNullable(feignAuthorityRpc.listUserByIds(dataAccessV2DTO.getUserIds()))
                    .map(ResultTemplate::getData)
                    .map(users -> users.stream().map(CodeNameVO::getCode).toList())
                    .orElse(Collections.emptyList()));
            // 处理有无负责人查询条件
            if (CollUtil.isEmpty(query.getQuery().getCreateBy())) {
                query.getQuery().setCreateBy(userCodes);
            } else {
                List<String> intersection = CollectionUtils.intersection(userCodes, query.getQuery().getCreateBy())
                        .stream().toList();
                if (CollUtil.isEmpty(intersection)) {
                    throw new CommonException("无所选负责人数据权限");
                }
                query.getQuery().setCreateBy(intersection);
            }
        } else if (StringUtils.equals(accessType, DataAccessTypeEnum.SELF.getCode())) {
            if (CollUtil.isEmpty(query.getQuery().getCreateBy())) {
                query.getQuery().setCreateBy(List.of(userVO.getWno()));
            } else {
                if (CollUtil.contains(query.getQuery().getCreateBy(), userVO.getWno())) {
                    query.getQuery().setCreateBy(List.of(userVO.getWno()));
                } else {
                    throw new CommonException("无所选跟进人数据权限");
                }
            }
        }

        //城市数据权限
        List<Integer> cityIds = dataAccessV2DTO.getCityIds();
        if (CollUtil.isEmpty(cityIds)) {
            throw new CommonException("城市数据权限为空");
        }
        ResultTemplate<List<CodeNameVO>> listResultTemplate = feignAuthorityRpc.listCityByIds(cityIds);
        log.info("城市权限数据:{}", JSON.toJSONString(listResultTemplate));
        List<String> cityList = listResultTemplate.getData().stream().map(CodeNameVO::getName).toList();
        if (CollUtil.isEmpty(query.getQuery().getCities())) {
            query.getQuery().setCities(cityList);
        } else {
            List<String> intersection = CollectionUtils.intersection(cityList, query.getQuery().getCities()).stream().toList();
            if (CollUtil.isEmpty(intersection)) {
                log.info("所选择城市不在城市数据权限中,数据权限列表{} , 查询条件{}", cityList, query.getQuery().getCities());
                throw new CommonException("所选择城市不在城市数据权限中");
            }
            query.getQuery().setCities(intersection);
        }
    }

    private void regionHandler(PageRequestVo<CustomerFollowRecordQuery> query) {
        //大区查询条件处理
        if (CollUtil.isEmpty(query.getQuery().getRegions())) {
            return;
        }
        // 查询大区下的所有城市
        List<String> cities = RpcUtils.unBox(feignCmsRpc.getCityNameByRegions(query.getQuery().getRegions()));
        if (CollUtil.isNotEmpty(query.getQuery().getCities()) && CollUtil.isNotEmpty(cities)) {
            // 取两个list的交集
            List<String> intersection = CollectionUtils.intersection(cities, query.getQuery().getCities()).stream().toList();
            if (CollUtil.isEmpty(intersection)) {
                log.info("所选择城市不在大区中,所选大区下城市列表{} , 查询条件{}", cities, query.getQuery().getCities());
                throw new CommonException("所选择城市不在大区中");
            }
            query.getQuery().setCities(intersection);
        } else {
            query.getQuery().setCities(cities);
        }
    }

    /**
     * 填充字段
     *
     * @param result
     */
    private void fillFiled(PageResponseVo<CustomerFollowRecordListDto> result) {
        if (result == null || CollUtil.isEmpty(result.getRows())) {
            return;
        }
        // 商机编码-城市
        List<CodeNameVO> businessRatingList = buildingRatingService.getBusinessRatingMap(result.getRows().stream()
                .map(CustomerFollowRecordListDto::getBusinessCode).toList());
        // 商机编码-城市   组装成map
        Map<String, String> businessRatingMap = businessRatingList.stream().collect(Collectors
                .toMap(CodeNameVO::getCode, CodeNameVO::getName, (oldValue, newValue) -> oldValue));
        // 城市对应大区map
        Map<String, String> regionInfoMap = RpcUtils.unBox(feignCmsRpc.getRegionInfoByCityNames(businessRatingList.stream()
                .map(CodeNameVO::getName).distinct().toList()));
        // 角色字典map
        Map<String, String> roleMap = codeNameHelper.getDictMapping(result.getRows().stream()
                .map(CustomerFollowRecordListDto::getRole).distinct().toList());
        List<SysUserEntity> lists = sysUserService.userDetail(result.getRows().stream()
                .map(CustomerFollowRecordListDto::getCreateBy).filter(StrUtil::isNotBlank).distinct().toList());

        Map<String, List<SysUserEntity>> map = lists.stream().collect(Collectors.groupingBy(SysUserEntity::getEmpCode));
        Map<String, String> userMap = map.entrySet().stream().map(e -> {
            SysUserEntity entity = e.getValue().get(0);
            return Map.entry(e.getKey(), entity.getRealName() + "(" + entity.getEmpCode() + ")");
        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        result.getRows().forEach(item -> {
            Optional.ofNullable(item.getBusinessType()).ifPresent(r -> item.setBusinessTypeName(BusinessTypeEnum.getDesc(item.getBusinessType())));
            Optional.ofNullable(item.getBusinessCode()).ifPresent(r -> item.setRegion(regionInfoMap.get(businessRatingMap.get(item.getBusinessCode()))));
            Optional.of(FollowTypeEnum.valueOf(item.getVisitType())).ifPresent(f -> item.setVisitTypeName(f.getDesc()));
            Optional.ofNullable(item.getRole()).ifPresent(r -> item.setRoleName(roleMap.get(item.getRole())));
            Optional.ofNullable(item.getValid()).ifPresent(r -> item.setValidName(BooleFlagEnum.getDesc(item.getValid())));
            if (StrUtil.isNotBlank(item.getPhone())) {
                item.setPhone(AesUtils.decryptStr(item.getPhone()));
            }
            Optional.ofNullable(item.getCreateBy()).ifPresent(r -> item.setCreateName(userMap.get(item.getCreateBy())));
        });
    }

    private void setDefaultQuery(PageRequestVo<CustomerFollowRecordQuery> entity) {
//        if (null == entity.getQuery().getBusinessType()) {
//            entity.getQuery().setBusinessType(BusinessTypeEnum.SELF.getCode());
//        }
//        if (null == entity.getQuery().getValid()) {
//            entity.getQuery().setValid(1);
//        }
        if (StrUtil.isNotBlank(entity.getQuery().getFollowTimeEnd())) {
            entity.getQuery().setFollowTimeEnd(DateUtil.format(DateUtil.endOfDay(DateUtil.parse(entity.getQuery().getFollowTimeEnd())), "yyyy-MM-dd HH:mm:ss"));
        }
        if (StrUtil.isNotBlank(entity.getQuery().getCreateTimeEnd())) {
            entity.getQuery().setCreateTimeEnd(DateUtil.format(DateUtil.endOfDay(DateUtil.parse(entity.getQuery().getCreateTimeEnd())), "yyyy-MM-dd HH:mm:ss"));
        }
    }

    public List<String> viewPic(Integer followId) {
        if (null == followId) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<FollowRecordPicEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(FollowRecordPicEntity::getFollowId, followId);
        return followRecordPicMapper.selectList(queryWrapper).stream().map(FollowRecordPicEntity::getPicUrl).toList();
    }

    public PageResponseVo<CodeNameVO> queryBusinessCode(Integer currentPage, Integer pageSize, String code) {
        Page<CustomerFollowRecordEntity> page = new Page<>(currentPage, pageSize);
        if (StringUtils.isNotBlank(code)) {
            customerFollowRecordService.query().select(" distinct business_code ")
                    .like(StrUtil.isNotBlank(code), "business_code", code).page(page);
            return new PageResponseVo<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), page.getRecords().stream()
                    .map(item -> new CodeNameVO(null, null, null, item.getBusinessCode(), null)).toList(), page.getTotal());
        }
        return new PageResponseVo<>();
    }

    public PageResponseVo<CodeNameVO> queryBusinessName(Integer currentPage, Integer pageSize, String name) {
        Page<BusinessOpportunityEntity> page = new Page<>(currentPage, pageSize);
        if (StringUtils.isNotBlank(name)) {
            businessOpportunityService.query()
                    .select(" distinct name")
                    .like(StrUtil.isNotBlank(name), "name", name)
                    .last(" and exists (" +
                            "select 1 from customer_follow_record cfr where business_code  = code)")
                    .page(page);
            return new PageResponseVo<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), page.getRecords().stream()
                    .map(item -> new CodeNameVO(null, null, null, item.getName(), null)).toList(), page.getTotal());
        }
        return new PageResponseVo<>();
    }

    public PageResponseVo<CodeNameVO> queryProjectName(Integer currentPage, Integer pageSize, String name) {
        return customerFollowRecordService.queryProjectName(currentPage, pageSize, name);
    }

    public PageResponseVo<CodeNameVO> queryProjectCity(Integer currentPage, Integer pageSize, String name) {
        return customerFollowRecordService.queryProjectName(currentPage, pageSize, name);
    }
}
