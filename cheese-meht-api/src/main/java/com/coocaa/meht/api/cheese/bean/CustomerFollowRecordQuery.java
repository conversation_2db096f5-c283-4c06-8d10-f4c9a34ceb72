package com.coocaa.meht.api.cheese.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-21
 */
@Data
public class CustomerFollowRecordQuery {

    @Schema(description = "商机编码")
    private String businessCode;

    @Schema(description = "商机名称")
    private String businessName;

    @Schema(description = "项目名称")
    private String projectName;

    /**
     * 跟进方式
     */
    @Schema(description = "跟进方式")
    private String visitType;


    @Schema(description = "负责人")
    private List<String> createBy;

    @Schema(description = "是否有效跟进 [0 无效 , 1有效]")
    private Integer valid = 1;

    @Schema(description = "业务类型  []")
    private Integer businessType;

    @Schema(description = "所属大区")
    private String region;

    @Schema(description = "城市")
    private List<String> cities;

    @Schema(description = "跟进开始时间-开始")

    private String followTimeStart;

    @Schema(description = "跟进结束时间-结束")
    private String followTimeEnd;

    @Schema(description = "创建时间-开始")
    private String createTimeStart;

    @Schema(description = "创建时间-结束")
    private String createTimeEnd;

    private List<String> createdBy;


    /**
     * 所属大区
     */
    @Schema(description = "所属大区")
    private List<String> regions;

}
