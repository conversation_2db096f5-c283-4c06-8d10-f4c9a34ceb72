package com.coocaa.meht.api.cheese.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.api.cheese.bean.CoreAreaJudgeParam;
import com.coocaa.meht.api.cheese.bean.CoreAreaParam;
import com.coocaa.meht.api.cheese.common.db.bean.CoreAreaDTO;
import com.coocaa.meht.api.cheese.common.db.entity.CoreAreaEntity;
import com.coocaa.meht.api.cheese.common.db.service.ICoreAreaService;
import com.coocaa.meht.api.cheese.common.tools.bean.common.CodeNameVO;
import com.coocaa.meht.api.cheese.common.tools.constant.Constants;
import com.coocaa.meht.api.cheese.common.tools.easyexcel.EasyExcelUtils;
import com.coocaa.meht.api.cheese.common.tools.enums.BooleFlagEnum;
import com.coocaa.meht.api.cheese.common.tools.enums.CoreAreaTypeEnum;
import com.coocaa.meht.api.cheese.common.tools.enums.OperateTypeEnum;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.common.tools.utils.RpcUtils;
import com.coocaa.meht.api.cheese.common.tools.utils.StringUtils;
import com.coocaa.meht.api.cheese.config.CitySortConfig;
import com.coocaa.meht.api.cheese.convert.CoreAreaConvert;
import com.coocaa.meht.api.cheese.exception.BusinessException;
import com.coocaa.meht.api.cheese.rpc.FeignAuthorityRpc;
import com.coocaa.meht.api.cheese.rpc.FeignCmsRpc;
import com.coocaa.meht.api.cheese.rpc.bean.UserDataAccessV2DTO;
import com.coocaa.meht.api.cheese.utils.CoreAreaUtils;
import com.coocaa.meht.api.cheese.utils.converter.ConverterFactory;
import com.coocaa.meht.api.cheese.vo.CoreAreaCircleVO;
import com.coocaa.meht.api.cheese.vo.CoreAreaCityVO;
import com.coocaa.meht.api.cheese.vo.CoreAreaPolygonVO;
import com.coocaa.meht.api.cheese.vo.CoreAreaVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 核心区域 业务实现类
 *
 * <AUTHOR>
 * @since 2025-7-1
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CoreAreaService {
    private final ICoreAreaService coreAreaService;
    private final CoreAreaOperateRecordService recordService;

    private final FeignCmsRpc feignCmsRpc;
    private final FeignAuthorityRpc feignAuthorityRpc;

    private final ConverterFactory converterFactory;

    private final CitySortConfig citySortConfig;

    /**
     * 入公海天数阈值
     */
    @Value("${core-area.buffer-width:0}")
    public Double bufferWidth;
    /**
     * 分页查询核心区域列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页结果
     */
    public PageResponseVo<CoreAreaVO> pageList(PageRequestVo<CoreAreaParam> pageRequest) {
        // 转换查询参数
        CoreAreaDTO coreArea = CoreAreaConvert.INSTANCE.toDto(pageRequest.getQuery());
        // 分页查询
        IPage<CoreAreaEntity> pageCoreAreas = coreAreaService.pageList(getPage(pageRequest), coreArea);
        PageResponseVo<CoreAreaVO> pageResponse = CoreAreaConvert.INSTANCE.toPageResponse(pageCoreAreas);
        // 没查到数据，直接返回
        if (CollectionUtils.isEmpty(pageCoreAreas.getRecords())) {
            return pageResponse;
        }
        // 翻译字段
        converterFactory.convert(pageResponse.getRows());
        return pageResponse;
    }

    /**
     * 核心区条件列表查询
     *
     * @param coreAreaParam 查询条件
     * @return 数据列表
     */
    public List<CoreAreaCityVO> list(CoreAreaParam coreAreaParam) {

        // 获取城市核心区列表
        List<CoreAreaEntity> coreAreas = getCoreAreas(coreAreaParam);

        if (CollectionUtils.isEmpty(coreAreas)) {
            return Collections.emptyList();
        }
        // 翻译
        List<CoreAreaVO> coreAreaList = CoreAreaConvert.INSTANCE.toVoList(coreAreas);
        converterFactory.convert(coreAreaList);

        // 获取城市经纬度
        List<CodeNameVO> codeNames = RpcUtils.unBox(feignAuthorityRpc.listCityByIds(coreAreaList.stream().map(CoreAreaVO::getCityId).collect(Collectors.toList())));

        // 根据城市id分组后把城市经纬度赋过去
        Map<Integer, CodeNameVO> cityCodeNameMap = codeNames.stream().collect(Collectors.toMap(CodeNameVO::getId, one -> one));

        // 分组返回
        List<CoreAreaCityVO> coreAreaCity = new ArrayList<>();
        Map<Integer, List<CoreAreaVO>> groupByCityIds = coreAreaList.stream().collect(Collectors.groupingBy(CoreAreaVO::getCityId));
        for (Map.Entry<Integer, List<CoreAreaVO>> integerListEntry : groupByCityIds.entrySet()) {
            CoreAreaCityVO coreAreaCityVO = new CoreAreaCityVO();
            coreAreaCityVO.setCityId(integerListEntry.getKey());
            coreAreaCityVO.setCityName(integerListEntry.getValue().get(0).getCityName());
            coreAreaCityVO.setCoreAreas(integerListEntry.getValue());
            CodeNameVO codeNameVO = cityCodeNameMap.get(coreAreaCityVO.getCityId());
            // 赋值经纬度
            if (null != codeNameVO) {
                coreAreaCityVO.setLongitude(codeNameVO.getLongitude());
                coreAreaCityVO.setLatitude(codeNameVO.getLatitude());
            }
            coreAreaCity.add(coreAreaCityVO);
        }
        if (CollectionUtils.isNotEmpty(coreAreaCity)) {
            // 按照城市排序
            return sortCoreAreaCities(coreAreaCity, citySortConfig);
        }
        return coreAreaCity;
    }

    /**
     * 根据城市名称获取核心区域列表
     * 不带权限校验
     * @param cityName 城市名称
     * @return 核心区域列表
     */
    public List<CoreAreaVO> getCityCoreAreaList(String cityName) {
        if (StringUtils.isBlank(cityName)) {
            throw new BusinessException("城市名称为空！");
        }
        // 获取城市id
        CodeNameVO cityInfo = RpcUtils.unBox(feignAuthorityRpc.getByCityName(cityName));
        if (Objects.isNull(cityInfo) || Objects.isNull(cityInfo.getId())) {
            throw new BusinessException("解析城市名称失败");
        }

        List<CoreAreaEntity> coreAreas = coreAreaService.lambdaQuery()
                .eq(CoreAreaEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(CoreAreaEntity::getCityId, cityInfo.getId())
                .list();
        if (CollUtil.isEmpty(coreAreas)) {
            return Collections.emptyList();
        }
        return CoreAreaConvert.INSTANCE.toVoList(coreAreas);
    }

    /**
     * 检查并获取城市权限
     * 全部城市权限会返回所有的已启用城市列表
     *
     * @return 城市id列表
     */
    private List<Integer> checkCityPermission() {
        ResultTemplate<UserDataAccessV2DTO> userDataAccessV2 = feignCmsRpc.getUserDataAccessV2();
        UserDataAccessV2DTO accessData = userDataAccessV2.getData();
        if (Objects.isNull(accessData)) {
            throw new BusinessException("未配置数据访问权限");
        }

        List<Integer> cityIds = accessData.getCityIds();
        if (CollectionUtils.isEmpty(cityIds)) {
            log.info("无城市数据权限");
            throw new BusinessException("无城市数据权限");
        }
        return cityIds;
    }

    /**
     * 条件查询城市核心区
     *
     * @param coreAreaParam 查询条件
     * @return 数据列表
     */
    public List<CoreAreaEntity> getCoreAreas(CoreAreaParam coreAreaParam) {
        // 检查城市权限
        List<Integer> cityIds = checkCityPermission();

        return coreAreaService.lambdaQuery()
                .eq(CoreAreaEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(StringUtils.isNotBlank(coreAreaParam.getType()), CoreAreaEntity::getType, coreAreaParam.getType())
                .like(StringUtils.isNotBlank(coreAreaParam.getName()), CoreAreaEntity::getName, coreAreaParam.getName())
                .eq(null != coreAreaParam.getCityId(), CoreAreaEntity::getCityId, coreAreaParam.getCityId())
                .in(CollectionUtils.isNotEmpty(cityIds), CoreAreaEntity::getCityId, cityIds)
                .list();
    }

    /**
     * 核心区域详情
     *
     * @param id 主键ID
     * @return 核心区域详情
     */
    public CoreAreaVO getDetail(Long id) {
        return Optional.ofNullable(coreAreaService.getById(id))
                .map(coreArea -> {
                    CoreAreaVO vo = CoreAreaConvert.INSTANCE.toVo(coreArea);
                    converterFactory.convert(Collections.singleton(vo));
                    return vo;
                }).orElse(null);
    }

    /**
     * 核心区域新增
     *
     * @param param 实体参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean create(CoreAreaParam param) {
        // 核心区名字判重  同一个城市不允许重复
        boolean exists = coreAreaService.lambdaQuery()
                .eq(CoreAreaEntity::getName, param.getName())
                .eq(CoreAreaEntity::getCityId, param.getCityId())
                .eq(CoreAreaEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .exists();
        if (exists) {
            throw new BusinessException("核心区域名称已存在");
        }

        // 校验多边形是否合法
        if (CoreAreaTypeEnum.POLYGON.getCode().equals(param.getType()) || CoreAreaTypeEnum.RECTANGLE.getCode().equals(param.getType())) {
            CoreAreaUtils.judgePolygonIsValid(param.getArea());
        }

        CoreAreaEntity coreArea = CoreAreaConvert.INSTANCE.toEntity(param);
        boolean save = coreAreaService.save(coreArea);
        // 保存日志
        boolean recordSave = recordService.onCreateOrRemoveCoreArea(OperateTypeEnum.CREATE, coreArea);
        return save && recordSave;
    }

    /**
     * 核心区域修改
     *
     * @param id    id
     * @param param 实体参数
     * @return 操作结果
     */
    public boolean update(Long id, CoreAreaParam param) {
        CoreAreaEntity coreArea = null;
        if (StringUtils.isNotBlank(param.getName())) {
            // 判断长度是否超过15
            if (param.getName().length() > 15) {
                throw new BusinessException("核心区域名称长度不能超过15");
            }
            // 判断名称是否重复
            coreArea = coreAreaService.getById(id);
            if (null != coreArea && !coreArea.getName().equals(param.getName())) {
                boolean exists = coreAreaService.lambdaQuery()
                        .eq(CoreAreaEntity::getName, param.getName())
                        .eq(CoreAreaEntity::getCityId, coreArea.getCityId())
                        .eq(CoreAreaEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                        .exists();
                if (exists) {
                    throw new BusinessException("核心区域名称已存在");
                }
            }
        }

        // 校验多边形是否合法
        if (CoreAreaTypeEnum.POLYGON.getCode().equals(param.getType()) || CoreAreaTypeEnum.RECTANGLE.getCode().equals(param.getType())) {
            if (StringUtils.isNotBlank(param.getArea())) {
                CoreAreaUtils.judgePolygonIsValid(param.getArea());
            }
        }

        CoreAreaEntity coreAreaUpdate = CoreAreaConvert.INSTANCE.toEntity(param);
        coreAreaUpdate.setId(id);
        boolean save = coreAreaService.updateById(coreAreaUpdate);
        // 保存日志
        boolean recordSave = recordService.onUpdateCoreArea(coreArea, param);
        return save && recordSave;
    }

    /**
     * 核心区域删除
     *
     * @param id 主键ID
     * @return 删除结果
     */
    public boolean deleteById(Long id) {
        boolean delete = coreAreaService.lambdaUpdate()
                .set(CoreAreaEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(CoreAreaEntity::getId, id)
                .update();
        CoreAreaEntity coreArea = coreAreaService.getById(id);
        // 保存日志
        boolean recordSave = recordService.onCreateOrRemoveCoreArea(OperateTypeEnum.DELETE, coreArea);
        return delete && recordSave;
    }

    /**
     * 获取分页对象
     */
    private Page<CoreAreaEntity> getPage(PageRequestVo<?> pageRequest) {
        return new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE)
        );
    }

    /**
     * 获取当前登录人的媒资配置的城市里欸包
     *
     * @return 当前登录人的媒资配置的城市
     */
    public List<CodeNameVO> getCurrentCity() {
        // 查询城市权限
        List<Integer> cityIds = checkCityPermission();
        // 直接返回所有城市
        List<CodeNameVO> cities = Optional.ofNullable(feignAuthorityRpc.getAvailableCities())
                .map(ResultTemplate::getData).orElse(Collections.emptyList());

        List<CodeNameVO> list = cities.stream().filter(city -> cityIds.contains(city.getId())).toList();

        if (CollectionUtils.isNotEmpty(list)) {
            // 根据配置来排个顺序，sortOne > sortTwo > sortThree > sortFour ，sortOne的城市排序靠前
            return sortCities(list, citySortConfig);
        }
        return list;
    }

    /**
     * 根据排序配置对城市列表进行排序
     * @param cities 待排序的城市列表
     * @param config 排序配置
     * @return 排序后的城市列表
     */
    public List<CoreAreaCityVO> sortCoreAreaCities(List<CoreAreaCityVO> cities, CitySortConfig config) {
        if (cities == null || cities.isEmpty() || config == null) {
            return Collections.emptyList();
        }

        // 将配置中的城市名称列表转换为集合
        List<String> sortOneCities = splitCityNames(config.getSortOne());
        List<String> sortTwoCities = splitCityNames(config.getSortTwo());
        List<String> sortThreeCities = splitCityNames(config.getSortThree());
        List<String> sortFourCities = splitCityNames(config.getSortFour());

        // 创建比较器
        Comparator<CoreAreaCityVO> comparator = Comparator
                // 第一优先级：sortOne中的城市
                .comparingInt((CoreAreaCityVO city) -> getPriority(sortOneCities, city.getCityName()))
                // 第二优先级：sortTwo中的城市
                .thenComparingInt((CoreAreaCityVO city) -> getPriority(sortTwoCities, city.getCityName()))
                // 第三优先级：sortThree中的城市
                .thenComparingInt((CoreAreaCityVO city) -> getPriority(sortThreeCities, city.getCityName()))
                // 第四优先级：sortFour中的城市
                .thenComparingInt((CoreAreaCityVO city) -> getPriority(sortFourCities, city.getCityName()))
                // 最后按城市名称自然排序
                .thenComparing(CoreAreaCityVO::getCityName);

        // 执行排序
        return cities.stream()
                .sorted(comparator)
                .collect(Collectors.toList());
    }

    /**
     * 根据排序配置对城市列表进行排序
     * @param cities 待排序的城市列表
     * @param config 排序配置
     * @return 排序后的城市列表
     */
    public List<CodeNameVO> sortCities(List<CodeNameVO> cities, CitySortConfig config) {
        if (cities == null || cities.isEmpty() || config == null) {
            return Collections.emptyList();
        }

        // 将配置中的城市名称列表转换为集合
        List<String> sortOneCities = splitCityNames(config.getSortOne());
        List<String> sortTwoCities = splitCityNames(config.getSortTwo());
        List<String> sortThreeCities = splitCityNames(config.getSortThree());
        List<String> sortFourCities = splitCityNames(config.getSortFour());

        // 创建比较器
        Comparator<CodeNameVO> comparator = Comparator
                // 第一优先级：sortOne中的城市
                .comparingInt((CodeNameVO city) -> getPriority(sortOneCities, city.getName()))
                // 第二优先级：sortTwo中的城市
                .thenComparingInt((CodeNameVO city) -> getPriority(sortTwoCities, city.getName()))
                // 第三优先级：sortThree中的城市
                .thenComparingInt((CodeNameVO city) -> getPriority(sortThreeCities, city.getName()))
                // 第四优先级：sortFour中的城市
                .thenComparingInt((CodeNameVO city) -> getPriority(sortFourCities, city.getName()))
                // 最后按城市名称自然排序
                .thenComparing(CodeNameVO::getName);

        // 执行排序
        return cities.stream()
                .sorted(comparator)
                .collect(Collectors.toList());
    }

    /**
     * 分割城市名称字符串为列表
     * @param cityNames 用逗号分隔的城市名称字符串
     * @return 城市名称列表
     */
    private static List<String> splitCityNames(String cityNames) {
        if (cityNames == null || cityNames.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.stream(cityNames.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 获取城市在优先级列表中的位置
     * @param priorityList 优先级城市列表
     * @param cityName 城市名称
     * @return 如果在列表中找到返回索引，否则返回Integer.MAX_VALUE
     */
    private static int getPriority(List<String> priorityList, String cityName) {
        if (cityName == null) {
            return Integer.MAX_VALUE;
        }
        int index = priorityList.indexOf(cityName.trim());
        return index == -1 ? Integer.MAX_VALUE : index;
    }

    /**
     * 导出
     *
     * @param param 查询参数
     * @return 文件链接
     */
    public String export(CoreAreaParam param) {
        List<CoreAreaCityVO> list = this.list(param);
        if (CollUtil.isNotEmpty(list)) {
            try {
                // 获取核心区域列表子集
                List<CoreAreaVO> coreAreaList = list.stream()
                        .filter(city -> CollUtil.isNotEmpty(city.getCoreAreas()))
                        .flatMap(city -> city.getCoreAreas().stream())
                        .collect(Collectors.toList());

                return EasyExcelUtils.createExcelToCos("核心区", coreAreaList,CollUtil.newArrayList("cityName" , "name"),
                        String.format("核心区_%s.xlsx", LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN)),
                        "CoreArea");
            } catch (IOException e) {
                log.error("导出数据异常", e);
                throw new BusinessException("导出数据异常");
            }
        }
        return null;
    }


    public boolean judgeCoreArea(CoreAreaJudgeParam param) {
        // 1.根据城市名称获取城市id
        CodeNameVO cityInfo = RpcUtils.unBox(feignAuthorityRpc.getByCityName(param.getCityName()));
        if (Objects.isNull(cityInfo) || Objects.isNull(cityInfo.getId())) {
            throw new BusinessException("解析城市名称失败");
        }
        // 2.根据城市id获取该城市所有的核心区
        List<CoreAreaEntity> coreAreaList = coreAreaService.lambdaQuery()
                .eq(CoreAreaEntity::getCityId, cityInfo.getId())
                .eq(CoreAreaEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();
        if (CollUtil.isEmpty(coreAreaList)) {
            return false;
        }

        // 获取楼宇的点
        double[] point = new double[]{Double.parseDouble(param.getLongitude()), Double.parseDouble(param.getLatitude())};

        // 3.判断核心区域是否包含该经纬度
        for (CoreAreaEntity coreArea : coreAreaList) {
            String area = coreArea.getArea();
            if (StringUtils.isBlank(area)) {
                continue;
            }
            //4.解析区域
            String areaType = coreArea.getType();
            if (CoreAreaTypeEnum.CIRCLE.getCode().equals(areaType)) {
                // 处理圆形区域逻辑
                CoreAreaCircleVO circle = JSON.parseObject(area, CoreAreaCircleVO.class);
                boolean inCircleWithBuffer = CoreAreaUtils.isInCircleWithBuffer(point, circle, bufferWidth);
                if (inCircleWithBuffer) {
                    return true;
                }
            } else if (CoreAreaTypeEnum.RECTANGLE.getCode().equals(areaType) || CoreAreaTypeEnum.POLYGON.getCode().equals(areaType)) {
                // 处理多边形区域逻辑
                CoreAreaPolygonVO rectangle = JSON.parseObject(area, CoreAreaPolygonVO.class);
                boolean pointInPolygon = CoreAreaUtils.isPointInPolygon(point, rectangle, bufferWidth);
                if (pointInPolygon) {
                    // 如果有一个判断是核心区，直接返回true
                    return true;
                }
            } else {
                throw new BusinessException("不支持的核心区域类型: " + areaType);
            }
        }
        return false;
    }
}