package com.coocaa.meht.api.cheese.rpc;

import com.coocaa.meht.api.cheese.common.tools.bean.common.CodeNameVO;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.common.tools.utils.RpcUtils;
import com.coocaa.meht.api.cheese.rpc.bean.ChatMessageParam;
import com.coocaa.meht.api.cheese.rpc.bean.CityNameParam;
import com.google.common.collect.Lists;
import jakarta.validation.constraints.NotEmpty;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/16
 */
@Component
public class AuthorityRpcService {
    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    public List<CodeNameVO> getGbByCityId(Integer cityId) {
        return RpcUtils.unBox(feignAuthorityRpc.getGbByCityId(cityId));
    }

    public List<String> getGBCodeByCityId(Integer cityId, Integer districtId) {
        List<CodeNameVO> vos = new ArrayList<>();
        if (districtId != null) {
            vos = getGbByCityId(districtId);
        } else if (cityId != null) {
            vos = getGbByCityId(cityId);
        }

        return vos.stream().map(CodeNameVO::getCode).collect(Collectors.toList());
    }

    public List<CodeNameVO> listDictByCodes(List<String> codes) {
        if (CollectionUtils.isNotEmpty(codes)) {
            return RpcUtils.unBox(feignAuthorityRpc.listDictByCodes(codes));
        }
        return new ArrayList<>();
    }

    public CodeNameVO getDictByCode(String code) {
        List<CodeNameVO> vos = listDictByCodes(Lists.newArrayList(code));
        return vos.get(0);
    }

    /**
     * 根据城市名称查询城市
     */

    public CodeNameVO getByCityName(String name) {
        return RpcUtils.unBox(feignAuthorityRpc.getByCityName(name));
    }


    /**
     * 根据城市名称，区县名称查询区县
     */

    public CodeNameVO getCounty(String cityName, String countyName) {
        CityNameParam param = CityNameParam.builder().cityName(cityName)
                .countyName(countyName).build();
        return RpcUtils.unBox(feignAuthorityRpc.getCounty(param));
    }


    public List<CodeNameVO> listCityByIds(List<Integer> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            return RpcUtils.unBox(feignAuthorityRpc.listCityByIds(ids));
        }
        return new ArrayList<>();
    }

    public List<CodeNameVO> listWithGbCode(List<Integer> cityIds) {
        if (CollectionUtils.isNotEmpty(cityIds)) {
            return RpcUtils.unBox(feignAuthorityRpc.listWithGbCode(cityIds));
        }
        return new ArrayList<>();
    }

    public String sendChatMessage(ChatMessageParam param){
        return RpcUtils.unBox(feignAuthorityRpc.sendChatMessage(param));
    }

    public String sendDefaultChatMessage(String title,String content){
        ChatMessageParam param = new ChatMessageParam();
        param.setContent(content);
        param.setTitle(title);
        return sendChatMessage(param);
    }
}
