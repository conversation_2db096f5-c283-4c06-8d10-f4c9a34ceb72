package com.coocaa.meht.api.cheese.bean.tctask;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "创建试算任务请求参数")
public class CreateTCTaskParams {
    
    @NotBlank(message = "任务名称不能为空")
    @Size(max = 20, message = "任务名称长度不能超过20个字符")
    @Schema(description = "任务名称", required = true)
    private String taskName;
    
    @NotBlank(message = "导入表格不能为空")
    @Schema(description = "COS文件访问地址", required = true)
    private String cosUrl;
} 