package com.coocaa.meht.api.cheese.bean.building;

import com.coocaa.meht.api.cheese.utils.converter.Convert;
import com.coocaa.meht.api.cheese.utils.converter.ConvertType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
public class BuildingMetaPointDetailVO {
    // private String projectName;
    private String buildingRatingNo;
    private String buildingName;
    private String unitName;
    @Convert(type = ConvertType.DICT)
    private String floor;
    @Schema(description = "物理楼层")
    private String floorName;
    private String waitingHallName;
    @Convert(type = ConvertType.DICT)
    private String waitingHallType;
    private String waitingHallTypeName;
    private Integer waitingHallId;
    private String pointCode;

    private String pointRemark;
    @Convert(type = ConvertType.DICT)
    private String deviceSize;
    private String deviceSizeName;
    private Integer pointId;
    private List<String> pointPics = new ArrayList<>();
    private LocalDateTime pointCreateTime;
    @Schema(description = "楼层名称")
    private String floorDesc;

    @Convert(type = ConvertType.DICT)
    private String pointStatus;
    @Schema(description = "点位状态")
    private String pointStatusName;
}
