package com.coocaa.meht.api.cheese.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.api.cheese.bean.price.ApplyDeviceVO;
import com.coocaa.meht.api.cheese.bean.price.CommentEntityVO;
import com.coocaa.meht.api.cheese.bean.price.PriceApplyDevicePointVO;
import com.coocaa.meht.api.cheese.bean.price.PriceApplyDeviceVO;
import com.coocaa.meht.api.cheese.bean.price.PriceApplyQueryParam;
import com.coocaa.meht.api.cheese.bean.price.PriceApplyUpdateStatusParam;
import com.coocaa.meht.api.cheese.bean.price.PriceApplyVO;
import com.coocaa.meht.api.cheese.bean.user.PermissionDTO;
import com.coocaa.meht.api.cheese.common.db.bean.PriceApplyQueryDTO;
import com.coocaa.meht.api.cheese.common.db.dto.ApplyPointDTO;
import com.coocaa.meht.api.cheese.common.db.dto.PriceApplyDTO;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingGeneEntity;
import com.coocaa.meht.api.cheese.common.db.entity.CommentEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PriceApplyDeviceEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PriceApplyDevicePointEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PriceApplyEntity;
import com.coocaa.meht.api.cheese.common.db.entity.ScreenApproveRecordEntity;
import com.coocaa.meht.api.cheese.common.db.entity.SysFileEntity;
import com.coocaa.meht.api.cheese.common.db.service.IBuildingGeneService;
import com.coocaa.meht.api.cheese.common.db.service.IBuildingRatingService;
import com.coocaa.meht.api.cheese.common.db.service.ICommentService;
import com.coocaa.meht.api.cheese.common.db.service.IPriceApplyDevicePointService;
import com.coocaa.meht.api.cheese.common.db.service.IPriceApplyDeviceService;
import com.coocaa.meht.api.cheese.common.db.service.IPriceApplyService;
import com.coocaa.meht.api.cheese.common.db.service.IScreenApproveRecordService;
import com.coocaa.meht.api.cheese.common.db.service.ISysFileService;
import com.coocaa.meht.api.cheese.common.tools.bean.common.CodeNameVO;
import com.coocaa.meht.api.cheese.common.tools.easyexcel.EasyExcelUtils;
import com.coocaa.meht.api.cheese.common.tools.enums.BooleFlagEnum;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.common.tools.utils.StringUtils;
import com.coocaa.meht.api.cheese.convert.PriceApplyConvert;
import com.coocaa.meht.api.cheese.convert.SysFileConvert;
import com.coocaa.meht.api.cheese.enums.BuildingTypeEnum;
import com.coocaa.meht.api.cheese.enums.DeviceGroupEnum;
import com.coocaa.meht.api.cheese.enums.PriceApplyStatusEnum;
import com.coocaa.meht.api.cheese.enums.ProjectLevelEnum;
import com.coocaa.meht.api.cheese.enums.SceneServiceTypeEnum;
import com.coocaa.meht.api.cheese.exception.BusinessException;
import com.coocaa.meht.api.cheese.handler.PermissionHandler;
import com.coocaa.meht.api.cheese.rpc.FeignAuthorityRpc;
import com.coocaa.meht.api.cheese.rpc.FeignCmsRpc;
import com.coocaa.meht.api.cheese.rpc.FeignMehtRpc;
import com.coocaa.meht.api.cheese.rpc.bean.ScreenApproveRecordDTO;
import com.coocaa.meht.api.cheese.rpc.meht.Result;
import com.coocaa.meht.api.cheese.utils.CodeNameHelper;
import com.coocaa.meht.api.cheese.utils.RsaExample;
import com.coocaa.meht.api.cheese.vo.UserVO;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.rmi.ServerException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class PriceApplyApiService {
    private static final int SCREEN_FLAG_SMALL = 1, SCREEN_FLAG_LARGE = 2, SCREEN_FLAG_BOTH = 3;

    /**
     * 处理价格申请的线程池
     */
    private static final ExecutorService PRICE_APPLY_THREAD_POOL = new ThreadPoolExecutor(
            5,
            10,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(500),
            new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "price-apply-processor-" + threadNumber.getAndIncrement());
                    t.setDaemon(true);
                    return t;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    // 分批处理的批次大小
    private static final int BATCH_SIZE = 100;

    @Autowired
    private CodeNameHelper codeNameHelper;

    @Autowired
    private PermissionHandler permissionHandler;

    @Autowired
    private PriceApplyConvert priceApplyConvert;

    @Autowired
    private IPriceApplyService priceApplyService;

    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    @Autowired
    private IBuildingGeneService iBuildingGeneService;

    @Autowired
    private IPriceApplyDeviceService iPriceApplyDeviceService;

    @Autowired
    private IPriceApplyDevicePointService iPriceApplyDevicePointService;

    @Autowired
    private ISysFileService fileService;

    @Autowired
    private IScreenApproveRecordService screenApproveRecordService;

    @Autowired
    private IBuildingRatingService buildingRatingService;

    @Autowired
    private ICommentService commentService;

    @Autowired
    private RsaExample rsaExample;

    @Autowired
    private FeignCmsRpc feignCmsRpc;

    @Autowired
    private FeignMehtRpc feignMehtRpc;


    public PriceApplyVO getPrice(Integer id) throws ServerException {
        PriceApplyEntity priceApply = priceApplyService.getById(id);

        if (Objects.isNull(priceApply)) {
            throw new ServerException("未找到价格申请");
        }
        PriceApplyVO priceApplyVo = PriceApplyConvert.INSTANCE.toPriceApplyVo(priceApply);
        // 手动处理格式问题
        priceApplyVo.setBuildingType(BuildingTypeEnum.getDescByValue(priceApply.getBuildingType()));
        priceApplyVo.setFinalCoefficient(String.valueOf(priceApply.getFinalCoefficient()));

        priceApplyVo.setStatusName(PriceApplyEntity.Status.getDesc(priceApplyVo.getStatus()));

        if (StringUtils.isNotBlank(priceApplyVo.getCreateBy())) {
            priceApplyVo.setApplicantType(priceApplyVo.getCreateBy().startsWith("CC") ? "内部" : "代理");
        }

        List<PriceApplyDeviceEntity> priceApplyDeviceEntities = iPriceApplyDeviceService.lambdaQuery()
                .eq(PriceApplyDeviceEntity::getApplyId, id)
                .list();
        if (CollUtil.isNotEmpty(priceApplyDeviceEntities)) {
            List<ApplyDeviceVO> applyDeviceVos = PriceApplyConvert.INSTANCE.toApplyDeviceVo(priceApplyDeviceEntities);

            List<Integer> deviceIds = applyDeviceVos.stream().map(ApplyDeviceVO::getId).toList();
            List<PriceApplyDevicePointEntity> priceApplyDevicePointEntities = iPriceApplyDevicePointService.lambdaQuery()
                    .in(PriceApplyDevicePointEntity::getPriceApplyDeviceId, deviceIds)
                    .list();
            List<PriceApplyDevicePointVO> priceApplyDevicePointVos = PriceApplyConvert.INSTANCE.toPriceApplyDevicePointVo(priceApplyDevicePointEntities);
            Map<Integer, List<PriceApplyDevicePointVO>> priceApplyDevicePointMap = priceApplyDevicePointVos.stream().collect(Collectors.groupingBy(PriceApplyDevicePointVO::getPriceApplyDeviceId));
            for (ApplyDeviceVO applyDeviceVo : applyDeviceVos) {
                List<PriceApplyDevicePointVO> priceApplyDevicePointVOS = priceApplyDevicePointMap.get(applyDeviceVo.getId());
                applyDeviceVo.setPriceApplyDevicePointVOList(priceApplyDevicePointVOS);

                // 水位价从价格申请上获取 提交的时候保存过
                applyDeviceVo.setWaterMarkPriceForSmall(String.valueOf(priceApplyVo.getSmallWaterMarkPrice()));
                applyDeviceVo.setWaterMarkPriceForBig(String.valueOf(priceApplyVo.getBigWaterMarkPrice()));
            }
            priceApplyVo.setApplyDevcieVOList(applyDeviceVos);
        }

        if (StrUtil.isNotBlank(priceApplyVo.getFileIds())) {
            //将priceApplyVo.getFileIds()转成Integer类型list
            if (StrUtil.isNotBlank(priceApplyVo.getFileIds())) {
                List<Integer> fileIds = Stream.of(priceApplyVo.getFileIds().split(",")).map(Integer::parseInt).toList();
                List<SysFileEntity> sysFileEntities = fileService.lambdaQuery()
                        .in(SysFileEntity::getId, fileIds)
                        .list();
                priceApplyVo.setFileUrls(SysFileConvert.INSTANCE.toFilesDetailDto(sysFileEntities));
            }

        }

        // 查询审批记录 并返回
        Result<List<ScreenApproveRecordDTO>> approveResult = feignMehtRpc.queryLocalNodes(priceApplyVo.getApplyCode());
        priceApplyVo.setApproveRecords(null != approveResult ? approveResult.getData() : null);


        //负责人
        Map<String, UserVO> userMap = feignAuthorityRpc.getUserByWnos(List.of(priceApplyVo.getCreateBy())).getData()
                .stream().collect(Collectors.toMap(UserVO::getWno, e -> e, (k1, k2) -> k1));
        priceApplyVo.setSubmitUserName(Objects.nonNull(userMap.get(priceApplyVo.getCreateBy()))
                ? userMap.get(priceApplyVo.getCreateBy()).getName() : "");


        //评论
        List<CommentEntity> comments = commentService.lambdaQuery()
                .eq(CommentEntity::getBusinessId, priceApply.getId())
                .eq(CommentEntity::getBusinessType, 2)
                .eq(CommentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();

        if (CollUtil.isNotEmpty(comments)) {
            List<CommentEntityVO> commentEntityVOS = convertToDTOList(comments);
            priceApplyVo.setCommentEntityVOList(commentEntityVOS);
        }

        return priceApplyVo;
    }


    private List<CommentEntityVO> convertToDTOList(List<CommentEntity> comments) {
        // 空值处理
        if (CollUtil.isEmpty(comments)) {
            return Collections.emptyList();
        }

        try {
            // 1. 初始化基本属性
            List<CommentEntityVO> dtoList = new ArrayList<>(comments.size());
            // 使用Set去重
            Set<String> allUserIds = new HashSet<>();
            List<Long> allFileIds = new ArrayList<>();

            // 2. 收集所有用户ID和文件ID
            for (CommentEntity comment : comments) {
                CommentEntityVO dto = new CommentEntityVO();
                BeanUtils.copyProperties(comment, dto);
                dto.setNotifiedUsers(new ArrayList<>());
                dto.setAttachments(new ArrayList<>());
                dtoList.add(dto);

                if (StringUtils.isNotBlank(comment.getCreateBy())) {
                    allUserIds.add(comment.getCreateBy());
                }

                if (StringUtils.isNotBlank(comment.getNotifiedUsers())) {
                    try {
                        List<String> notifiedUsers = JSON.parseArray(comment.getNotifiedUsers(), String.class);
                        if (CollUtil.isNotEmpty(notifiedUsers)) {
                            allUserIds.addAll(notifiedUsers);
                        }
                    } catch (Exception e) {
                        log.warn("解析通知用户列表异常: {}", e.getMessage());
                    }
                }

                if (StringUtils.isNotBlank(comment.getAttachmentIds())) {
                    try {
                        List<Long> fileIds = JSON.parseArray(comment.getAttachmentIds(), Long.class);
                        if (CollUtil.isNotEmpty(fileIds)) {
                            allFileIds.addAll(fileIds);
                        }
                    } catch (Exception e) {
                        log.warn("解析附件ID列表异常: {}", e.getMessage());
                    }
                }
            }

            // 3. 批量查询用户信息
            Map<String, UserVO> userNameMaps = new HashMap<>();
            if (CollUtil.isNotEmpty(allUserIds)) {
                try {
                    userNameMaps = feignAuthorityRpc.getUserByWnos(new ArrayList<>(allUserIds)).getData()
                            .stream().collect(Collectors.toMap(UserVO::getWno, e -> e, (k1, k2) -> k1));
                } catch (Exception e) {
                    log.warn("批量查询用户信息异常: {}", e.getMessage());
                }
            }

            // 4. 批量查询附件信息
            Map<Long, SysFileEntity> fileMap = new HashMap<>();
            if (CollUtil.isNotEmpty(allFileIds)) {
                try {
                    List<SysFileEntity> fileList = fileService.lambdaQuery()
                            .in(SysFileEntity::getId, allFileIds)
                            .list();
                    if (CollUtil.isNotEmpty(fileList)) {
                        fileMap = fileList.stream()
                                .filter(Objects::nonNull)
                                .collect(Collectors.toMap(
                                        SysFileEntity::getId,
                                        Function.identity(),
                                        (a, b) -> a
                                ));
                    }
                } catch (Exception e) {
                    log.warn("批量查询附件信息异常: {}", e.getMessage());
                }
            }

            // 5. 填充DTO信息
            for (int i = 0; i < comments.size(); i++) {
                CommentEntity comment = comments.get(i);
                CommentEntityVO dto = dtoList.get(i);

                // 处理通知用户列表
                if (org.apache.commons.lang3.StringUtils.isNotBlank(comment.getNotifiedUsers())) {
                    try {
                        List<String> notifiedUsers = JSON.parseArray(comment.getNotifiedUsers(), String.class);
                        if (CollUtil.isNotEmpty(notifiedUsers)) {
                            Map<String, UserVO> finalUserNameMaps = userNameMaps;
                            List<String> nameList = notifiedUsers.stream()
                                    .map(userId -> {
                                        UserVO userDto = finalUserNameMaps.get(userId);
                                        String userName = userDto != null ? userDto.getName() : "";
                                        return userId + (StrUtil.isNotBlank(userName) ? "(" + userName + ")" : "");
                                    })
                                    .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                                    .toList();
                            dto.setNotifiedUsers(nameList);
                        }
                    } catch (Exception e) {
                        log.warn("处理通知用户列表异常: {}", e.getMessage());
                    }
                }

                // 处理附件列表
                if (org.apache.commons.lang3.StringUtils.isNotBlank(comment.getAttachmentIds())) {
                    try {
                        List<Long> fileIds = JSON.parseArray(comment.getAttachmentIds(), Long.class);
                        if (CollUtil.isNotEmpty(fileIds)) {
                            List<SysFileEntity> fileList = fileIds.stream()
                                    .map(fileMap::get)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());
                            dto.setAttachments(fileList);
                        }
                    } catch (Exception e) {
                        log.warn("处理附件列表异常: {}", e.getMessage());
                    }
                }

                // 获取创建人姓名
                try {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(comment.getCreateBy())) {
                        UserVO user = userNameMaps.get(comment.getCreateBy());
                        dto.setCreateByName(user != null ? user.getName() : comment.getCreateBy());
                    } else {
                        dto.setCreateByName("");
                    }
                } catch (Exception e) {
                    log.warn("获取用户名称失败: {}", e.getMessage());
                    dto.setCreateByName(comment.getCreateBy());
                }
            }

            return dtoList;
        } catch (Exception e) {
            log.error("转换评论列表异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public PageResponseVo<PriceApplyVO> listPage(PageRequestVo<PriceApplyQueryParam> param) {
        log.info("价格申请参数listPage param:{}", param);

        PriceApplyQueryDTO priceApplyQueryDTO = processParameters(param.getQuery());

        IPage<Object> page = new Page<>(
                Optional.ofNullable(param.getCurrentPage()).orElse(1L),
                Optional.ofNullable(param.getPageSize()).orElse(10));

        IPage<PriceApplyDTO> pageResult = priceApplyService.listPage(page, priceApplyQueryDTO);

        PageResponseVo<PriceApplyVO> pageResponse = new PageResponseVo<>();
        pageResponse.setCurrentPage(pageResult.getCurrent());
        pageResponse.setTotalRows(pageResult.getTotal());
        pageResponse.setTotal(pageResult.getTotal());
        pageResponse.setPageSize(pageResult.getSize());
        pageResponse.setTotalPages(pageResult.getPages());
        List<PriceApplyDTO> records = pageResult.getRecords();
        List<PriceApplyVO> priceApplyVOS = processPriceApply(records, DeviceGroupEnum.APPLY.getCode());
        pageResponse.setRows(priceApplyVOS);

        return pageResponse;
    }

    public String exportList(PageRequestVo<PriceApplyQueryParam> param) {
        log.info("价格申请导表参数listPage param:{}", param);

        PriceApplyQueryDTO priceApplyQueryDTO = processParameters(param.getQuery());

        param.setCurrentPage(1L);
        param.setPageSize(-1);

        IPage page = new Page<>(
                param.getCurrentPage(),
                param.getPageSize());

        IPage<PriceApplyDTO> pageResult = priceApplyService.listPage(page, priceApplyQueryDTO);

        List<PriceApplyDTO> records = pageResult.getRecords();
        List<PriceApplyVO> priceApplyVOS = processPriceApply(records, DeviceGroupEnum.APPLY.getCode());

        if (CollUtil.isEmpty(priceApplyVOS)) {
            throw new BusinessException("没有查询到数据");
        }

        try {
            return EasyExcelUtils.createExcelToCos("价格申请", priceApplyVOS,
                    String.format("价格申请-%s.xlsx", LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN)),
                    "priceApply");
        } catch (IOException e) {
            log.error("导出数据异常", e);
            throw new BusinessException("导出数据异常");
        }
    }

    public String exportDeviceList(PageRequestVo<PriceApplyQueryParam> param) {
        log.info("价格申请导表device参数listPage param:{}", param);

        PriceApplyQueryDTO priceApplyQueryDTO = processParameters(param.getQuery());

        List<PriceApplyDTO> priceApplyList = priceApplyService.getPriceApplyList(priceApplyQueryDTO);

        List<PriceApplyVO> priceApplyVOS = processPriceApply(priceApplyList, DeviceGroupEnum.DEVICE_GROUP.getCode());

        List<PriceApplyDeviceVO> priceApplyDeviceVo = PriceApplyConvert.INSTANCE.toPriceApplyDeviceVo(priceApplyVOS);

        priceApplyDeviceVo.forEach(p -> {
            if (Objects.nonNull(p.getSignPrice()) && p.getQuantity() != null) {
                p.setSignTotalPrice(p.getSignPrice().multiply(new BigDecimal(p.getQuantity())));
            }
        });

        //水位价
        assembleWaterMarkPrice(priceApplyDeviceVo);

        if (CollUtil.isEmpty(priceApplyDeviceVo)) {
            throw new BusinessException("没有查询到数据");
        }

        try {
            return EasyExcelUtils.createExcelToCos("价格申请分组", priceApplyDeviceVo,
                    String.format("价格申请分组-%s.xlsx", LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN)),
                    "priceApply");
        } catch (IOException e) {
            log.error("导出数据异常", e);
            throw new BusinessException("导出数据异常");
        }
    }


    private void assembleWaterMarkPrice(List<PriceApplyDeviceVO> priceApplyDeviceVos) {
        if (CollUtil.isEmpty(priceApplyDeviceVos)) {
            return;
        }
        for (PriceApplyDeviceVO priceApplyDeviceVo : priceApplyDeviceVos) {
            if (null != priceApplyDeviceVo.getSmallWaterMarkPrice() && BigDecimal.ZERO.compareTo(priceApplyDeviceVo.getSmallWaterMarkPrice()) != 0) {
                priceApplyDeviceVo.setWaterMarkPriceForSmall(String.valueOf(priceApplyDeviceVo.getSmallWaterMarkPrice()));
            }
            if (null != priceApplyDeviceVo.getBigWaterMarkPrice() && BigDecimal.ZERO.compareTo(priceApplyDeviceVo.getBigWaterMarkPrice()) != 0) {
                priceApplyDeviceVo.setWaterMarkPriceForBig(String.valueOf(priceApplyDeviceVo.getBigWaterMarkPrice()));
            }
        }
    }



    public List<PriceApplyVO> processPriceApply(List<PriceApplyDTO> records, Integer deviceFlag) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理价格申请数据，总记录数: {}", records == null ? 0 : records.size());

        // 如果记录为空，快速返回
        if (CollUtil.isEmpty(records)) {
            return new ArrayList<>();
        }

        // 转换DTO到VO
        long convertStart = System.currentTimeMillis();
        List<PriceApplyVO> priceApplyVos = priceApplyConvert.toPriceApplyVos(records);
        long convertEnd = System.currentTimeMillis();
        log.info("转换DTO到VO完成，耗时: {} ms", (convertEnd - convertStart));

        // 1. 提前收集所有需要的ID，减少重复遍历 - 预设集合大小提高效率
        long collectStart = System.currentTimeMillis();
        int size = priceApplyVos.size();
        List<Integer> ids = new ArrayList<>(size);
        Set<String> buildingNoSet = new HashSet<>(size / 2);
        Set<String> userCodes = new HashSet<>(size);

        // 单次遍历收集所有ID，减少多次流处理开销
        for (PriceApplyVO vo : priceApplyVos) {
            Integer id = vo.getId();
            if (id != null) {
                ids.add(id);
            }

            String buildingNo = vo.getBuildingNo();
            if (StringUtils.isNotBlank(buildingNo)) {
                buildingNoSet.add(buildingNo);
            }

            String createBy = vo.getCreateBy();
            if (StringUtils.isNotBlank(createBy)) {
                userCodes.add(createBy);
            }

            String approveBy = vo.getApproveBy();
            if (StringUtils.isNotBlank(approveBy)) {
                userCodes.add(approveBy);
            }
        }

        List<String> buildingNos = new ArrayList<>(buildingNoSet);

        long collectEnd = System.currentTimeMillis();
        log.info("收集ID完成，耗时: {} ms, 申请ID数量: {}, 楼宇编号数量: {}, 用户编码数量: {}",
                (collectEnd - collectStart), ids.size(), buildingNos.size(), userCodes.size());

        // 3. 同时发起所有远程调用和数据库查询
        // 用户信息查询 - 分批处理
        CompletableFuture<Map<String, String>> userMapFuture = CompletableFuture.supplyAsync(() -> {
            if (CollUtil.isEmpty(userCodes)) {
                return new HashMap<>(0);
            }
            Map<String, String> userMap = new HashMap<>(userCodes.size());

            // 将用户编码分批处理
            List<List<String>> batches = partition(new ArrayList<>(userCodes), BATCH_SIZE);
            log.info("用户信息查询分为 {} 批处理", batches.size());

            for (List<String> batch : batches) {
                long batchStart = System.currentTimeMillis();
                ResultTemplate<List<UserVO>> userByWnos = feignAuthorityRpc.getUserByWnos(batch);
                long batchEnd = System.currentTimeMillis();

                if (batchEnd - batchStart > 100) {  // 只记录慢查询
                    log.info("用户批次查询耗时: {} ms, 批次大小: {}, 返回结果数: {}",
                            (batchEnd - batchStart),
                            batch.size(),
                            userByWnos != null && userByWnos.getData() != null ? userByWnos.getData().size() : 0);
                }

                if (userByWnos != null && userByWnos.getData() != null) {
                    List<UserVO> userData = userByWnos.getData();
                    // 直接遍历填充map，避免流处理开销
                    for (UserVO user : userData) {
                        if (user != null && StringUtils.isNotBlank(user.getWno())) {
                            userMap.put(user.getWno(), user.getName());
                        }
                    }
                }
            }

            return userMap;
        }, PRICE_APPLY_THREAD_POOL);

        // 楼宇基因查询 - 分批处理
        CompletableFuture<Map<String, BuildingGeneEntity>> buildingMapFuture = CompletableFuture.supplyAsync(() -> {
            if (CollUtil.isEmpty(buildingNos)) {
                return new HashMap<>(0);
            }

            Map<String, BuildingGeneEntity> buildingMap = new HashMap<>(buildingNos.size());

            // 将楼宇编号分批处理
            List<List<String>> batches = partition(buildingNos, BATCH_SIZE);
            log.info("楼宇基因查询分为 {} 批处理", batches.size());

            for (List<String> batch : batches) {
                long batchStart = System.currentTimeMillis();
                List<BuildingGeneEntity> buildingGeneEntities = iBuildingGeneService.lambdaQuery()
                        .select(BuildingGeneEntity::getBuildingRatingNo, BuildingGeneEntity::getFinalCoefficient, BuildingGeneEntity::getSpec)
                        .in(BuildingGeneEntity::getBuildingRatingNo, batch)
                        .last("LIMIT " + batch.size() * 2) // 增加LIMIT限制防止返回过多数据
                        .list();
                long batchEnd = System.currentTimeMillis();

                if (batchEnd - batchStart > 100) {  // 只记录慢查询
                    log.info("楼宇基因批次查询耗时: {} ms, 批次大小: {}, 返回结果数: {}",
                            (batchEnd - batchStart), batch.size(), buildingGeneEntities.size());
                }

                // 直接遍历填充map，避免流处理开销
                for (BuildingGeneEntity entity : buildingGeneEntities) {
                    if (entity != null && StringUtils.isNotBlank(entity.getBuildingRatingNo())) {
                        buildingMap.put(entity.getBuildingRatingNo(), entity);
                    }
                }
            }

            return buildingMap;
        }, PRICE_APPLY_THREAD_POOL);

        // 字典查询
        CompletableFuture<Map<String, CodeNameVO>> dictMapFuture = CompletableFuture.supplyAsync(() -> {
            long rpcStart = System.currentTimeMillis();
            ResultTemplate<List<CodeNameVO>> listResultTemplate = feignAuthorityRpc.listDictByParent("0013");
            long rpcEnd = System.currentTimeMillis();
            log.info("字典RPC调用耗时: {} ms", (rpcEnd - rpcStart));

            if (listResultTemplate == null || listResultTemplate.getData() == null) {
                return new HashMap<>(0);
            }

            List<CodeNameVO> dictData = listResultTemplate.getData();
            log.info("字典数据数量: {}", dictData.size());

            // 预设合适的Map大小
            Map<String, CodeNameVO> dictMap = new HashMap<>((int) (dictData.size() / 0.75) + 1);
            // 直接遍历填充map，避免流处理开销
            for (CodeNameVO vo : dictData) {
                if (vo != null && StringUtils.isNotBlank(vo.getCode())) {
                    dictMap.put(vo.getCode(), vo);
                }
            }
            return dictMap;
        }, PRICE_APPLY_THREAD_POOL);

        // 设备查询 - 分批处理
        CompletableFuture<Map<Integer, List<PriceApplyDeviceEntity>>> deviceMapFuture = CompletableFuture.supplyAsync(() -> {
            if (CollUtil.isEmpty(ids)) {
                return new HashMap<>(0);
            }

            Map<Integer, List<PriceApplyDeviceEntity>> deviceMap = new HashMap<>(ids.size());

            // 将申请ID分批处理
            List<List<Integer>> batches = partition(ids, BATCH_SIZE);
            log.info("设备查询分为 {} 批处理", batches.size());

            for (List<Integer> batch : batches) {
                long batchStart = System.currentTimeMillis();
                List<PriceApplyDeviceEntity> devices = iPriceApplyDeviceService.lambdaQuery()
                        .in(PriceApplyDeviceEntity::getApplyId, batch)
                        .list();
                long batchEnd = System.currentTimeMillis();

                if (batchEnd - batchStart > 100) {  // 只记录慢查询
                    log.info("设备批次查询耗时: {} ms, 批次大小: {}, 返回结果数: {}",
                            (batchEnd - batchStart), batch.size(), devices.size());
                }

                // 手动分组，避免流处理开销
                for (PriceApplyDeviceEntity device : devices) {
                    if (device != null && device.getApplyId() != null) {
                        deviceMap.computeIfAbsent(device.getApplyId(), k -> new ArrayList<>()).add(device);
                    }
                }
            }

            return deviceMap;
        }, PRICE_APPLY_THREAD_POOL);

        // 点位查询 - 分批处理
        CompletableFuture<List<ApplyPointDTO>> pointListFuture = CompletableFuture.supplyAsync(() -> {
            if (CollUtil.isEmpty(ids)) {
                return new ArrayList<>(0);
            }

            // 预估点位数量，通常每个申请有多个点位
            List<ApplyPointDTO> allPoints = new ArrayList<>(ids.size() * 5);

            // 将申请ID分批处理
            List<List<Integer>> batches = partition(ids, BATCH_SIZE);
            log.info("点位查询分为 {} 批处理", batches.size());

            for (List<Integer> batch : batches) {
                long batchStart = System.currentTimeMillis();
                List<ApplyPointDTO> points = priceApplyService.getApplyPoint(batch);
                long batchEnd = System.currentTimeMillis();

                if (batchEnd - batchStart > 100) {  // 只记录慢查询
                    log.info("点位批次查询耗时: {} ms, 批次大小: {}, 返回结果数: {}",
                            (batchEnd - batchStart), batch.size(), points.size());
                }

                if (CollUtil.isNotEmpty(points)) {
                    allPoints.addAll(points);
                }
            }

            return allPoints;
        }, PRICE_APPLY_THREAD_POOL);

        try {
            // 4. 等待所有异步任务完成
            long waitStart = System.currentTimeMillis();
            Map<String, String> userMap = userMapFuture.get();
            long userEnd = System.currentTimeMillis();
            log.info("用户信息查询实际耗时: {} ms, 用户信息数量: {}", (userEnd - waitStart), userMap.size());

            Map<String, CodeNameVO> specMap = dictMapFuture.get();
            long dictEnd = System.currentTimeMillis();
            log.info("字典查询实际耗时: {} ms, 字典数量: {}", (dictEnd - userEnd), specMap.size());

            Map<Integer, List<PriceApplyDeviceEntity>> applyDeviceMap = deviceMapFuture.get();
            long deviceEnd = System.currentTimeMillis();
            log.info("设备查询实际耗时: {} ms, 设备组数量: {}", (deviceEnd - dictEnd), applyDeviceMap.size());

            List<ApplyPointDTO> allPoints = pointListFuture.get();
            long pointEnd = System.currentTimeMillis();
            log.info("点位查询实际耗时: {} ms, 点位数量: {}", (pointEnd - deviceEnd), allPoints.size());

            // 5. 预处理点位数据
            long processStart = System.currentTimeMillis();

            // 预设合适的Map大小
            Map<Integer, List<ApplyPointDTO>> pointMap = new HashMap<>((int) (allPoints.size() / 0.75) + 1);
            Map<Integer, List<ApplyPointDTO>> pointDeviceMap = new HashMap<>((int) (allPoints.size() / 0.75) + 1);

            // 单次遍历同时构建两个Map，避免两次流处理
            for (ApplyPointDTO point : allPoints) {
                if (point != null) {
                    // 申请ID分组
                    Integer applyId = point.getApplyId();
                    if (applyId != null) {
                        pointMap.computeIfAbsent(applyId, k -> new ArrayList<>()).add(point);
                    }

                    // 设备ID分组
                    Integer deviceId = point.getDeviceId();
                    if (deviceId != null) {
                        pointDeviceMap.computeIfAbsent(deviceId, k -> new ArrayList<>()).add(point);
                    }
                }
            }

            long processEnd = System.currentTimeMillis();
            log.info("点位数据分组处理耗时: {} ms, 申请点位组: {}, 设备点位组: {}",
                    (processEnd - processStart), pointMap.size(), pointDeviceMap.size());

            // 6. 批量处理VO对象
            long fillStart = System.currentTimeMillis();

            // 对每个VO对象填充数据
            for (PriceApplyVO item : priceApplyVos) {
                // 状态判断
                if (!PriceApplyStatusEnum.PASSED.getCode().equals(item.getStatus())) {
                    item.setApproveTime(null);
                }

                // 设置用户名
                String createBy = item.getCreateBy();
                String approveBy = item.getApproveBy();

                // 设置创建人和审批人
                if (StringUtils.isNotBlank(createBy)) {
                    String name = userMap.get(createBy);
                    item.setSubmitUserName(Objects.isNull(name) ? null : name + "(" + createBy + ")");
                }

                if (StringUtils.isNotBlank(approveBy)) {
                    String name = userMap.get(approveBy);
                    item.setApproveName(Objects.isNull(name) ? null : name + "(" + approveBy + ")");
                }

                // 设置数量
                Integer id = item.getId();
                if (DeviceGroupEnum.APPLY.getCode().equals(deviceFlag) && id != null) {
                    List<PriceApplyDeviceEntity> devices = applyDeviceMap.get(id);
                    if (CollUtil.isNotEmpty(devices)) {
                        int sum = 0;
                        for (PriceApplyDeviceEntity device : devices) {
                            if (device != null && device.getQuantity() != null) {
                                sum += device.getQuantity() != null ? device.getQuantity() : 0;
                            }
                        }
                        item.setQuantity(sum);
                    }
                }

                // 解密地址
                if (StringUtils.isNotBlank(item.getMapAddress())) {
                    try {
                        item.setMapAddress(rsaExample.decryptByPrivate(item.getMapAddress()));
                    } catch (Exception e) {
                        log.error("解密地址失败: {}", item.getMapAddress(), e);
                    }
                }

                // 设置规格信息 - 使用优化后的方法
                processSpecInfo(item, deviceFlag, pointMap, pointDeviceMap, specMap);
            }
            long fillEnd = System.currentTimeMillis();
            log.info("数据填充处理耗时: {} ms", (fillEnd - fillStart));

        } catch (Exception e) {
            log.error("处理价格申请数据异常", e);
        }
        long endTime = System.currentTimeMillis();
        log.info("价格申请数据处理完成，总耗时: {} ms", (endTime - startTime));

        return priceApplyVos;
    }

    /**
     * 将列表分割为指定大小的批次
     */
    private <T> List<List<T>> partition(List<T> list, int size) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<List<T>> result = new ArrayList<>();
        int total = list.size();
        for (int i = 0; i < total; i += size) {
            result.add(list.subList(i, Math.min(i + size, total)));
        }
        return result;
    }

    /**
     * 处理规格信息
     */
    private void processSpecInfo(PriceApplyVO item, Integer deviceFlag,
                                 Map<Integer, List<ApplyPointDTO>> pointMap,
                                 Map<Integer, List<ApplyPointDTO>> pointDeviceMap,
                                 Map<String, CodeNameVO> specMap) {
        long start = System.currentTimeMillis();
        List<ApplyPointDTO> applyPointDTOS;

        if (deviceFlag.equals(DeviceGroupEnum.APPLY.getCode())) {
            applyPointDTOS = pointMap.get(item.getId());
        } else {
            applyPointDTOS = pointDeviceMap.get(item.getDeviceId());
        }

        if (CollUtil.isEmpty(applyPointDTOS)) {
            item.setSpec("");
            long end = System.currentTimeMillis();
            if (end - start > 10) { // 只记录执行超过10毫秒的，避免日志过多
                log.debug("处理规格信息(空)耗时: {} ms, 申请ID: {}", (end - start), item.getId());
            }
            return;
        }

        // 使用更高效的方式构建规格字符串
        Map<String, Integer> deviceSizeMap = new HashMap<>(8);
        for (ApplyPointDTO dto : applyPointDTOS) {
            String deviceSize = dto.getDeviceSize();
            deviceSizeMap.put(deviceSize, deviceSizeMap.getOrDefault(deviceSize, 0) + 1);
        }

        StringBuilder codeName = new StringBuilder(64);
        for (Map.Entry<String, Integer> entry : deviceSizeMap.entrySet()) {
            String key = entry.getKey();
            Integer count = entry.getValue();
            CodeNameVO codeNameVO = specMap.get(key);
            if (Objects.nonNull(codeNameVO)) {
                codeName.append(codeNameVO.getName()).append(":").append(count).append(";");
            }
        }

        item.setSpec(codeName.isEmpty() ? "" : codeName.toString());
        long end = System.currentTimeMillis();
        if (end - start > 10) {
            log.debug("处理规格信息耗时: {} ms, 申请ID: {}, 点位数: {}, 设备大小类型数: {}",
                    (end - start), item.getId(), applyPointDTOS.size(), deviceSizeMap.size());
        }
    }

    private PriceApplyQueryDTO processParameters(PriceApplyQueryParam param) {
        // 访问权限
        Map<Integer, String> cityMapping = codeNameHelper.getCityMapping(param.getCities());
        PermissionDTO cmsPermission = permissionHandler.getCmsPermission(param.getSubmitUsers(), cityMapping.values());
        if (Objects.isNull(cmsPermission.getCities()) || Objects.isNull(cmsPermission.getUserCodes())) {
            // 没有符合权限的条件，直接返回空数据
            throw new BusinessException("没有符合条件的数据");
        }

        if (StrUtil.isNotBlank(param.getBuildingType())) {
            param.setBuildingType(Objects.requireNonNull(BuildingTypeEnum.getValueByCode(param.getBuildingType())).toString());
        }

        if (StringUtils.isNotBlank(param.getProjectLevel())) {
            param.setProjectLevel(ProjectLevelEnum.getDesc(param.getProjectLevel()));
        }

        PriceApplyQueryDTO priceApplyDto = priceApplyConvert.toPriceApplyDto(param);
        priceApplyDto.setSubmitUsers(cmsPermission.getUserCodes());
        priceApplyDto.setCityNames(cmsPermission.getCities());

        return priceApplyDto;
    }


    public void updateStatus(PriceApplyUpdateStatusParam param) throws ServerException {
        PriceApplyEntity priceApply = priceApplyService.lambdaQuery()
                .eq(PriceApplyEntity::getApplyCode, param.getApplyCode())
                .one();

        if (Objects.isNull(priceApply)) {
            throw new ServerException("价格申请不存在");
        }

        priceApplyService.lambdaUpdate()
                .set(PriceApplyEntity::getStatus, param.getStatus())
                .set(PriceApplyEntity::getApproveRemark, priceApply.getApproveRemark() + " 管理员调整：" + param.getRemark())
                .eq(PriceApplyEntity::getApplyCode, param.getApplyCode())
                .update();

        List<ScreenApproveRecordEntity> list = screenApproveRecordService.lambdaQuery()
                .eq(ScreenApproveRecordEntity::getNaturalKey, param.getApplyCode())
                .eq(ScreenApproveRecordEntity::getSceneType, SceneServiceTypeEnum.PRICE_APPLY.getType())
                .orderByDesc(ScreenApproveRecordEntity::getCreateTime)
                .list();

        if (CollUtil.isNotEmpty(list)) {
            ScreenApproveRecordEntity screenApproveRecordEntity = list.get(0);
            if (screenApproveRecordEntity.getApproveLevel() != 0) {
                screenApproveRecordService.lambdaUpdate()
                        .set(ScreenApproveRecordEntity::getStatus, param.getStatus())
                        .set(ScreenApproveRecordEntity::getRemark, screenApproveRecordEntity.getRemark() + " 管理员调整：" + param.getRemark())
                        .eq(ScreenApproveRecordEntity::getNaturalKey, param.getApplyCode())
                        .update();
            }

        }


    }

    @PreDestroy
    public void shutdown() {
        log.info("正在关闭价格申请服务线程池...");
        try {
            // 尝试优雅关闭，最多等待10秒
            PRICE_APPLY_THREAD_POOL.shutdown();
            if (!PRICE_APPLY_THREAD_POOL.awaitTermination(10, TimeUnit.SECONDS)) {
                // 如果超时，强制关闭
                PRICE_APPLY_THREAD_POOL.shutdownNow();
                log.warn("价格申请服务线程池强制关闭");
            } else {
                log.info("价格申请服务线程池已成功关闭");
            }
        } catch (InterruptedException e) {
            // 如果当前线程被中断，则强制关闭线程池
            PRICE_APPLY_THREAD_POOL.shutdownNow();
            Thread.currentThread().interrupt(); // 重新设置中断标志
            log.error("关闭价格申请服务线程池时被中断", e);
        }
    }

}
