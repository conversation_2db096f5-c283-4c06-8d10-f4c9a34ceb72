package com.coocaa.meht.api.cheese.enums.tctask;

/**
 * 任务状态，1:待执行 2:测算中 3:已完成 4:已失败
 */
public enum TCTaskStatus {

    WAIT_EXECUTE(1, "待执行"),
    CALCULATING(2, "测算中"),
    COMPLETED(3, "已完成"),
    FAILED(4, "已失败");

    private final Integer code;
    private final String desc;

    TCTaskStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static TCTaskStatus getByCode(Integer code) {
        for (TCTaskStatus status : TCTaskStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
