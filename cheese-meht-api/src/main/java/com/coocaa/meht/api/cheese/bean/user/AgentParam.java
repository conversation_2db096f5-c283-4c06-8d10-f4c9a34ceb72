package com.coocaa.meht.api.cheese.bean.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 合同查询供应商配置参数
 * <AUTHOR>
 * @since 2015-01-02
 */
@Data
public class AgentParam {

    @Schema(description = "内部查所有：1，外部查询当前登录人：2", type = "Integer", example = "1")
    private Integer type;

    @Schema(description = "代理商名字模糊搜索字段", type = "string", example = "1")
    private String name;
}
