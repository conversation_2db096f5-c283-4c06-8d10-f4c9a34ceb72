package com.coocaa.meht.api.cheese.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.coocaa.meht.api.cheese.bean.tctask.ExcelTemplateDTO;
import lombok.SneakyThrows;

import java.rmi.ServerException;
import java.util.*;

/**
 * @program: cheese-meht-web-api
 * @ClassName TaskScoreFileListener
 * @description:
 * @author: 自定义监听器
 * @create: 2024-12-26 11:10
 * @Version 1.0
 **/
public class TaskScoreFileListener extends AnalysisEventListener<ExcelTemplateDTO> {

    Set<ExcelTemplateDTO> dtoSet = new HashSet<>();

    List<ExcelTemplateDTO> dtoList = new ArrayList<>();

    @SneakyThrows
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        //校验表头
        //或者更精准校验可以对比表头的字符串是否一致
        if (headMap.size() == 0 || headMap.size() <3) {
            throw new RuntimeException("导入模板不正确");
        }else if(headMap.containsKey(null) || headMap.containsKey("")){
            throw new RuntimeException("导入模板不正确");
        }
    }

    @SneakyThrows
    @Override
    public void invoke(ExcelTemplateDTO dto, AnalysisContext analysisContext) {
        if(ObjectUtil.isEmpty(dto.getCityName())){
            throw new ServerException("城市不能为空");
        }
        if(ObjectUtil.isEmpty(dto.getBuildingName())){
            throw new ServerException("楼宇名称不能为空");
        }
        if(ObjectUtil.isEmpty(dto.getBuildingType())){
            throw new ServerException("楼宇类型编码不能为空");
        }
        if(!dtoSet.contains(dto)){
            dtoSet.add(dto);
            dtoList.add(dto);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }

    public List<ExcelTemplateDTO> getDtoList(){
        return dtoList;
    }
}
