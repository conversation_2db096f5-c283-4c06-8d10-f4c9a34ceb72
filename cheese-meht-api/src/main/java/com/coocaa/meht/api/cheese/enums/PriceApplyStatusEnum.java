package com.coocaa.meht.api.cheese.enums;

import com.coocaa.meht.api.cheese.bean.price.PriceApplyNameCodeVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum PriceApplyStatusEnum {
    DRAFT(0, "草稿"),
    PENDING(1, "待审核"),
    PASSED(2, "已审核"),
    REJECTED(3, "审核不通过"),
    CANCEL(4, "取消或作废");

    private final Integer code;
    private final String desc;

    public static List<PriceApplyNameCodeVO> getCodeName() {
        List<PriceApplyNameCodeVO> priceApplyNameCodeVOS = new ArrayList<>();
        for (PriceApplyStatusEnum value : PriceApplyStatusEnum.values()) {
            PriceApplyNameCodeVO priceApplyNameCodeVO = new PriceApplyNameCodeVO();
            priceApplyNameCodeVO.setCode(value.getCode());
            priceApplyNameCodeVO.setName(value.getDesc());
            priceApplyNameCodeVOS.add(priceApplyNameCodeVO);
        }
        return priceApplyNameCodeVOS;
    }
}
