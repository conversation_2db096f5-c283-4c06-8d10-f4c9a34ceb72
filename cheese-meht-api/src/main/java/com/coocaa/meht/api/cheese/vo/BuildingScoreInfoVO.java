package com.coocaa.meht.api.cheese.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月16 11:07
 */
@Data
public class BuildingScoreInfoVO {

    @Schema(description = "加权得分")
    public Integer  avgScore;

    @Schema(description = "评分项")
    public List<BuildingParameterVO> parameter;


//    @Schema(description = "申请值")
//    public String  parameterValue;
//
//
//    @Schema(description = "单项得分")
//    public Decimal parameterScore;
//
//
//    @Schema(description = "网络参考值")
//    public String  thirdValue;
//
//    @Schema(description = "权重")
//    public Decimal  weightValue;
}
