package com.coocaa.meht.api.cheese.kafka;


import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @date 2024-11-11 14:55:01
 * <p> kafka生产者 </p>
 */
@Service
@Slf4j
public class KafkaProducerService {
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(String topic, String msg) {
        CompletableFuture<SendResult<String, String>> send = kafkaTemplate.send(topic, msg);
        send.whenComplete((result, exception) -> {
            if (exception != null) {
                log.error("发送kafka消息失败:{},topic:{},data:{}", exception.getMessage(), topic, msg);
            }
        });
    }

    public void sendMessage(String topic, String key, String msg) {
        CompletableFuture<SendResult<String, String>> send = kafkaTemplate.send(topic, key, msg);
        send.whenComplete((result, exception) -> {
            if (exception != null) {
                log.error("发送kafka消息失败:{},topic:{},data:{}", exception.getMessage(), topic, msg);
            }
        });
    }

    public void sendMessageSync(String topic, String key, String msg) {
        CompletableFuture<SendResult<String, String>> send = kafkaTemplate.send(topic, key, msg);
        try {
            SendResult<String, String> result = send.get(30, TimeUnit.SECONDS);
            RecordMetadata recordMetadata = result.getRecordMetadata();
            log.info("发送成功的消息topic:{},partition:{}", recordMetadata.topic(), recordMetadata.partition());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        } catch (ExecutionException | TimeoutException e) {
            log.error("同步发送kafka消息出错:", e);
            throw new RuntimeException(e);
        }
    }
}
