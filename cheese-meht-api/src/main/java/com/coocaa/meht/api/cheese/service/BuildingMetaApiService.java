package com.coocaa.meht.api.cheese.service;

import com.coocaa.meht.api.cheese.bean.building.*;
import com.coocaa.meht.api.cheese.bean.user.BuildingMetaManagerVO;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.vo.BuildingPropertyCompanyVO;
import com.coocaa.meht.api.cheese.vo.building.BuildingMetaListVO;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/16
 */
public interface BuildingMetaApiService {

    /**
     * 查询楼宇元数据列表
     *
     */
    PageResponseVo<BuildingMetaListVO> listBuildingMeta(PageRequestVo<BuildingMetaQueryDTO> queryDTO);

    BuildingMetaDetailVO getMetaDetailById(Long id);


    void updateMeta(BuildingMetaUpdateParam param);

    List<BuildingMetaPointDetailVO> getPricePointDetailByPointPlanId(Integer pointPlanId);

    List<BuildingMetaPointDetailVO> getContractPointDetailByPointPlanId(Integer pointPlanId);

    void syncBuildingMeta();

    void firstSave(BuildingMetaImportParam param);

    List<SspBuildingVO> queryByName(Collection<String> names);

    String syncBySsp(SspSyncBuilding sspSyncBuilding);

    List<SspBuildingVO> queryByStatus(Integer status);
    /**
     * 楼宇关联物业公司
     * @param buildingNo
     * @return
     */
    List<BuildingPropertyCompanyVO> getPropertyPerson(String buildingNo);

    void updateBuildingNameAi(BuildingMetaImportParam param);


    void updateLatitude(List<Long> ids);

    byte[] exportMeta(BuildingMetaQueryDTO dto);

    void refreshForbiddenIndustry(RefreshForbiddenIndustryParam param);

    void updateBaseMessage(UpdateBaseMessageParam param);

    void updateMetaMessage(List<Long> ids);

    void processAiMeta(Long id);

    void rent();

    void doubao(AiThirdBuildingParam param);

    void deepseek(AiThirdBuildingParam param);

    void tyqw(AiThirdBuildingParam param);

    void openAi(AiThirdBuildingParam param);

    /**
     * 获取楼宇负责人信息
     * @param buildingMetaNo 楼宇编码
     * @return 楼宇负责人信息
     */
    BuildingMetaManagerVO getManagerInfo(String buildingMetaNo);

    /**
     * 根据楼宇编码批量获取负责人信息
     * @param buildingMetaNos 楼宇编码集合
     * @return 楼宇负责人信息集合
     */
    List<BuildingMetaManagerVO> getManagerInfoList(List<String> buildingMetaNos);
}
