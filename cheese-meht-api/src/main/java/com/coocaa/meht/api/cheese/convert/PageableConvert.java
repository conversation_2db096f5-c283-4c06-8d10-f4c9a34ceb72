package com.coocaa.meht.api.cheese.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
public interface PageableConvert<T, R> {
    /**
     * 转换成分页结果
     */
    @Mappings({
            @Mapping(source = "current", target = "currentPage"),
            @Mapping(source = "total", target = "totalRows"),
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "pages", target = "totalPages"),
            @Mapping(source = "records", target = "rows")
    })
    PageResponseVo<R> toPageResponse(IPage<T> pagedRoles);
}
