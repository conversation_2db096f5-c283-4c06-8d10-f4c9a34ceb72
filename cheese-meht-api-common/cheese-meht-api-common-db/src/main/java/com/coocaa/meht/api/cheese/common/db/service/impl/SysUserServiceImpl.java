package com.coocaa.meht.api.cheese.common.db.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.api.cheese.common.db.bean.SysUserApproveVO;
import com.coocaa.meht.api.cheese.common.db.entity.SysUserEntity;
import com.coocaa.meht.api.cheese.common.db.mapper.SysUserMapper;
import com.coocaa.meht.api.cheese.common.db.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 员工 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUserEntity> implements ISysUserService {

    @Autowired
    private SysUserMapper sysUserMapper;


    @Override
    public List<SysUserEntity> userDetail(Collection<String> userCode) {

        return sysUserMapper.getUserListByCode(userCode);
    }

    @Override
    public SysUserApproveVO getUserDetail(String userCode) {
        return sysUserMapper.getUserDetail(userCode);
    }
}
