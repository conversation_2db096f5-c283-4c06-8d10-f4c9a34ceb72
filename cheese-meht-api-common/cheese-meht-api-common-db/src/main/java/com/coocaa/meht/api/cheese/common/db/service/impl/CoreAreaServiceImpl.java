package com.coocaa.meht.api.cheese.common.db.service.impl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.api.cheese.common.db.bean.CoreAreaDTO;
import com.coocaa.meht.api.cheese.common.db.entity.CoreAreaEntity;
import com.coocaa.meht.api.cheese.common.db.mapper.CoreAreaMapper;
import com.coocaa.meht.api.cheese.common.db.service.ICoreAreaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
/**
 * 核心区域操作 服务实现类
 *
 * <AUTHOR>
 * @since 2025-7-1
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CoreAreaServiceImpl extends ServiceImpl<CoreAreaMapper, CoreAreaEntity> implements ICoreAreaService {
    
    @Override
    public IPage<CoreAreaEntity> pageList(IPage<CoreAreaEntity> page, CoreAreaDTO coreArea) {
        return baseMapper.pageList(page, coreArea);
    }
}