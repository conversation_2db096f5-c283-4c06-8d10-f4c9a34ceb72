package com.coocaa.meht.api.cheese.common.db.service;

import com.coocaa.meht.api.cheese.common.db.entity.PointPicPriceSnapshotEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 点位价格图片快照表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface IPointPicPriceSnapshotService extends IService<PointPicPriceSnapshotEntity> {

    List<PointPicPriceSnapshotEntity> listByPointIds(Collection<Integer> pointIds);

    void removeByPointIds(List<Integer> collect);
}