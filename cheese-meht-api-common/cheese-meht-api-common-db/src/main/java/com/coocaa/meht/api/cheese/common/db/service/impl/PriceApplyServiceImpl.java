package com.coocaa.meht.api.cheese.common.db.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.api.cheese.common.db.bean.PriceApplyQueryDTO;
import com.coocaa.meht.api.cheese.common.db.dto.ApplyPointDTO;
import com.coocaa.meht.api.cheese.common.db.dto.PriceApplyDTO;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingRatingEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PriceApplyEntity;
import com.coocaa.meht.api.cheese.common.db.mapper.PriceApplyMapper;
import com.coocaa.meht.api.cheese.common.db.service.IPriceApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class PriceApplyServiceImpl extends ServiceImpl<PriceApplyMapper, PriceApplyEntity> implements IPriceApplyService {

    @Autowired
    private  PriceApplyMapper priceApplyMapper;
    @Override
    public IPage<PriceApplyDTO> listPage(IPage page, PriceApplyQueryDTO priceApplyQueryDTO) {
        return priceApplyMapper.priceApplyPage(page,priceApplyQueryDTO);
    }

    @Override
    public List<ApplyPointDTO> getApplyPoint(List<Integer> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return priceApplyMapper.getApplyPoint(ids);
    }

    @Override
    public List<PriceApplyDTO> getPriceApplyList(PriceApplyQueryDTO priceApplyQueryDTO) {
        return priceApplyMapper.priceApplyList(priceApplyQueryDTO);
    }
}
