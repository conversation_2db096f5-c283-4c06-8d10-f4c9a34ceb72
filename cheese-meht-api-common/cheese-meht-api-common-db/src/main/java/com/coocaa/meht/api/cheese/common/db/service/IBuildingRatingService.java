package com.coocaa.meht.api.cheese.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.meht.api.cheese.common.db.bean.BuildingRatingQueryDTO;
import com.coocaa.meht.api.cheese.common.db.bean.CustomerQueryDTO;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingRatingEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 楼宇基本信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
public interface IBuildingRatingService extends IService<BuildingRatingEntity> {

    IPage<BuildingRatingEntity> applyList(IPage iPage, BuildingRatingQueryDTO dto);

    List<BuildingRatingEntity>  listByStatus(Integer buildingStatus, Integer auditStatus);

    BuildingRatingEntity getByMapNo(String mapNo);

    List<BuildingRatingEntity> listByMapNos(List<String> mapNos);

    List<BuildingRatingEntity> listByUserCode(String userCode);

    void updateByBuildingRatingNo(BuildingRatingEntity buildingRating);

    IPage<BuildingRatingEntity> listCustomerPage(IPage<BuildingRatingEntity> page, CustomerQueryDTO customerQueryDto);

    IPage<BuildingRatingEntity> listHighSeaCustomer(IPage<BuildingRatingEntity> page, CustomerQueryDTO customerQueryDto);

}
