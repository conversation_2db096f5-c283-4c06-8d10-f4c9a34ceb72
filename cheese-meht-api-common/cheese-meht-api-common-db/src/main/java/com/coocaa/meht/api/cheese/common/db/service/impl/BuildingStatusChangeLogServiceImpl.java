package com.coocaa.meht.api.cheese.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.api.cheese.common.db.mapper.BuildingStatusChangeLogMapper;
import com.coocaa.meht.api.cheese.common.db.service.IBuildingStatusChangeLogService;
import com.coocaa.meht.api.cheese.common.tools.enums.BooleFlagEnum;
import com.coocaa.meht.api.cheese.common.tools.utils.UserCodeUtils;
import com.coocaa.meht.api.cheese.common.tools.utils.UserIdUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-07
 */

@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingStatusChangeLogServiceImpl extends ServiceImpl<BuildingStatusChangeLogMapper, BuildingStatusChangeLogEntity> implements IBuildingStatusChangeLogService {

    @Override
    public void addBusinessChangeLog(Integer businessId, String businessCode, String status, String userName) {
        BuildingStatusChangeLogEntity changeLogEntity = new BuildingStatusChangeLogEntity();
        changeLogEntity.setBizId(businessId.longValue());
        changeLogEntity.setBizCode(businessCode);
        changeLogEntity.setType(BuildingStatusChangeLogEntity.BizType.BUSINESS.getCode());
        changeLogEntity.setStatus(status);
        changeLogEntity.setChangeTime(LocalDateTime.now());
        changeLogEntity.setOperatorWno(UserCodeUtils.getUserCode());
        changeLogEntity.setOperatorName(userName);
        changeLogEntity.setOperator(UserIdUtils.getUserId().longValue());

        // 逻辑删除老数据后新增
        lambdaUpdate()
                .eq(BuildingStatusChangeLogEntity::getBizId, changeLogEntity.getBizId())
                .eq(BuildingStatusChangeLogEntity::getType, changeLogEntity.getType())
                .eq(BuildingStatusChangeLogEntity::getStatus, changeLogEntity.getStatus())
                .eq(BuildingStatusChangeLogEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .set(BuildingStatusChangeLogEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .update();

        save(changeLogEntity);
    }

}
