package com.coocaa.meht.api.cheese.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.api.cheese.common.db.entity.PointPicEntity;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
public interface IPointPicService extends IService<PointPicEntity> {
    List<PointPicEntity> listByPointIds(Collection<Integer> pointIds);
}