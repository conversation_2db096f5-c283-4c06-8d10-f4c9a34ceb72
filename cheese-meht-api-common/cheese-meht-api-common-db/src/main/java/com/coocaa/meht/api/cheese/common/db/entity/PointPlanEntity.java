package com.coocaa.meht.api.cheese.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 点位方案实体
 */
@Data
@TableName("point_plan")
public class PointPlanEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 对应building_rating表的no
     */
    private String buildingRatingNo;

    /**
     * 状态 字典0037
     */
    private String status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 商机编码
     * */
    private String businessCode;
} 