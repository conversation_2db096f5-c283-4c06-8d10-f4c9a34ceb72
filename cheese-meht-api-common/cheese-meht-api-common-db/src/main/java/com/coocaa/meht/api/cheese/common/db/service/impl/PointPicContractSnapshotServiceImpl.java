package com.coocaa.meht.api.cheese.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.api.cheese.common.db.entity.PointPicContractSnapshotEntity;
import com.coocaa.meht.api.cheese.common.db.mapper.PointPicContractSnapshotMapper;
import com.coocaa.meht.api.cheese.common.db.service.IPointPicContractSnapshotService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 点位合同图片快照表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
public class PointPicContractSnapshotServiceImpl extends ServiceImpl<PointPicContractSnapshotMapper, PointPicContractSnapshotEntity> implements IPointPicContractSnapshotService {

    @Override
    public List<PointPicContractSnapshotEntity> listByPointIds(Collection<Integer> pointIds) {
        if(CollectionUtils.isNotEmpty(pointIds)){
            return lambdaQuery().in(PointPicContractSnapshotEntity::getPointId,pointIds)
                    .list();
        }
        return new ArrayList<>();
    }
}