package com.coocaa.meht.api.cheese.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.api.cheese.common.db.bean.SysUserApproveVO;
import com.coocaa.meht.api.cheese.common.db.entity.SysUserEntity;


import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 员工 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
public interface ISysUserService extends IService<SysUserEntity> {

    /**
     * 根据工号获取员工信息
     * @param userCode
     * @return
     */
    List<SysUserEntity> userDetail(Collection<String> userCode);

    /**
     * 审批获取员工信息
     */
    SysUserApproveVO getUserDetail(String userCode);
}
