package com.coocaa.meht.api.cheese.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("city_rent")
public class CityRentEntity {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String province;
    
    private String city;
    
    private Long adCode;
    
    private BigDecimal officeRent;
    
    private BigDecimal residRent;
    
    private Integer deleted;
    
    private String createBy;
    
    private LocalDateTime createTime;
    
    private String updateBy;
    
    private LocalDateTime updateTime;
} 