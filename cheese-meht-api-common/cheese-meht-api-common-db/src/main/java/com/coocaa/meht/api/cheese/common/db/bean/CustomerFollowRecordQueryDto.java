package com.coocaa.meht.api.cheese.common.db.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-21
 */
@Data
public class CustomerFollowRecordQueryDto {

    /**
     * 商机编码
     */
    private String businessCode;

    /**
     * 商机名称
     */
    private String businessName;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 跟进方式
     */
    private String visitType;


    /**
     * 负责人
     */
    private List<String> createBy;

    /**
     * 是否有效跟进 [0 无效 , 1有效]
     */
    private Integer valid;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 所属大区
     */
    private String region;

    /**
     * 城市
     */
    private List<String> cities;

    /**
     * 跟进时间
     */
    private String followTimeStart;

    /**
     * 跟进结束时间-结束
     */
    private String followTimeEnd;

    /**
     * 创建时间-开始
     */
    private String createTimeStart;

    /**
     * 创建时间-结束
     */
    private String createTimeEnd;


    private List<String> createdBy;


}
