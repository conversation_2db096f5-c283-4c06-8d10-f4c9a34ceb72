package com.coocaa.meht.api.cheese.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.api.cheese.common.db.entity.SysConfigEntity;
import com.coocaa.meht.api.cheese.common.db.mapper.SysConfigMapper;
import com.coocaa.meht.api.cheese.common.db.service.ISysConfigService;
import org.springframework.stereotype.Service;

@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfigEntity> implements ISysConfigService {
} 