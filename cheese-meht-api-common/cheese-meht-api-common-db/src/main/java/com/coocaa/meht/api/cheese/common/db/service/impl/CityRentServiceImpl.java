package com.coocaa.meht.api.cheese.common.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.api.cheese.common.db.entity.CityRentEntity;
import com.coocaa.meht.api.cheese.common.db.mapper.CityRentMapper;
import com.coocaa.meht.api.cheese.common.db.service.ICityRentService;
import org.springframework.stereotype.Service;

@Service
public class CityRentServiceImpl extends ServiceImpl<CityRentMapper, CityRentEntity> implements ICityRentService {
    
    @Override
    public CityRentEntity getRent(String adCode) {
        LambdaQueryWrapper<CityRentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CityRentEntity::getAdCode, adCode);
        return getOne(wrapper);
    }

    @Override
    public CityRentEntity getFirstByCity(String city) {
        LambdaQueryWrapper<CityRentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CityRentEntity::getCity, city)
              .last("LIMIT 1");  // 限制只返回第一条记录
        return getOne(wrapper);
    }
} 