package com.coocaa.meht.api.cheese.common.db.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: cheese-meht-web-api
 * @ClassName UpdatePointStatusDTO
 * @description:
 * @author: zhangbinxian
 * @create: 2025-01-17 20:57
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdatePointStatusDTO {
    private List<String> pointCodes;

    private String status;
}
