package com.coocaa.meht.api.cheese.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 点位合同快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("point_contract_snapshot")
public class PointContractSnapshotEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 等候厅id
     */
    private Integer waitingHallId;

    /**
     * 点位编码
     */
    private String code;

    /**
     * 点位状态
     */
    private String pointStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 设备尺寸
     */
    private String deviceSize;

    /**
     * building_rating表的no
     */
    private String buildingRatingNo;

    /**
     * 城市id
     */
    private Integer city;

    /**
     * 点位序号
     */
    private Integer number;

    /**
     * 楼栋名称
     */
    private String buildingName;

    /**
     * 单元名称
     */
    private String unitName;

    /**
     * 楼层名称 对应字典0004
     */
    private String floor;

    /**
     * 等候厅名称
     */
    private String waitingHallName;

    /**
     * 等候厅类型，字典
     */
    private String waitingHallType;

    /**
     * 点位id
     */
    private Integer pointId;

    private Integer pointPlanId;

    private String floorDesc;

    private String businessCode;
} 