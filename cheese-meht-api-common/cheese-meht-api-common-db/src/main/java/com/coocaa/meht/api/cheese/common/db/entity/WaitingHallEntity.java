package com.coocaa.meht.api.cheese.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("waiting_hall")
public class WaitingHallEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String buildingRatingNo;
    private String buildingName;
    private String unitName;
    private String floor;
    private String waitingHallName;
    private String waitingHallType;
    private String createBy;
    private LocalDateTime createTime;
    private String updateBy;
    private LocalDateTime updateTime;
    private Integer pointPlanId;
    private String floorDesc;
} 