package com.coocaa.meht.api.cheese.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.api.cheese.common.db.bean.BusinessOpportunityPageDTO;
import com.coocaa.meht.api.cheese.common.db.bean.BusinessOpportunityQueryDTO;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingRatingEntity;
import com.coocaa.meht.api.cheese.common.db.entity.BusinessOpportunityEntity;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 商机
 */
public interface IBusinessOpportunityService extends IService<BusinessOpportunityEntity> {

    IPage<BusinessOpportunityPageDTO> listPage(IPage<BuildingRatingEntity> page, BusinessOpportunityQueryDTO queryDto);

}
