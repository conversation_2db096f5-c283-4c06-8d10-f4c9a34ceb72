package com.coocaa.meht.api.cheese.common.db.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.coocaa.meht.api.cheese.common.db.entity.PersonnelApprovalEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: cheese-meht
 * @ClassName SysPersonnelApprovalDto
 * @description:
 * @author: zhangbinxian
 * @create: 2024-12-24 11:40
 * @Version 1.0
 **/
@Data
public class PersonnelApprovalDTO extends PersonnelApprovalEntity {

    private String ids;
}
