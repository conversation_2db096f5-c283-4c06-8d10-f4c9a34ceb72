<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.BuildingSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.coocaa.meht.api.cheese.common.db.entity.BuildingSnapshotEntity">
    <!--@mbg.generated-->
    <!--@Table building_snapshot-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="building_rating_no" jdbcType="VARCHAR" property="buildingRatingNo" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="details_snapshot" jdbcType="LONGVARCHAR" property="detailsSnapshot" />
    <result column="meta_snapshot" jdbcType="LONGVARCHAR" property="metaSnapshot" />
    <result column="rating_snapshot" jdbcType="LONGVARCHAR" property="ratingSnapshot" />
    <result column="rating_version" jdbcType="VARCHAR" property="ratingVersion" />
    <result column="screen_snapshot" jdbcType="LONGVARCHAR" property="screenSnapshot" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, building_rating_no, create_by, create_time, delete_flag, details_snapshot, meta_snapshot, 
    rating_snapshot, rating_version, screen_snapshot, `type`, update_by, update_time
  </sql>
</mapper>