<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.api.cheese.common.db.mapper.BusinessOpportunityMapper">


    <select id="listPage" resultType="com.coocaa.meht.api.cheese.common.db.bean.BusinessOpportunityPageDTO">
        select
        bo.`id`,
        bo.`code`,
        bo.name,
        br.building_no,
        br.building_name,
        br.building_type,
        COALESCE(NULLIF(br.project_review_level, ''), br.project_level) as project_level,
        br.map_city,
        bo.update_time,
        bo.`status`,
        bo.create_time,
        bo.submit_user as submit_user_code
        from
        business_opportunity bo
        left join building_rating br on bo.building_no = br.building_no
        <where>
            <if test="condition.buildingName != null and condition.buildingName!=''">
                and br.building_name like concat('%',#{condition.buildingName},'%')
            </if>
            <if test="condition.code != null and condition.code!=''">
                and bo.`code` like concat('%',#{condition.code},'%')
            </if>
            <if test="condition.buildingType != null">
                and br.building_type = #{condition.buildingType}
            </if>
            <if test="condition.projectLevel != null and condition.projectLevel!=''">
                and (
                (br.project_review_level IS NOT NULL
                AND br.project_review_level != ''
                AND br.project_review_level = #{condition.projectLevel})
                OR
                ( (br.project_review_level IS NULL OR br.project_review_level = '')
                AND br.project_level = #{condition.projectLevel})
                )
            </if>

            <if test="condition.createTimeBegin != null and condition.createTimeBegin!=''">
                and bo.create_time &gt;= #{condition.createTimeBegin}
            </if>
            <if test="condition.createTimeEnd != null and condition.createTimeEnd!=''">
                and bo.create_time &lt;= #{condition.createTimeEnd}
            </if>
            <if test="condition.updateTimeBegin != null and condition.updateTimeBegin!=''">
                and bo.update_time &gt;= #{condition.updateTimeBegin}
            </if>
            <if test="condition.updateTimeEnd != null and condition.updateTimeEnd!=''">
                and bo.update_time &lt;= #{condition.updateTimeEnd}
            </if>
            <if test='condition.cities != null and condition.cities.size() > 0'>
                and br.map_city in
                <foreach collection='condition.cities' item='city' open='(' separator=',' close=')'>
                    #{city}
                </foreach>
            </if>
            <if test='condition.submitUsers != null and condition.submitUsers.size() > 0'>
                and br.submit_user in
                <foreach collection='condition.submitUsers' item='user' open='(' separator=',' close=')'>
                    #{user}
                </foreach>
            </if>
            <if test='condition.statusList != null and condition.statusList.size() > 0'>
                and bo.`status` in
                <foreach collection='condition.statusList' item='status' open='(' separator=',' close=')'>
                    #{status}
                </foreach>
            </if>
        </where>
        order by bo.update_time desc
    </select>

</mapper>
