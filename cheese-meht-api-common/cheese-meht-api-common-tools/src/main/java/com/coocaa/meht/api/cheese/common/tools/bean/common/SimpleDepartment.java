package com.coocaa.meht.api.cheese.common.tools.bean.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 部门信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SimpleDepartment {
    /**
     * 部门ID (飞书OpenId)
     */
    private String id;

    /**
     * 部门名称
     */
    private String name;
}
