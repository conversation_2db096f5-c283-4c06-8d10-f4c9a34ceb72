package com.coocaa.meht.api.cheese.common.tools.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/11/2
 */

@AllArgsConstructor
public enum PointStatusEnum {
    WAITING("0001-1", "待启用"),
    SALEABLE("0001-2", "可售"),
    MAINTAINING("0001-3", "维护中"),
    REMOVED("0001-4", "已拆除"),
    PENDING_SIGNATURE("0001-5", "待签约"),
    SIGNING("0001-6", "签约中");
    @Getter
    private final String code;
    private final String desc;


    /**
     * 根据code获取枚举
     */
    public static PointStatusEnum getByCode(String code) {
        for (PointStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }


}
