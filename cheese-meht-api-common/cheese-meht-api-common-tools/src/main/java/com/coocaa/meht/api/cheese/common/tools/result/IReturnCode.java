package com.coocaa.meht.api.cheese.common.tools.result;

/**
 * 类IReturnCode实现描述:
 *
 * <AUTHOR>
 * @date 2021年09月18 11:16 上午
 */
public interface IReturnCode {

    /**
     * 返回码
     *
     * @return 返回码
     */
    String getErrCode();

    /**
     * 提示信息
     *
     * @return 提示信息
     */
    String getMsg();

    /**
     * 默认
     */
    enum Default implements IReturnCode {

        /* -----------------成功---------------------------- */
        SUCCESS("1", "Success")

        /* ------------------888888 通用错误--------------------------- */, ERROR("0", "错误")

        /* ------------------999999 系统错误--------------------------- */, ERROR_SYSTEM_EXCEPTION("-1", "系统异常")

        /* ------------------101xxx 请求错误--------------------------- */, ERROR_REQUEST_METHOD_NOT_SUPPORT("101001", "请求方法不支持"), ERROR_REQUEST_NOT_FOUND("404", "访问地址不存在！"), REPEAT_REQUEST_NOT_ALLOWED("406", "无法生成响应实体"), ERROR_HTTP_100("100", "1XX错误"), ERROR_HTTP_300("300", "3XX错误"), ERROR_HTTP_400("400", "4XX错误"), ERROR_SERVER_505("505", "505 服务器异常")

        /* ------------------102xxx 参数错误--------------------------- */, ERROR_PARAM_NOT_NULL("102001", "参数不能为空"), ERROR_PARAM_EXCEPTION("102002", "参数异常"), ERROR_PARAM_ILLEGAL("102003", "参数非法"),
        CAN_NOT_NULL_ERROR("102004", "%s不能为空！"),
        PARAM_ILLEGAL_ERROR("102005", "%s参数非法")

        /* ------------------103xxx 数据错误--------------------------- */, ERROR_DATA_SAVE_FAILURE("103001", "数据保存失败"), ERROR_DATA_UPDATE_FAILURE("103002", "数据修改失败"), ERROR_DATA_DELETE_FAILURE("103003", "数据删除失败"),
        DATA_EXIST_ERROR("103004", "%s已存在，请重新设置！"),
        DATA_NOT_EXISTS_ERROR("103005", "数据不存在！"),
        NOT_EXISTS_ERROR("103006", "%s不存在！"),
        RULES_EXISTS_ERROR("103007", "%s已有规则占用！")


        /* ------------------104xxx 用户相关的错误--------------------------- */, ERROR_USER_NOT_EXIST("104001", "用户不存在"), ERROR_USER_PASSWORD_INCORRECT("104002", "用户或密码错误"), ERROR_USER_LOCKED("104003", "用户被锁定"), ERROR_USER_EXPIRE("104004", "用户已过期"), ERROR_USER_ARREARS("104005", "用户已欠费"),
        USER_PASSWORD_ILLEGAL_ERROR("104006", "密码不合法，长度必须不小于8位，且必须同时包含大小写字母，数字，特殊符号"),
        ILLEGAL_CODE_ERROR("104007", "验证码错误"),
        PWD_ERROR("104008", "账号密码错误,总共%s次机会,还剩%s次机会！"),
        CAN_NOT_DELETE_ERROR("104009", "已绑定角色，无法删除"),
        NOT_LOGIN_ERROR("104010", "用户没有登录"),
        NO_AUTHORITY_ERROR("104011", "您没有该权限"),
        USER_ROLE_ERROR("104012", "该角色下有用户不能删除"),
        USER_RESET_PASSWORD_ERROR("104013", "新密码与确认密码不正确")


        /* ------------------105xxx 账户相关的错误--------------------------- */, ERROR_ACCOUNT_NOT_EXIST("105001", "账户不存在"), ERROR_ACCOUNT_PASSWORD_INCORRECT("105002", "账户或密码错误"), ERROR_ACCOUNT_LOCKED("105003", "账户被锁定"), ERROR_ACCOUNT_EXPIRE("105004", "账户已过期"), ERROR_ACCOUNT_ARREARS("105005", "账户已欠费")


        /* ------------------106xxx 服务调用相关的错误--------------------------- */, ERROR_SERVICE_CALL_EXCEPTION("106001", "服务调用异常"), ERROR_SERVICE_RESPONSE_EXCEPTION("106002", "服务响应异常");

        /**
         * 返回码
         */
        private final String errCode;

        /**
         * 提示信息
         */
        private final String msg;

        /**
         * 构造方法
         *
         * @param errCode 返回码
         * @param msg     提示信息
         */
        Default(String errCode, String msg) {
            this.errCode = errCode;
            this.msg = msg;
        }

        /**
         * {@code errCode} get方法
         *
         * @return 错误码
         */
        @Override
        public String getErrCode() {
            return errCode;
        }

        /**
         * {@code msg} get方法
         *
         * @return 描述
         */
        @Override
        public String getMsg() {
            return msg;
        }
    }
}
