package com.coocaa.meht.api.cheese.common.tools.utils;

public class UserCodeUtils {
    private static final ThreadLocal<String> threadLocal = new ThreadLocal<>();

    public static String getUserCode(){
        return threadLocal.get();
    }
    public static void setUserCode(String userCode){
         threadLocal.set(userCode);
    }
    public static void remove(){
        threadLocal.remove();
    }


}
