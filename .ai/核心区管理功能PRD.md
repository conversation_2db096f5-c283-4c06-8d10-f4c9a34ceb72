# 核心区管理功能产品需求文档 (PRD)

## 文档修订记录

| 版本号 | 修订日期   | 修订人 | 修订内容 |
| :----- | :--------- | :----- | :------- |
| V1.0   | 2023-10-27 | AI助手 | 创建文档 |

---

## 1. 概述

### 1.1. 项目背景
为了满足业务运营中对地理区域精细化管理的需求，平台需要提供一个在地图上划分、管理和展示核心业务区域的功能。运营人员可以通过该功能，直观地在地图上绘制不同形状（圆形、矩形、多边形）的核心区，并对其进行命名和管理，为后续的资源分配、数据分析和业务决策提供地理围栏支持。

### 1.2. 功能简介
核心区管理功能允许用户在地图上通过可视化操作，创建、查看、编辑和删除核心业务区域。系统支持绘制圆形、矩形和不规则多边形三种形状的区域，并能保存这些区域的地理坐标、名称等信息。

---

## 2. 需求详述

### 2.1. 功能目标
- **可视化绘制**：支持用户在地图上方便快捷地绘制圆形、矩形、多边形区域。
- **精细化管理**：支持对已创建的核心区进行名称、形状、大小和位置的修改。
- **操作简便**：提供清晰的操作指引和流畅的交互体验，降低用户使用门槛。
- **数据持久化**：确保所有创建和修改的区域信息能够被准确保存和读取。

### 2.2. 用户角色
| 角色       | 描述                                     |
| :--------- | :--------------------------------------- |
| **运营人员** | 核心区的主要使用者，负责区域的划定与日常管理。 |
| **系统管理员** | 拥有最高权限，可查看和管理所有核心区数据。 |

### 2.3. 功能范围
- **查看核心区**：在地图上加载并展示所有已创建的核心区。
- **新增核心区**：支持绘制矩形、圆形、多边形三种类型的核心区。
- **编辑核心区**：支持修改核心区的名称、地理范围。
- **删除核心区**：支持删除不再需要的核心区。
- **搜索/筛选**：支持按城市筛选核心区。

### 2.4. 核心功能流程

#### 2.4.1. 查看核心区
1.  用户进入【核心区管理】页面。
2.  系统默认加载当前选定城市的所有核心区，并在地图上以不同颜色或样式的覆盖物进行展示。**（注：区域颜色由前端负责显示，无需后端存储）**
3.  地图上应清晰展示各个区域的轮廓和名称。
4.  当鼠标悬停在某个核心区上时，应高亮显示该区域，并以浮窗（Tooltip）形式展示其详细信息（如：名称、面积等）。
5.  页面右上角应显示当前城市的核心区总数，如 "成都 30个"。

#### 2.4.2. 新增核心区
1.  用户在页面顶部的工具栏中选择要绘制的区域类型："矩形选区"、"圆形选区"或"多边形选区"。
2.  地图进入绘制模式，鼠标指针样式改变以提示用户。
3.  用户在地图上完成绘制操作，一个处于"编辑中"状态的区域图形将出现在地图上。
4.  在图形中央或旁边出现一个输入框，提示用户输入"核心区名称"，同时出现【确认】和【取消】按钮。
5.  用户输入名称后，点击【确认】按钮。
6.  系统弹出二次确认对话框，提示"是否确认创建该核心区？"。
7.  用户确认后，系统保存核心区数据，图形变为"已保存"的普通状态。
8.  若用户点击【取消】按钮，则清除当前绘制的图形和信息，退出绘制模式。

**具体绘制交互说明:**
*   **矩形区域**：用户按住鼠标左键并拖拽，释放鼠标后完成绘制。
*   **圆形区域**：用户点击地图确定圆心，然后可通过两种方式完成绘制：
    *   **拖拽**：拖拽鼠标确定半径，再次点击或释放鼠标后完成。
    *   **输入半径**：在绘制过程中，允许用户在信息框中直接输入**半径值（单位：米，0-1,000,000）**，地图上的圆形会根据输入值实时变化。
*   **多边形区域**：用户在地图上连续单击以设置多边形的顶点，双击鼠标或单击第一个顶点以闭合图形，完成绘制。

**字段校验:**
*   **核心区名称**：必填，长度为1-15个字符，同一城市下名称不可重复。若不符合规则，【确认】按钮置灰或点击后在输入框下方提示错误。

#### 2.4.3. 编辑核心区
1.  用户单击地图上一个已存在的核心区。
2.  被选中的核心区进入"编辑模式"，其顶点或控制点变为可拖拽状态。
3.  同时出现【确认】和【取消】按钮，以及可编辑的名称输入框。
4.  用户可以通过拖拽控制点来修改区域的形状、大小和位置。也可以修改区域名称。
    *   **对于圆形区域**，除了拖拽，也应支持直接修改半径数值。
5.  修改完成后，用户点击【确认】按钮，系统弹出二次确认对话框"是否确认保存修改？"。确认后保存更改。
6.  若用户点击【取消】按钮，则所有修改被撤销，区域恢复到编辑前的状态。

#### 2.4.4. 删除核心区
1.  用户在"编辑模式"下，应出现一个【删除】按钮。
2.  用户点击【删除】按钮。
3.  系统弹出二次确认对话框，提示"您确定要删除【核心区名称】吗？此操作不可恢复。"
4.  用户确认后，该核心区将从地图和数据库中被永久删除。

### 2.5. 页面元素与交互说明

*   **地图主界面**：
    *   占据页面的主要区域，作为核心区的展示和操作平台。
    *   应包含缩放、平移等基本地图控件。
*   **城市筛选器**：位于页面左上方，允许用户切换不同城市以查看对应的核心区。
*   **绘图工具栏**：位于页面顶部，提供【矩形选区】、【圆形选区】、【多边形选区】按钮。
*   **核心区列表**：
    *   位于页面右侧，以列表形式展示当前城市下所有的核心区名称。
    *   列表支持上下滚动。
    *   点击列表中的任一核心区，地图视野应平移至该区域中央并高亮显示。
*   **区域信息框**：
    *   **绘制/编辑时**：直接叠加在图形上或旁边，包含名称输入框、确认、取消、删除按钮。对于圆形，额外包含半径输入框。
    *   **查看时**：鼠标悬停时出现的Tooltip，展示只读信息。
*   **提示与校验**：
    *   操作成功后，应有绿色的成功提示（Toast），如"核心区创建成功！"。
    *   操作失败或校验不通过时，应有红色的失败/警告提示，如"核心区名称已存在！"。
    *   所有删除等危险操作，必须有二次确认弹窗。

---

## 3. 非功能性需求

### 3.1. 性能
- **加载速度**：进入页面时，地图和核心区的加载时间应在3秒以内（网络状况良好时）。
- **操作响应**：在地图上绘制、拖拽、编辑区域时，应无明显卡顿，响应流畅。
- **数据承载**：单个城市支持至少1000个核心区（多边形顶点总数<100,000）的流畅展示和操作。

### 3.2. 易用性
- 界面布局清晰，功能分区明确。
- 图标和按钮含义清晰，关键操作有文字提示。
- 交互流程符合用户直觉，提供必要的防错机制（如二次确认）。

### 3.3. 兼容性
- **浏览器**：支持主流浏览器，包括 Chrome、Firefox、Edge 的最新版本。
- **分辨率**：在 `1440*900` 及以上分辨率下有良好的显示效果。

---

## 4. 待确认事项 (Questions)

为了使需求更加完善，希望能与您确认以下几点：

1.  **关于半径/范围**：在您提供的截图中，提到"支持输入信息 (0-1000000) 调整范围"。
    *   对于**圆形**区域，这是否指圆形的**半径**？单位是什么（例如：米）？
    *   对于**矩形**和**多边形**，这个数值代表什么？是否是某种业务属性，或者不需要为这两种类型设置此参数？

2.  **核心区列表**：除了在地图上展示，是否需要在页面侧边（如右侧）提供一个所有核心区的列表？如果需要，用户点击列表项时，地图是否应自动定位并高亮显示对应的核心区？

3.  **区域颜色**：核心区的显示颜色是系统为不同区域自动分配，还是允许用户在创建/编辑时自定义颜色？

4.  **多边形绘制**：绘制多边形时，是否有最小或最大顶点数的限制？

请您审阅以上文档。期待您的反馈，以便我们进行下一步的细化。 