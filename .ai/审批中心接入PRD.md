# 审批中心接入产品需求文档(PRD)

## 1. 产品概述

### 1.1 产品背景

当前系统使用自建的审批流程管理，审批数据存储在本地数据库的`screen_approve_record`表中（审批节点表）。随着业务发展和公司整体架构的调整，需要将审批流程迁移到统一的站内审批中心系统，通过`FeignInnerApprovalRpc`接口进行对接，但待办和已办事项仍需保留在本地数据库，以保持用户体验的连续性。

### 1.2 产品价值

1. **统一审批管理**：接入公司统一的审批中心，实现审批流程的标准化管理
2. **保持业务连续性**：保留本地待办和已办查询，确保业务功能不中断
3. **提升用户体验**：通过对审批流程的平滑过渡，避免用户感知到系统变化带来的不便
4. **数据一致性**：确保本地数据库与审批中心数据的一致性，提供可靠的数据查询

### 1.3 核心目标用户

1. 业务操作人员：提交审批申请的人员
2. 业务审批人员：负责审核审批的人员
3. 管理员：管理审批流程的人员

## 2. 产品需求

### 2.1 总体要求

1. 将现有系统的审批流程迁移到新的审批中心，但**待办和已办事项仍走本地数据库**
2. 确保历史审批数据的可查询和可追溯
3. 保证业务功能的连续性和稳定性
4. 提供平滑的过渡方案，避免影响用户体验
5. **保持本地审批数据的完整性和一致性**

### 2.2 功能需求

#### 2.2.1 审批提交


| 功能点       | 详细说明                                                                   | 优先级 |
| ------------ | -------------------------------------------------------------------------- | ------ |
| 审批发起     | 用户发起审批时，系统将审批请求发送至审批中心，同时在本地数据库保存审批记录 | P0     |
| 业务参数传递 | 支持将业务参数（如sceneType、operateType等）传递给审批中心                 | P0     |
| 附件上传     | 支持审批过程中的附件上传与查看                                             | P1     |

#### 2.2.2 审批处理


| 功能点   | 详细说明                                                       | 优先级 |
| -------- | -------------------------------------------------------------- | ------ |
| 审批同意 | 用户同意审批时，将操作同步至审批中心，并更新本地数据库状态     | P0     |
| 审批拒绝 | 用户拒绝审批时，将操作同步至审批中心，并更新本地数据库状态     | P0     |
| 审批撤回 | 用户撤回审批申请时，将操作同步至审批中心，并更新本地数据库状态 | P0     |
| 审批意见 | 支持审批过程中添加审批意见，并同步至审批中心                   | P0     |

#### 2.2.3 审批查询


| 功能点       | 详细说明                                                               | 优先级 |
| ------------ | ---------------------------------------------------------------------- | ------ |
| 待办任务查询 | 用户可查询待处理的审批任务，数据来源为本地数据库                       | P0     |
| 已办任务查询 | 用户可查询已处理的审批任务，数据来源为本地数据库                       | P0     |
| 我的申请查询 | 用户可查询自己发起的审批申请，数据来源为本地数据库                     | P0     |
| 审批详情查询 | 用户可查看审批详情，包括审批状态、流程节点等信息，数据来源为本地数据库 | P0     |

#### 2.2.4 数据同步


| 功能点   | 详细说明                                   | 优先级 |
| -------- | ------------------------------------------ | ------ |
| 实时同步 | 审批中心的审批结果实时同步到本地数据库     | P0     |
| 定时同步 | 定期从审批中心拉取审批状态，更新本地数据库 | P1     |
| 数据修复 | 当发现数据不一致时，提供数据修复机制       | P2     |

### 2.3 非功能需求


| 需求类型 | 详细说明                                             | 优先级 |
| -------- | ---------------------------------------------------- | ------ |
| 性能要求 | 审批操作响应时间不超过2秒，查询操作响应时间不超过1秒 | P0     |
| 可用性   | 系统可用性不低于99.9%                                | P0     |
| 并发能力 | 系统能够承受正常业务峰值的并发请求                   | P0     |
| 兼容性   | 确保与现有业务系统的兼容性，不影响其他功能的正常使用 | P0     |
| 安全性   | 确保数据传输和存储的安全性，符合公司的安全规范       | P0     |

## 3. 用户流程

### 3.1 审批发起流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant System as 业务系统
    participant LocalDB as 本地数据库
    participant ApprovalCenter as 审批中心

    User->>System: 填写审批信息并提交
    System->>ApprovalCenter: 调用审批中心接口发起审批
    ApprovalCenter-->>System: 返回审批实例编码instanceCode
    System->>LocalDB: 保存审批记录(节点表)
    System->>LocalDB: 保存审批单映射记录
    System-->>User: 提示审批发起成功
```

### 3.2 审批处理流程

```mermaid
sequenceDiagram
    participant User as 审批人
    participant System as 业务系统
    participant LocalDB as 本地数据库
    participant ApprovalCenter as 审批中心

    User->>System: 审批操作(同意/拒绝)
    System->>ApprovalCenter: 调用审批中心接口执行审批操作
    ApprovalCenter-->>System: 返回操作结果
    System->>LocalDB: 更新本地审批记录状态
    System-->>User: 提示审批操作成功
```

### 3.3 待办查询流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant System as 业务系统
    participant LocalDB as 本地数据库

    User->>System: 请求查看待办任务
    System->>LocalDB: 查询本地数据库待办任务
    LocalDB-->>System: 返回待办任务列表
    System-->>User: 展示待办任务列表
```

### 3.4 数据同步流程

```mermaid
sequenceDiagram
    participant System as 业务系统
    participant LocalDB as 本地数据库
    participant ApprovalCenter as 审批中心

    ApprovalCenter->>System: 审批状态变更通知
    System->>ApprovalCenter: 查询审批详情
    ApprovalCenter-->>System: 返回审批详情
    System->>LocalDB: 更新本地审批记录
  
    loop 定时同步
        System->>ApprovalCenter: 定时查询未完成的审批状态
        ApprovalCenter-->>System: 返回审批状态列表
        System->>LocalDB: 批量更新本地审批记录
    end
```

## 4. 交互界面

### 4.1 审批提交界面

提交审批的界面保持不变，后端处理逻辑修改为调用审批中心接口。

### 4.2 待办任务列表

待办任务列表页面保持不变，数据来源仍为本地数据库。

### 4.3 审批详情页面

审批详情页面保持不变，数据来源仍为本地数据库。

## 5. 数据模型

### 5.1 审批节点表 (screen_approve_record)


| 字段名            | 类型        | 说明             |
| ----------------- | ----------- | ---------------- |
| id                | BIGINT      | 主键ID           |
| natural_key       | VARCHAR(64) | 业务主键         |
| approve_user      | INT         | 审批人ID         |
| approve_user_name | VARCHAR(64) | 审批人姓名       |
| remark            | TEXT        | 审批意见         |
| status            | TINYINT     | 节点状态         |
| approve_time      | DATETIME    | 节点审批时间     |
| create_time       | DATETIME    | 节点开始时间     |
| approve_level     | INT         | 审批级别         |
| scene_type        | INT         | 场景类型         |
| operate_type      | INT         | 操作类型         |
| business_info     | TEXT        | 业务参数JSON     |
| instance_code     | VARCHAR(64) | 审批中心实例编码 |
| rule_code         | INT         | 审批中心规则编码 |
| approval_result   | VARCHAR(20) | 节点审批结果     |
| approval_status   | VARCHAR(20) | 审批状态         |
| approval_flag     | TINYINT     | 节点类型标识     |
| node_id           | BIGINT      | 审批中心节点ID   |
| sync_status       | VARCHAR(20) | 同步状态         |
| sync_time         | DATETIME    | 最后同步时间     |

### 5.2 审批单映射表 (screen_approval_instance)


| 字段名          | 类型         | 说明             |
| --------------- | ------------ | ---------------- |
| id              | BIGINT       | 主键ID           |
| instance_code   | VARCHAR(64)  | 审批实例编码     |
| rule_code       | INT          | 规则编码         |
| approval_name   | VARCHAR(128) | 审批任务名称     |
| approval_result | VARCHAR(20)  | 审批结果         |
| approval_status | VARCHAR(20)  | 审批状态         |
| cancel_reason   | VARCHAR(64)  | 取消原因         |
| user_id         | INT          | 审批单提交人ID   |
| user_name       | VARCHAR(64)  | 审批单提交人姓名 |
| end_time        | DATETIME     | 审批单结束时间   |
| create_time     | DATETIME     | 审批单创建时间   |
| update_time     | DATETIME     | 审批单更新时间   |
| sync_status     | VARCHAR(20)  | 同步状态         |
| sync_time       | DATETIME     | 最后同步时间     |

## 6. 接口文档

### 6.1 审批中心接口

#### 6.1.1 发起审批

**接口名称**: initiateApproval

**请求参数**:

```json
{
  "ruleCode": "审批规则编码",
  "businessParams": {
    "key1": "value1",
    "key2": "value2"
  },
  "description": "审批描述"
}
```

**响应参数**:

```json
{
  "success": true,
  "data": {
    "instanceCode": "审批实例编码"
  },
  "message": "操作成功"
}
```

#### 6.1.2 执行审批操作

**接口名称**: executeApproval

**请求参数**:

```json
{
  "instanceCode": "审批实例编码",
  "action": "AGREE/REJECT",
  "comment": "审批意见"
}
```

**响应参数**:

```json
{
  "success": true,
  "message": "操作成功"
}
```

#### 6.1.3 查询审批详情

**接口名称**: queryDetail

**请求参数**:

```json
{
  "instanceCode": "审批实例编码"
}
```

**响应参数**:

```json
{
  "success": true,
  "data": {
    "id": 1,
    "ruleId": 1,
    "ruleCode": 1,
    "instanceCode": "INS123456",
    "approvalName": "价格申请审批",
    "approvalResult": "APPROVED",
    "approvalResultName": "已通过",
    "approvalStatus": "FINISHED",
    "approvalStatusName": "已完成",
    "userId": 1001,
    "userName": "张三",
    "endTime": "2023-06-01 10:30:00",
    "createTime": "2023-06-01 09:00:00",
    "nodes": [
      {
        "id": 101,
        "ruleCode": 1,
        "instanceCode": "INS123456",
        "rank": 1,
        "userId": 1002,
        "userName": "李四",
        "nodeStatus": "FINISHED",
        "nodeStatusName": "已完成",
        "startTime": "2023-06-01 09:00:00",
        "endTime": "2023-06-01 10:30:00",
        "approvalFlag": 1,
        "approvalResult": "APPROVED",
        "approvalResultName": "已通过",
        "comment": "同意"
      }
    ]
  },
  "message": "操作成功"
}
```

### 6.2 本地接口

#### 6.2.1 查询待办任务

**接口名称**: queryTodoList

**请求参数**:

```json
{
  "userId": 1001,
  "pageSize": 10,
  "pageNum": 1
}
```

**响应参数**:

```json
{
  "success": true,
  "data": {
    "total": 2,
    "list": [
      {
        "id": 1,
        "naturalKey": "INS123456",
        "approveUser": 1001,
        "approveUserName": "张三",
        "status": 0,
        "createTime": "2023-06-01 09:00:00",
        "sceneType": 1,
        "sceneTypeName": "价格申请",
        "instanceCode": "INS123456"
      }
    ]
  },
  "message": "操作成功"
}
```

#### 6.2.2 查询已办任务

**接口名称**: queryDoneList

**请求参数**:

```json
{
  "userId": 1001,
  "pageSize": 10,
  "pageNum": 1
}
```

**响应参数**:
与查询待办任务相同

#### 6.2.3 查询审批详情

**接口名称**: queryApprovalDetail

**请求参数**:

```json
{
  "instanceCode": "INS123456"
}
```

**响应参数**:

```json
{
  "success": true,
  "data": {
    "instance": {
      "id": 1,
      "instanceCode": "INS123456",
      "ruleCode": 1,
      "approvalName": "价格申请审批",
      "approvalResult": "APPROVED",
      "approvalStatus": "FINISHED",
      "userId": 1001,
      "userName": "张三",
      "endTime": "2023-06-01 10:30:00",
      "createTime": "2023-06-01 09:00:00"
    },
    "nodes": [
      {
        "id": 1,
        "naturalKey": "INS123456",
        "approveUser": 1002,
        "approveUserName": "李四",
        "remark": "同意",
        "status": 1,
        "approveTime": "2023-06-01 10:30:00",
        "createTime": "2023-06-01 09:00:00",
        "approveLevel": 1,
        "instanceCode": "INS123456",
        "approvalResult": "APPROVED"
      }
    ]
  },
  "message": "操作成功"
}
```

## 7. 开发计划

### 7.1 迭代规划


| 迭代   | 时间周期 | 主要工作内容                                             |
| ------ | -------- | -------------------------------------------------------- |
| 迭代一 | 2周      | 准备工作：需求分析、方案设计、数据库设计                 |
| 迭代二 | 3周      | 核心功能开发：审批中心客户端、数据同步机制、业务代码改造 |
| 迭代三 | 2周      | 测试与优化：功能测试、数据同步测试、性能测试             |
| 迭代四 | 1周      | 灰度发布与全量上线：灰度发布、问题处理、全量上线         |

### 7.2 具体任务清单

1. **审批流程分析与配置**

   - [ ]  分析现有审批流程的业务规则
   - [ ]  在审批中心配置对应的流程
   - [ ]  建立审批类型映射关系
2. **本地数据库调整**

   - [ ]  设计数据库结构调整方案
   - [ ]  在审批节点表添加审批中心关联字段
   - [ ]  创建审批单映射表
   - [ ]  编写数据迁移脚本
3. **审批中心客户端开发**

   - [ ]  创建ApprovalCenterClient服务类
   - [ ]  实现发起审批接口
   - [ ]  实现审批操作接口
   - [ ]  实现查询接口
4. **数据同步机制开发**

   - [ ]  实现审批结果同步到本地数据库
   - [ ]  实现本地审批操作同步到审批中心
   - [ ]  开发定时同步任务
   - [ ]  实现数据转换适配器
5. **业务代码改造**

   - [ ]  修改审批提交逻辑
   - [ ]  修改审批操作逻辑
   - [ ]  **保留本地待办和已办查询逻辑**
   - [ ]  实现新旧数据兼容处理

## 8. 验收标准

### 8.1 功能验收

- 所有审批流程能够正常在新审批中心运行
- **待办和已办事项能够正常在本地数据库查询**
- **审批结果能够正确同步到本地数据库**
- 历史数据能够正常查询和展示

### 8.2 性能验收

- 审批操作响应时间不超过2秒
- 查询操作响应时间不超过1秒
- 系统能够承受正常业务峰值的并发请求

### 8.3 数据一致性验收

- 本地数据库与审批中心的数据保持一致
- 数据同步机制能够正常工作
- 同步失败能够被有效处理

### 8.4 用户体验验收

- 用户操作流程简洁明了
- 审批状态和结果展示清晰
- 提供足够的操作指引和帮助信息

## 9. 风险与应对措施


| 风险       | 描述                                       | 应对措施                                               |
| ---------- | ------------------------------------------ | ------------------------------------------------------ |
| 数据不一致 | 本地数据库与审批中心数据可能出现不一致情况 | 实施双向同步策略，定期校验数据一致性，设置同步状态标记 |
| 业务中断   | 系统切换过程中可能导致业务中断             | 选择业务低峰期进行切换，准备回滚方案                   |
| 性能影响   | 引入远程调用可能影响系统性能               | 实施性能测试，优化接口调用，必要时增加缓存             |
| 同步失败   | 数据同步过程可能出现失败                   | 实现同步重试机制，设置同步失败处理流程                 |

## 10. 附录

### 10.1 审批状态码映射


| 原系统节点状态 | 审批中心状态 | 说明           |
| -------------- | ------------ | -------------- |
| 0 (待审核)     | APPROVING    | 审批中         |
| 1 (通过)       | APPROVED     | 已通过         |
| 2 (拒绝)       | REJECTED     | 已拒绝         |
| 3 (撤回)       | CANCELED     | 已取消         |
| -              | DRAFTING     | 新状态：草稿中 |
| -              | WITHDRAW     | 新状态：已撤回 |

### 10.2 相关参考文档

- 审批中心API文档
- 现有审批系统设计文档
- 审批中心接入需求文档
