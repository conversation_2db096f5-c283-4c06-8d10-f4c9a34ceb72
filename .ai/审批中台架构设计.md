# 审批中心小中台架构设计文档

## 1. 设计背景与目标

### 1.1 背景

当前系统的审批流程需要迁移到统一的站内审批中心系统，通过`FeignInnerApprovalRpc`接口进行对接。为了降低业务系统与审批中心的耦合度，提高系统的可扩展性和可维护性，我们决定采用小中台设计模式，在业务系统与审批中心之间构建中间层。

### 1.2 设计目标

1. **解耦业务与审批流程**：通过中台层将业务逻辑与审批流程解耦
2. **提高可扩展性**：支持快速接入新的审批类型和业务场景
3. **统一接口规范**：为业务系统提供统一的审批接口
4. **保证数据一致性**：确保本地数据与审批中心数据的一致性
5. **简化维护成本**：集中管理审批功能，降低维护成本
6. **提高代码复用性**：抽取公共审批逻辑，提高代码复用率
7. **屏蔽外部实现**：中台负责与审批中心的交互，业务系统无需关心审批中心的实现细节和配置

## 2. 整体架构设计

### 2.1 架构图

```
+------------------+    +------------------+    +------------------+
|   业务系统模块A   |    |   业务系统模块B   |    |   业务系统模块C   |
+--------+---------+    +--------+---------+    +--------+---------+
         |                       |                       |
         v                       v                       v
+------------------------------------------------------+
|                  审批中台门面层(Facade)                 |
|                ApprovalCenterFacade                  |
+------------------------------------------------------+
         |                       |                       |
         v                       v                       v
+------------------------------------------------------+
|                  核心服务层(Core Service)               |
|     ApprovalProcessService, ApprovalQueryService     |
+------------------------------------------------------+
         |                       |                       |
         v                       v                       v
+------------------------------------------------------+
|               审批策略层(Strategy Layer)               |
|               ApprovalStrategyFactory               |
+------------------------------------------------------+
         |                       |                       |
         v                       v                       v
+------------------------------------------------------+
|                  适配器层(Adapter Layer)               |
|                 ApprovalCenterAdapter                |
+------------------------------------------------------+
         |                                   |
         v                                   v
+---------------------+        +---------------------------+
|    本地数据存储      |        |       审批中心系统         |
+---------------------+        +---------------------------+
```

### 2.2 分层说明

1. **门面层(Facade Layer)**：
   - 对外提供统一的审批操作接口
   - 屏蔽内部实现细节
   - 提供简单易用的API

2. **核心服务层(Core Service Layer)**：
   - 实现审批业务逻辑
   - 控制审批流程
   - 处理审批相关的业务规则

3. **策略层(Strategy Layer)**：
   - 基于策略模式实现不同类型审批的差异化处理
   - 支持灵活扩展新的审批类型

4. **适配器层(Adapter Layer)**：
   - 统一与审批中心系统的交互接口
   - 处理与本地数据库的交互
   - 实现数据同步机制
   - 封装与审批中心的交互细节，业务层无需关心底层实现

## 3. 关键设计模式应用

### 3.1 门面模式(Facade Pattern)

**应用场景**：为复杂的审批子系统提供统一的访问接口，简化客户端的调用。

**核心实现**：

```java
@Service
@Slf4j
@RequiredArgsConstructor
public class ApprovalCenterFacade {
    private final ApprovalProcessService processService;
    private final ApprovalQueryService queryService;
    
    // 统一审批操作接口
    public Result<String> submitApproval(ApprovalSubmitDTO dto) {
        log.info("Receive approval submit: {}", dto);
        return processService.submit(dto);
    }
    
    public Result<String> approveTask(ApprovalOperationDTO dto) {
        return processService.approve(dto);
    }
    
    public Result<String> rejectTask(ApprovalOperationDTO dto) {
        return processService.reject(dto);
    }
    
    // 统一查询接口
    public Result<List<TodoTaskDTO>> queryTodoList(String userId) {
        return queryService.queryTodoList(userId);
    }
    
    // 更多接口...
}
```

### 3.2 策略模式(Strategy Pattern)

**应用场景**：处理不同类型审批的差异化业务逻辑，支持灵活扩展。

**核心实现**：

```java
// 策略接口
public interface ApprovalStrategy {
    /**
     * 获取策略类型标识
     * @return 审批类型标识
     */
    String getType();
    
    /**
     * 验证审批数据的有效性
     * @param data 审批数据
     * @throws ApprovalBusinessException 当数据校验失败时抛出
     */
    void validateData(ApprovalDTO data);
    
    /**
     * 构建提交到审批中心的表单数据
     * @param data 审批数据
     * @return 转换后的表单数据
     */
    Map<String, Object> buildFormData(ApprovalDTO data);
}

// 具体策略实现
@Service
@Slf4j
public class PriceApprovalStrategy implements ApprovalStrategy {
    @Override
    public String getType() {
        return "PRICE_APPROVAL";
    }
    
    @Override
    public void validateData(ApprovalDTO data) {
        log.info("验证价格审批数据: {}", data);
        // 价格审批特有的数据校验逻辑
        if (data.getPrice() == null || data.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApprovalBusinessException("价格必须大于0");
        }
        
        if (StringUtils.isEmpty(data.getContractId())) {
            throw new ApprovalBusinessException("合同ID不能为空");
        }
        
        // 其他价格审批特有的验证逻辑
    }
    
    @Override
    public Map<String, Object> buildFormData(ApprovalDTO data) {
        log.info("构建价格审批表单数据");
        // 构建价格审批的表单数据
        Map<String, Object> formData = new HashMap<>();
        formData.put("contractId", data.getContractId());
        formData.put("price", data.getPrice());
        formData.put("currency", data.getCurrency());
        formData.put("department", data.getDepartment());
        formData.put("description", data.getDescription());
        formData.put("submitter", data.getSubmitter());
        formData.put("submitTime", new Date());
        
        return formData;
    }
}

// 策略工厂
@Service
@Slf4j
public class ApprovalStrategyFactory {
    private final Map<String, ApprovalStrategy> strategyMap = new ConcurrentHashMap<>();
    
    @Autowired
    public ApprovalStrategyFactory(List<ApprovalStrategy> strategies) {
        log.info("初始化审批策略工厂，加载策略数量: {}", strategies.size());
        for (ApprovalStrategy strategy : strategies) {
            String type = strategy.getType();
            log.info("注册审批策略: {}", type);
            strategyMap.put(type, strategy);
        }
    }
    
    /**
     * 获取指定类型的审批策略
     * 
     * @param type 审批类型
     * @return 对应的审批策略
     * @throws ApprovalBusinessException 当不支持指定的审批类型时抛出
     */
    public ApprovalStrategy getStrategy(String type) {
        ApprovalStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            log.error("不支持的审批类型: {}", type);
            throw new ApprovalBusinessException("不支持的审批类型: " + type);
        }
        return strategy;
    }
    
    /**
     * 获取所有支持的审批类型
     * 
     * @return 支持的审批类型集合
     */
    public Set<String> getSupportedTypes() {
        return Collections.unmodifiableSet(strategyMap.keySet());
    }
}
```

### 3.3 模板方法模式(Template Method Pattern)

**应用场景**：定义审批流程的骨架，将变化的部分延迟到子类实现。

**核心实现**：

```java
@Service
@RequiredArgsConstructor
public abstract class AbstractApprovalService {
    private final ApprovalStrategyFactory strategyFactory;
    private final ApprovalAdapter approvalAdapter;
    
    /**
 * 处理审批提交的模板方法
 * 定义了标准的审批处理流程
 * 
 * @param dto 审批请求数据
 * @return 审批结果，包含审批实例ID
 */
@Transactional(rollbackFor = Exception.class)
public final Result<String> processApproval(ApprovalDTO dto) {
    log.info("开始处理审批请求: {}", dto);
    try {
        // 1. 获取策略
        ApprovalStrategy strategy = strategyFactory.getStrategy(dto.getType());
        
        // 2. 数据校验
        log.debug("执行数据校验");
        strategy.validateData(dto);
        
        // 3. 构建表单
        log.debug("构建审批表单");
        Map<String, Object> formData = strategy.buildFormData(dto);
        
        // 4. 预处理业务逻辑
        log.debug("执行业务预处理");
        preProcess(dto);
        
        // 5. 提交审批
        log.debug("提交到审批中心");
        String instanceId = submitToApprovalSystem(dto, formData);
        log.info("审批提交成功，实例ID: {}", instanceId);
        
        // 6. 后处理
        log.debug("执行业务后处理");
        postProcess(dto, instanceId);
        
        return Result.success(instanceId);
    } catch (ApprovalBusinessException e) {
        log.warn("审批业务异常: {}", e.getMessage());
        return Result.fail(e.getMessage());
    } catch (Exception e) {
        log.error("审批处理异常", e);
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        return Result.fail("系统异常，请稍后重试");
    }
}
    
    // 子类实现的钩子方法
    protected abstract void preProcess(ApprovalDTO dto);
    protected abstract void postProcess(ApprovalDTO dto, String instanceId);
    
    private String submitToApprovalSystem(ApprovalDTO dto, Map<String, Object> formData) {
        return approvalAdapter.submit(dto.getType(), dto.getBusinessKey(), formData);
    }
}
```

### 3.4 适配器模式(Adapter Pattern)

**应用场景**：适配审批中心接口，使其符合系统的接口规范，隔离业务系统与审批中心直接交互。

**核心实现**：

```java
/**
 * 审批中心适配器接口
 * 定义与审批中心交互的标准方法
 */
public interface ApprovalAdapter {
    /**
     * 提交审批申请
     * 
     * @param type 审批类型
     * @param businessKey 业务唯一标识
     * @param formData 审批表单数据
     * @return 审批实例ID
     * @throws ApprovalSystemException 当调用审批中心失败时抛出
     */
    String submit(String type, String businessKey, Map<String, Object> formData);
    
    /**
     * 审批通过操作
     * 
     * @param instanceId 审批实例ID
     * @param opinion 审批意见
     * @throws ApprovalSystemException 当调用审批中心失败时抛出
     */
    void approve(String instanceId, String opinion);
    
    /**
     * 审批拒绝操作
     * 
     * @param instanceId 审批实例ID
     * @param opinion 审批意见
     * @throws ApprovalSystemException 当调用审批中心失败时抛出
     */
    void reject(String instanceId, String opinion);
    
    /**
     * 获取审批详情
     * 
     * @param instanceId 审批实例ID
     * @return 审批详情
     * @throws ApprovalSystemException 当调用审批中心失败时抛出
     */
    ApprovalDetailVO getDetail(String instanceId);
    
    /**
     * 获取审批节点信息
     * 
     * @param instanceId 审批实例ID
     * @return 审批节点列表
     * @throws ApprovalSystemException 当调用审批中心失败时抛出
     */
    List<ApprovalNodeVO> getNodes(String instanceId);
}

/**
 * 审批中心适配器实现类
 * 负责与审批中心系统交互，并保存本地审批记录
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ApprovalCenterAdapter implements ApprovalAdapter {
    private final FeignInnerApprovalRpc approvalRpc;
    private final ScreenApprovalInstanceMapper instanceMapper;
    private final ApprovalRuleConfig ruleConfig;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submit(String type, String businessKey, Map<String, Object> formData) {
        log.info("提交审批申请: 类型={}, 业务标识={}", type, businessKey);
        try {
            // 构建审批参数
            ApprovalInitiateParam param = buildApprovalParam(type, businessKey, formData);
            
            // 调用审批中心接口
            log.debug("调用审批中心发起审批");
            Result<String> result = approvalRpc.initiateApproval(param);
            
            if (!result.isSuccess()) {
                log.error("调用审批中心失败: {}", result.getMessage());
                throw new ApprovalSystemException("调用审批中心失败: " + result.getMessage());
            }
            
            String instanceId = result.getData();
            log.info("审批中心返回实例ID: {}", instanceId);
            
            // 保存审批实例
            saveApprovalInstance(type, businessKey, formData, instanceId);
            
            return instanceId;
        } catch (Exception e) {
            if (!(e instanceof ApprovalSystemException)) {
                log.error("提交审批异常", e);
                throw new ApprovalSystemException("提交审批失败: " + e.getMessage(), e);
            }
            throw e;
        }
    }
    
    @Override
    public void approve(String instanceId, String opinion) {
        log.info("审批通过操作: 实例ID={}, 意见={}", instanceId, opinion);
        try {
            ApprovalOperateParam param = new ApprovalOperateParam();
            param.setInstanceId(instanceId);
            param.setOpinion(opinion);
            param.setOperation("APPROVE");
            
            Result<Void> result = approvalRpc.operateApproval(param);
            if (!result.isSuccess()) {
                log.error("调用审批中心失败: {}", result.getMessage());
                throw new ApprovalSystemException("审批操作失败: " + result.getMessage());
            }
            
            // 更新本地审批状态
            updateLocalApprovalStatus(instanceId, "APPROVED");
            
        } catch (Exception e) {
            log.error("审批通过操作异常", e);
            throw new ApprovalSystemException("审批通过失败: " + e.getMessage(), e);
        }
    }
    
    // 其他方法实现...
    
    /**
     * 构建审批中心请求参数
     */
    private ApprovalInitiateParam buildApprovalParam(String type, String businessKey, Map<String, Object> formData) {
        ApprovalInitiateParam param = new ApprovalInitiateParam();
        
        // 获取审批类型对应的规则编码
        Integer ruleCode = ruleConfig.getRuleCode(type);
        if (ruleCode == null) {
            throw new ApprovalBusinessException("未配置审批类型对应的规则编码: " + type);
        }
        
        param.setRuleCode(ruleCode);
        param.setBusinessKey(businessKey);
        param.setFormData(JSON.toJSONString(formData));
        
        return param;
    }
    
    /**
     * 保存本地审批实例记录
     */
    private void saveApprovalInstance(String type, String businessKey, Map<String, Object> formData, String instanceId) {
        log.debug("保存本地审批实例记录");
        ScreenApprovalInstance instance = new ScreenApprovalInstance();
        instance.setInstanceId(instanceId);
        instance.setBusinessKey(businessKey);
        instance.setType(type);
        instance.setFormData(JSON.toJSONString(formData));
        instance.setStatus("PROCESSING");
        instance.setCreateTime(new Date());
        instance.setUpdateTime(new Date());
        
        instanceMapper.insert(instance);
        log.info("本地审批记录保存成功");
    }
    
    /**
     * 更新本地审批状态
     */
    private void updateLocalApprovalStatus(String instanceId, String status) {
        log.debug("更新本地审批状态: 实例ID={}, 状态={}", instanceId, status);
        ScreenApprovalInstance instance = new ScreenApprovalInstance();
        instance.setInstanceId(instanceId);
        instance.setStatus(status);
        instance.setUpdateTime(new Date());
        
        instanceMapper.updateByInstanceId(instance);
    }
}
```

### 3.5 观察者模式(Observer Pattern)

**应用场景**：实现审批状态变更的事件通知机制。

**核心实现**：

```java
/**
 * 审批事件监听器接口
 */
public interface ApprovalEventListener {
    /**
     * 判断是否支持处理指定场景类型的事件
     * 
     * @param sceneType 场景类型
     * @return 是否支持
     */
    boolean support(Integer sceneType);
    
    /**
     * 处理审批状态变更事件
     * 
     * @param event 状态变更事件
     */
    void onStatusChanged(ApprovalStatusChangeEvent event);
}

/**
 * 审批状态变更事件对象
 */
@Data
@AllArgsConstructor
public class ApprovalStatusChangeEvent {
    /**
     * 审批实例ID
     */
    private String instanceCode;
    
    /**
     * 业务唯一标识
     */
    private String naturalKey;
    
    /**
     * 业务场景类型
     */
    private Integer sceneType;
    
    /**
     * 审批状态
     */
    private String status;
    
    /**
     * 审批结果
     */
    private String result;
    
    /**
     * 审批时间
     */
    private Date approvalTime;
}

/**
 * 价格审批事件监听器
 * 处理价格审批相关的事件
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PriceApprovalEventListener implements ApprovalEventListener {
    private final PriceApprovalService priceApprovalService;
    
    @Override
    public boolean support(Integer sceneType) {
        return Objects.equals(sceneType, ApprovalSceneType.PRICE_APPROVAL.getCode());
    }
    
    @Override
    public void onStatusChanged(ApprovalStatusChangeEvent event) {
        log.info("价格审批状态变更: {}", event);
        
        try {
            String naturalKey = event.getNaturalKey();
            
            if (ApprovalResult.APPROVED.getValue().equals(event.getResult())) {
                log.info("处理价格审批通过逻辑, 业务标识: {}", naturalKey);
                priceApprovalService.handleApproved(naturalKey);
            } else if (ApprovalResult.REJECTED.getValue().equals(event.getResult())) {
                log.info("处理价格审批拒绝逻辑, 业务标识: {}", naturalKey);
                priceApprovalService.handleRejected(naturalKey);
            } else {
                log.info("其他状态变更，无需处理: {}", event.getResult());
            }
        } catch (Exception e) {
            log.error("处理价格审批事件异常", e);
            // 可以考虑将异常记录到专门的异常处理表，便于后续人工干预或重试
        }
    }
}
```

## 4. 核心组件设计

### 4.1 审批中台门面(ApprovalCenterFacade)

**职责**：
- 提供统一的审批操作接口
- 屏蔽内部实现细节
- 处理请求参数校验

**接口定义**：
- `submitApproval`: 提交审批
- `approveTask`: 审批通过
- `rejectTask`: 审批拒绝
- `queryTodoList`: 查询待办列表
- `queryDoneList`: 查询已办列表
- `queryDetail`: 查询审批详情

### 4.2 审批处理服务(ApprovalProcessService)

**职责**：
- 处理审批提交逻辑
- 处理审批操作逻辑
- 协调各组件完成审批流程

**核心方法**：
- `submit`: 提交审批
- `approve`: 审批通过
- `reject`: 审批拒绝

### 4.3 审批查询服务(ApprovalQueryService)

**职责**：
- 处理审批查询逻辑
- 支持待办、已办、详情查询

**核心方法**：
- `queryTodoList`: 查询待办列表
- `queryDoneList`: 查询已办列表
- `queryDetail`: 查询审批详情

### 4.4 审批策略(ApprovalStrategy)

**职责**：
- 实现特定类型审批的业务逻辑
- 处理数据校验、表单构建等

**核心方法**：
- `getType`: 获取策略类型
- `validateData`: 校验审批数据
- `buildFormData`: 构建审批表单

### 4.5 审批适配器(ApprovalAdapter)

**职责**：
- 适配审批中心接口
- 处理数据转换
- 保存审批记录
- 封装外部系统交互细节

**核心方法**：
- `submit`: 提交审批
- `approve`: 审批通过
- `reject`: 审批拒绝
- `getDetail`: 获取审批详情
- `getNodes`: 获取审批节点

### 4.6 数据同步服务(ApprovalSyncService)

**职责**：
- 确保本地数据与审批中心数据的一致性
- 定时同步审批状态
- 处理同步异常

**核心方法**：
- `syncApprovalStatus`: 同步审批状态
- `markSyncFailed`: 标记同步失败
- `retrySync`: 重试同步

## 5. 数据流转设计

### 5.1 审批提交流程

```mermaid
sequenceDiagram
    participant 业务系统
    participant ApprovalCenterFacade
    participant ApprovalProcessService
    participant ApprovalStrategy
    participant ApprovalAdapter
    participant 审批中心系统
    participant 本地数据库
    
    业务系统->>ApprovalCenterFacade: submitApproval(dto)
    ApprovalCenterFacade->>ApprovalProcessService: submit(dto)
    ApprovalProcessService->>ApprovalStrategy: getStrategy(type)
    ApprovalProcessService->>ApprovalStrategy: validateData(dto)
    ApprovalProcessService->>ApprovalStrategy: buildFormData(dto)
    ApprovalProcessService->>ApprovalProcessService: preProcess(dto)
    ApprovalProcessService->>ApprovalAdapter: submit(type, businessKey, formData)
    ApprovalAdapter->>审批中心系统: initiateApproval(param)
    审批中心系统-->>ApprovalAdapter: 返回审批实例ID
    ApprovalAdapter->>本地数据库: 保存审批实例记录
    ApprovalAdapter-->>ApprovalProcessService: 返回审批实例ID
    ApprovalProcessService->>ApprovalProcessService: postProcess(dto, instanceId)
    ApprovalProcessService-->>ApprovalCenterFacade: 返回Result
    ApprovalCenterFacade-->>业务系统: 返回Result
```

### 5.2 审批状态同步流程

```mermaid
sequenceDiagram
    participant 定时任务
    participant ApprovalSyncService
    participant 本地数据库
    participant ApprovalAdapter
    participant 审批中心系统
    participant ApprovalEventPublisher
    participant ApprovalEventListener
    
    定时任务->>ApprovalSyncService: 触发同步任务
    ApprovalSyncService->>本地数据库: 查询待同步的审批实例
    本地数据库-->>ApprovalSyncService: 返回实例列表
    
    loop 每个待同步实例
        ApprovalSyncService->>ApprovalAdapter: getDetail(instanceId)
        ApprovalAdapter->>审批中心系统: 查询审批详情
        审批中心系统-->>ApprovalAdapter: 返回审批详情
        ApprovalAdapter-->>ApprovalSyncService: 返回审批详情
        
        alt 状态有变更
            ApprovalSyncService->>本地数据库: 更新本地审批状态
            ApprovalSyncService->>ApprovalEventPublisher: 发布状态变更事件
            ApprovalEventPublisher->>ApprovalEventListener: 通知所有支持的监听器
            ApprovalEventListener->>ApprovalEventListener: 处理业务逻辑
        else 状态无变更
            ApprovalSyncService->>本地数据库: 更新同步时间
        end
    end
```

### 5.3 审批操作流程

```mermaid
sequenceDiagram
    participant 业务系统
    participant ApprovalCenterFacade
    participant ApprovalProcessService
    participant ApprovalAdapter
    participant 审批中心系统
    participant 本地数据库
    
    alt 审批通过
        业务系统->>ApprovalCenterFacade: approveTask(dto)
        ApprovalCenterFacade->>ApprovalProcessService: approve(dto)
        ApprovalProcessService->>ApprovalAdapter: approve(instanceId, opinion)
        ApprovalAdapter->>审批中心系统: operateApproval(param)
        审批中心系统-->>ApprovalAdapter: 返回操作结果
        ApprovalAdapter->>本地数据库: 更新本地审批状态为"已通过"
    else 审批拒绝
        业务系统->>ApprovalCenterFacade: rejectTask(dto)
        ApprovalCenterFacade->>ApprovalProcessService: reject(dto)
        ApprovalProcessService->>ApprovalAdapter: reject(instanceId, opinion)
        ApprovalAdapter->>审批中心系统: operateApproval(param)
        审批中心系统-->>ApprovalAdapter: 返回操作结果
        ApprovalAdapter->>本地数据库: 更新本地审批状态为"已拒绝"
    end
    
    ApprovalAdapter-->>ApprovalProcessService: 返回操作结果
    ApprovalProcessService-->>ApprovalCenterFacade: 返回Result
    ApprovalCenterFacade-->>业务系统: 返回Result
```

## 6. 扩展设计

### 6.1 新增审批类型扩展点

添加新的审批类型只需:
1. 实现ApprovalStrategy接口
2. 配置审批类型和处理逻辑
3. 实现对应的业务监听器(可选)

## 7. 中台内部配置设计

中台内部维护必要的配置信息，业务系统无需关心审批中心的配置细节。中台负责:

1. **审批类型映射管理**：维护业务审批类型与审批中心规则的映射关系
2. **审批权限管理**：集中管理审批权限规则
3. **审批表单字段管理**：统一管理各类审批所需的表单字段
4. **审批流程状态定义**：规范审批流程状态的定义和转换规则

业务系统只需通过中台提供的标准接口进行交互，无需了解底层配置细节。

### 7.1 配置类设计示例

```java
/**
 * 审批规则配置类
 */
@Component
@ConfigurationProperties(prefix = "approval")
@Data
public class ApprovalRuleConfig {
    /**
     * 审批类型与规则编码的映射
     */
    private Map<String, RuleInfo> rules = new HashMap<>();
    
    /**
     * 获取审批类型对应的规则编码
     */
    public Integer getRuleCode(String type) {
        RuleInfo ruleInfo = rules.get(type);
        return ruleInfo != null ? ruleInfo.getRuleCode() : null;
    }
    
    /**
     * 获取审批类型对应的场景类型
     */
    public Integer getSceneType(String type) {
        RuleInfo ruleInfo = rules.get(type);
        return ruleInfo != null ? ruleInfo.getSceneType() : null;
    }
    
    @Data
    public static class RuleInfo {
        /**
         * 规则编码
         */
        private Integer ruleCode;
        
        /**
         * 规则名称
         */
        private String ruleName;
        
        /**
         * 场景类型
         */
        private Integer sceneType;
        
        /**
         * 必填字段列表
         */
        private List<String> requiredFields = new ArrayList<>();
    }
}

/**
 * 审批场景类型枚举
 */
@Getter
@AllArgsConstructor
public enum ApprovalSceneType {
    
    PRICE_APPROVAL(2, "价格审批"),
    BUILDING_APPROVAL(1, "楼宇审批");
    
    private final Integer code;
    private final String desc;
    
    public static ApprovalSceneType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ApprovalSceneType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}

/**
 * 审批结果枚举
 */
@Getter
@AllArgsConstructor
public enum ApprovalResult {
    
    PROCESSING("PROCESSING", "处理中"),
    APPROVED("APPROVED", "已通过"),
    REJECTED("REJECTED", "已拒绝"),
    CANCELED("CANCELED", "已取消");
    
    private final String value;
    private final String desc;
}
```

## 8. 异常处理设计

### 8.1 异常分类

```java
/**
 * 审批业务异常
 * 用于表示业务规则验证失败等情况
 */
public class ApprovalBusinessException extends RuntimeException {
    
    public ApprovalBusinessException(String message) {
        super(message);
    }
    
    public ApprovalBusinessException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 审批系统异常
 * 用于表示调用审批中心失败等系统级异常
 */
public class ApprovalSystemException extends RuntimeException {
    
    public ApprovalSystemException(String message) {
        super(message);
    }
    
    public ApprovalSystemException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 审批同步异常
 * 用于表示数据同步过程中的异常
 */
public class ApprovalSyncException extends RuntimeException {
    
    private final String instanceId;
    
    public ApprovalSyncException(String instanceId, String message) {
        super(message);
        this.instanceId = instanceId;
    }
    
    public ApprovalSyncException(String instanceId, String message, Throwable cause) {
        super(message, cause);
        this.instanceId = instanceId;
    }
    
    public String getInstanceId() {
        return instanceId;
    }
}
```

1. **业务异常(ApprovalBusinessException)**：
   - 数据校验失败（如必填字段缺失、数据格式错误）
   - 业务规则不符合（如超出权限范围、状态不允许操作）
   - 未找到指定的审批类型

2. **系统异常(ApprovalSystemException)**：
   - 审批中心调用失败（如网络异常、超时）
   - 数据库操作失败（如SQL错误、事务异常）
   - 系统配置错误（如缺少必要配置）

3. **同步异常(ApprovalSyncException)**：
   - 同步数据失败（如获取审批中心数据失败）
   - 数据一致性问题（如本地状态与审批中心状态不一致）
   - 同步过程中的业务处理失败

### 8.2 异常处理策略

1. **业务异常处理**：
   - 直接返回错误信息给客户端
   - 记录错误日志

2. **系统异常处理**：
   - 标记同步状态为失败
   - 触发告警
   - 提供重试机制

3. **同步异常处理**：
   - 记录同步失败原因
   - 安排定时任务重试
   - 提供手动修复工具

## 9. 安全性设计

### 9.1 数据安全

1. **表单数据加密**：敏感数据在传输和存储时进行加密
2. **审批记录不可篡改**：审批记录一旦生成不可修改
3. **操作日志完整性**：记录所有审批操作的详细日志

### 9.2 权限控制

1. **操作权限校验**：审批操作前检查用户权限
2. **数据范围控制**：用户只能查看和操作有权限的审批数据
3. **敏感操作审计**：记录关键审批操作的审计日志

## 10. 性能优化设计

### 10.1 缓存策略

1. **审批规则缓存**：中台内部缓存审批规则，减少查询开销
2. **审批详情缓存**：缓存审批详情数据，减少审批中心调用
3. **待办已办列表缓存**：缓存用户待办已办列表，提高查询性能

### 10.2 异步处理

1. **异步提交审批**：大型审批表单异步处理
2. **异步状态同步**：审批状态变更异步同步
3. **批量数据同步**：批量同步审批状态，减少接口调用次数

## 11. 总结与展望

### 11.1 架构优势

1. **高内聚低耦合**：各组件职责明确，耦合度低
2. **可扩展性强**：基于策略模式和适配器模式，支持灵活扩展
3. **维护成本低**：集中管理审批逻辑，降低维护成本
4. **业务适应性强**：能够快速适应业务变化和新需求
5. **屏蔽复杂性**：业务系统无需关心审批中心的配置和实现细节

### 11.2 后续演进

1. **自定义流程配置**：支持业务系统自定义审批流程
2. **审批数据分析**：提供审批数据分析功能，优化审批流程
3. **移动端支持**：优化移动端审批体验
 