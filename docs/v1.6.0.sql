-- 城市表
CREATE TABLE `venue_city`
(
    `id`            INT(11) UNSIGNED        NOT NULL  COMMENT '取权限系统ID',
    `region_id`     INT(11) UNSIGNED        NOT NULL DEFAULT '0' COMMENT '区域id',
    `business_head` INT(11) UNSIGNED        NOT NULL DEFAULT '0' COMMENT '业务负责人id',
    `legal_bp`      INT(11) UNSIGNED        NOT NULL DEFAULT '0' COMMENT '法务bp id',
    `finance_bp`    INT(11) UNSIGNED        NOT NULL DEFAULT '0' COMMENT '财务bp id',
    `priority`      INT(11) UNSIGNED        NOT NULL DEFAULT '500' COMMENT '优先级',
    `name`          VARCHAR(50)       DEFAULT '' COMMENT '城市名称',
    `code`          VARCHAR(20)       DEFAULT '' COMMENT '城市编码',
    `create_time`   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`       INT(11) UNSIGNED        NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_time`   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `operator`      INT(11) UNSIGNED                 DEFAULT '0' COMMENT '操作人',
    `delete_flag`   TINYINT(1) UNSIGNED              DEFAULT '0' COMMENT '删除标识。0-否，1-是',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='城市信息表';
-- 大区表
CREATE TABLE `venue_region`
(
    `id`            INT(11) UNSIGNED        NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `business_head` INT(11) UNSIGNED        NOT NULL DEFAULT '0' COMMENT '业务负责人id',
    `legal_bp`      INT(11) UNSIGNED        NOT NULL DEFAULT '0' COMMENT '法务bp id',
    `finance_bp`    INT(11) UNSIGNED        NOT NULL DEFAULT '0' COMMENT '财务bp id',
    `priority`      INT(11) UNSIGNED        NOT NULL DEFAULT '500' COMMENT '优先级',
    `name`          VARCHAR(50)       DEFAULT '' COMMENT '大区名称',
    `create_time`   DATETIME          DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`       INT(11) UNSIGNED        NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_time`   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `operator`      INT(11) UNSIGNED                 DEFAULT '0' COMMENT '操作人',
    `delete_flag`   TINYINT(1) UNSIGNED              DEFAULT '0' COMMENT '删除标识。0-否，1-是',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='大区信息表';

-- 异常合同主表
CREATE TABLE `venue_contract_abnormal`
(
    `id`              INT ( 11 ) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `contract_id`     INT ( 11 ) NOT NULL COMMENT '原合同id',
    `type`            VARCHAR(16)  NOT NULL DEFAULT '' COMMENT '字典 0099 异常类型：竟对打压，物业公司内部原因，技术限制，我方原因',
    `apply_status`    VARCHAR(16)  NOT NULL DEFAULT '' COMMENT '字典 0102 申请状态，审批中，退回，驳回，处理中，已完成',
    `abnormal_count`  INT ( 11 ) NOT NULL COMMENT '异常点位数，所有项目合计',
    `import_flag`     TINYINT ( 1 ) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否导入：1-是，0-否',
    `over_date_flag`  TINYINT ( 1 ) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否超期未处理：1-是，0-否',
    `handler`         INT ( 11 ) NOT NULL COMMENT '处理人',
    `handle_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '填写时间',
    `handle_remark`   VARCHAR(500) NOT NULL DEFAULT '' COMMENT '处理说明',
    `apply_code`      VARCHAR(32)  NOT NULL DEFAULT '' COMMENT '申请编码',
    `abnormal_reason` VARCHAR(16)  NOT NULL DEFAULT '' COMMENT '异常原因 字典0101',
    `abnormal_remark` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '异常说明',
    `create_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator`         INT ( 10 ) UNSIGNED DEFAULT '0' COMMENT '创建人',
    `operator`        INT ( 10 ) UNSIGNED DEFAULT '0' COMMENT '操作人',
    `deleted`         TINYINT ( 1 ) UNSIGNED NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：1-删除，0-未删除',
    `apply_time`      DATETIME     NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '申请时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB  COMMENT = '异常合同主表';


-- 异常合同项目表
CREATE TABLE `venue_contract_error_project`
(
    `id`                     INT ( 11 ) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sign_count`             INT ( 10 ) NOT NULL DEFAULT '0' COMMENT '签约点位数',
    `abnormal_count`         INT ( 10 ) NOT NULL DEFAULT '0' COMMENT '异常点位数',
    `paid_amount`            DECIMAL(12, 2) UNSIGNED NOT NULL DEFAULT '0' COMMENT '付款金额',
    `return_invoiced_amount` DECIMAL(12, 2) UNSIGNED NOT NULL DEFAULT '0' COMMENT '回票金额',
    `install_count`          INT ( 10 ) NOT NULL DEFAULT '0' COMMENT '安装点位',
    `abnormal_id`            INT ( 11 ) NOT NULL COMMENT '异常主表id',
    `project_name`           VARCHAR(100) NOT NULL DEFAULT '' COMMENT '项目名称',
    `project_code`           VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '项目编码(商机号)',
    `create_time`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator`                INT ( 10 ) UNSIGNED DEFAULT '0' COMMENT '创建人',
    `operator`               INT ( 10 ) UNSIGNED DEFAULT '0' COMMENT '操作人',
    `deleted`                TINYINT ( 1 ) UNSIGNED NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：1-删除，0-未删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB  COMMENT = '异常合同项目表';

-- 处理方式表
CREATE TABLE `venue_contract_error_deal`
(
    `id`               INT ( 11 ) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `error_id`         INT ( 10 ) NOT NULL DEFAULT '0' COMMENT '异常主表id',
    `type`             VARCHAR(16)   NOT NULL DEFAULT '' COMMENT '类型(字典)',
    `approval_opinion` VARCHAR(16)   NOT NULL DEFAULT '' COMMENT '审批意见(字典)',
    `abnormal_reason`  VARCHAR(16)   NOT NULL DEFAULT '' COMMENT '异常原因(字典)',
    `deal_type`        VARCHAR(16)   NOT NULL DEFAULT '' COMMENT '新增字典，建议处理方式：业务处理，财务处理，终止合同',
    `deal_date`        DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建议处理时间',
    `remark`           VARCHAR(1500) NOT NULL DEFAULT '' COMMENT '备注',
    `create_time`      DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator`          INT ( 10 ) UNSIGNED DEFAULT '0' COMMENT '创建人',
    `operator`         INT ( 10 ) UNSIGNED DEFAULT '0' COMMENT '操作人',
    `deleted`          TINYINT ( 1 ) UNSIGNED NOT NULL DEFAULT '0' COMMENT '逻辑删除标志：1-删除，0-未删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB  COMMENT = '处理方式表';

-- 附件表增加类型
alter table venue_contract_attachment
    add belong_to TINYINT(1) unsigned default 1 not null comment '附件归属的主表： [1:合同附件，2:异常申请附件]';
-- 合同表增加异常状态标志
ALTER TABLE `venue_contract`
    ADD COLUMN `abnormal_flag` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '异常状态：[0:无, 1:疑似异常, 2:异常]';
