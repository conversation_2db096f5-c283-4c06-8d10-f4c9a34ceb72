-- cheese_cms
-- 合同状态变更记录
CREATE TABLE `venue_status_change_log`
(
    `id`            INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type`          VARCHAR(16)      NOT NULL DEFAULT '0042-5' COMMENT '数据类型 (字典0042)',
    `sub_type`      INT(11) UNSIGNED          DEFAULT '0' COMMENT '数据类型子类型',
    `biz_id`        INT(11) UNSIGNED          DEFAULT '0' COMMENT '业务ID (合同,...)',
    `biz_code`      VARCHAR(32)               DEFAULT '' COMMENT '业务编码 (合同,...)',
    `status`        VARCHAR(16)               DEFAULT '' COMMENT '业务状态(字典0028)',
    `change_time`   DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '状态变更时间',
    `operator`      INT(11) UNSIGNED          DEFAULT '0' COMMENT '状态变更操作人',
    `operator_wno`  VARCHAR(16)               DEFAULT '' COMMENT '状态变更操作人工号',
    `operator_name` VARCHAR(20)               DEFAULT '' COMMENT '状态变更操作人姓名',
    `content`       TEXT COMMENT '补充内容',
    `delete_flag`   TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `create_time`   DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`, `biz_id`) COMMENT '变更类型索引'
) ENGINE = InnoDB COMMENT ='合同状态变更记录';

-- 数据权限控制
CREATE TABLE `venue_data_access`
(
    `id`            INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`       INT(11)                   DEFAULT '0' COMMENT '用户ID',
    `user_name`     VARCHAR(20)               DEFAULT '' COMMENT '用户名称',
    `wno`           VARCHAR(10)               DEFAULT '' COMMENT '工号',
    `access_type`   VARCHAR(16)               DEFAULT '' COMMENT '权限类型',
    `city_list_str` TEXT COMMENT 'JSON格式城市列表，[]表示全部城市',
    `create_time`   DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`       INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_time`   DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `operator`      INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    PRIMARY KEY (`id`)
) ENGINE = INNODB COMMENT = '数据权限管理';

-- 合同点位统计记录
CREATE TABLE `venue_contract_point_statistics`
(
    `id`                      INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `city_id`                 INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '城市ID',
    `city_name`               VARCHAR(20)      NOT NULL DEFAULT '' COMMENT '城市名称',
    `signed_count`            INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '归档数',
    `installed_count`         INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '安装数',
    `charged_count`           INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '计费数',
    `available_count`         INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '可售数',
    `fault_count`             INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '故障数',
    `charged_available_ratio` DECIMAL(10, 4)   NOT NULL DEFAULT '0.0000' COMMENT '计费可售比',
    `statistics_date`         DATE             NOT NULL DEFAULT '1970-01-01' COMMENT '统计日期',
    `create_time`             DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_statistics_date` (`statistics_date`),
    KEY `idx_city_id` (`city_id`)
) ENGINE = INNODB COMMENT = '城市维度点位状态统计表';


-- 增加字段标记是否导入合同
ALTER TABLE `venue_contract`
    CHANGE `old_flag` `old_flag` TINYINT(2) UNSIGNED DEFAULT 0 NULL COMMENT '老合同变更标记 [0:否, 1:是]',
    ADD COLUMN `import_flag` TINYINT(2) UNSIGNED DEFAULT 0 NULL COMMENT '是否其它系统导入合同 [0:否, 1:是]' AFTER `old_contract_code`;


-- 设备数据导入记录表
CREATE TABLE `statistics_device_import_record`
(
    `id`              int(11) unsigned    NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `statistics_date` date                NOT NULL DEFAULT '1970-01-01' COMMENT '数据统计日期',
    `import_time`     datetime            NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '导入时间',
    `file_url`        varchar(120)        NOT NULL DEFAULT '' COMMENT '文件下载地址',
    `version`         tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '版本',
    `deleted`         tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '删除标志',
    `creator`         int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`        int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '操作人',
    `update_time`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='设备数据导入记录表';

-- 设备数据导入模板 - sheet1 - 城市设备导入数量表
CREATE TABLE `statistics_device_city_data_quantity`
(
    `id`                              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `import_no`                       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '关联批次ID',
    `city_id`                         int(11) unsigned NOT NULL DEFAULT '0' COMMENT '城市ID',
    `city_name`                       varchar(20)      NOT NULL DEFAULT '' COMMENT '城市名称',
    `warehouse_logistics_count`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '仓储物流数',
    `work_order_dispatch_count`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '派工数',
    `site_survey_count`               int(11) unsigned NOT NULL DEFAULT '0' COMMENT '踏勘数',
    `mounting_plate_count`            int(11) unsigned NOT NULL DEFAULT '0' COMMENT '挂板数',
    `installation_and_lighting_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '安装点亮数',
    `create_time`                     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='城市设备导入数量表';

-- 设备数据导入模板 - sheet2 - 设备生产数量表
CREATE TABLE `statistics_device_production_quantity`
(
    `id`                        int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `import_no`                 int(11) unsigned NOT NULL DEFAULT '0' COMMENT '关联批次ID',
    `order_count`               int(11) unsigned NOT NULL DEFAULT '0' COMMENT '下单数',
    `production_delivery_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '生产交付数',
    `create_time`               datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='设备生产数量表';

