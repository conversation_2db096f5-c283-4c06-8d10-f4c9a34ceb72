-- 增加发票信息
USE cheese_cms;
ALTER TABLE `venue_contract`
    ADD COLUMN `invoice_type`          VARCHAR(16)  DEFAULT '' NULL COMMENT '发票类型(字典0044)' AFTER `agent_name`,
    ADD COLUMN `tax_point`             VARCHAR(16)  DEFAULT '' NULL COMMENT '税点(字典0045)' AFTER `invoice_type`,
    ADD COLUMN `agent_approver`        VARCHAR(20)  DEFAULT '' COMMENT '代理商审批人' AFTER `agent_name`,
    ADD COLUMN `agent_approval_reason` VARCHAR(500) DEFAULT '' NULL COMMENT '代理商审批原因' AFTER `agent_approver`,
    ADD COLUMN `old_contract_code`     VARCHAR(30)  DEFAULT '' NULL COMMENT '老合同编码' AFTER `old_flag`;


-- 字典 TODO 需要修改子项的父ID
USE cheese_authority;
INSERT INTO dict (parent_id, name, code, `rank`, status) VALUES (331, '代理合同预审核', '0025-7', 7, 1);

INSERT INTO dict (parent_id, name, code, `rank`, status) VALUES (0, '发票类型', '0044', 0, 1);
INSERT INTO dict (parent_id, name, code, `rank`, status) VALUES (470, '增值税专用发票', '0044-1', 1, 1);
INSERT INTO dict (parent_id, name, code, `rank`, status) VALUES (470, '增值税普通发票', '0044-2', 2, 1);

INSERT INTO dict (parent_id, name, code, `rank`, status) VALUES (0, '税点', '0045', 0, 1);
INSERT INTO dict (parent_id, name, code, `rank`, status) VALUES (471, '1', '0045-1', 1, 1);
INSERT INTO dict (parent_id, name, code, `rank`, status) VALUES (471, '3', '0045-2', 2, 1);
INSERT INTO dict (parent_id, name, code, `rank`, status) VALUES (471, '5', '0045-3', 3, 1);
INSERT INTO dict (parent_id, name, code, `rank`, status) VALUES (471, '6', '0045-4', 4, 1);
INSERT INTO dict (parent_id, name, code, `rank`, status) VALUES (471, '9', '0045-5', 5, 1);