-- 价格周期增加发票类型 税点
ALTER TABLE `venue_contract_price_period`
    ADD COLUMN `invoice_type` VARCHAR(16) NULL DEFAULT '' COMMENT '发票类型(字典0044)',
    ADD COLUMN `tax_point`    VARCHAR(16)      NULL DEFAULT '' COMMENT '税点(字典0045)',
    ADD COLUMN `parent_id`    INT(11) UNSIGNED NULL DEFAULT '0' COMMENT '原价格周期id,如变更,补充协议,修改申请对应的原合同'
;
-- 付款周期增加发票类型 税点
ALTER TABLE `venue_contract_payment_period`
    ADD COLUMN `invoice_type` VARCHAR(16) NULL DEFAULT '' COMMENT '发票类型(字典0044)',
    ADD COLUMN `tax_point`    VARCHAR(16) NULL DEFAULT '' COMMENT '税点(字典0045)'
;
-- 项目表增加 来源id
ALTER TABLE `venue_contract_project`
    ADD COLUMN `parent_id` INT(11) UNSIGNED NULL DEFAULT '0' COMMENT '原项目id,如变更,补充协议,修改申请对应的原合同'
;

-- 价格申请表增加 来源id
ALTER TABLE `venue_contract_price_apply`
    ADD COLUMN `parent_id` INT(11) UNSIGNED NULL DEFAULT '0' COMMENT '原价格申请id,如变更,补充协议,修改申请对应的原合同'
;

-- 终端表增加 来源id
ALTER TABLE `venue_contract_device`
    ADD COLUMN `parent_id` INT(11) UNSIGNED NULL DEFAULT '0' COMMENT '原终端id,如变更,补充协议,修改申请对应的原合同'
;

-- 子合同表增加 来源id
ALTER TABLE `venue_contract_sub`
    ADD COLUMN `parent_id` INT(11) UNSIGNED NULL DEFAULT '0' COMMENT '原子合同id,如变更,补充协议,修改申请对应的原合同'
;

ALTER TABLE `cheese_cms`.`venue_contract_deposit_supplier`
    ADD COLUMN `parent_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '原项目id,如变更,补充协议,修改申请对应的原合同' AFTER `supplier_bank_id`;

-- 合同表增加 子合同是否盖章字段
ALTER TABLE `venue_contract`
    ADD COLUMN `sub_contract_seal_flag` TINYINT(1) UNSIGNED DEFAULT NULL COMMENT '子合同是否盖章 1 是 0 否';

-- 台账增加期初付款金额 期初回票金额  实际付款日期
ALTER TABLE venue_ledger
    ADD COLUMN initial_payment_amount DECIMAL(20, 2) DEFAULT '0.00' COMMENT '期初付款金额',
    ADD COLUMN initial_invoiced_amount DECIMAL(20, 2) DEFAULT '0.00' COMMENT '期初回票金额',
    ADD COLUMN actual_payment_date     date           DEFAULT NULL COMMENT '实际付款日期';

-- 评论表
CREATE TABLE `venue_comment`
(
    `id`           INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `create_time`  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`      INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_time`  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `operator`     INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `delete_flag`  TINYINT(1) UNSIGNED       DEFAULT '0' COMMENT '删除标识。0-否，1-是',
    `content`      varchar(500)      DEFAULT '' COMMENT '评论内容',
    `url`          varchar(200)      DEFAULT '' COMMENT '跳转链接',
    `url_text`     varchar(50)       DEFAULT '' COMMENT '飞书跳转链接的文本内容',
    `notify_users` varchar(32)       DEFAULT '' COMMENT '通知用户,多个',
    `biz_id`       INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '业务id',
    `biz_type`     INT(2) UNSIGNED  NOT NULL DEFAULT '0' COMMENT '业务类型 1 修改申请',
    `send_status`  TINYINT(1) UNSIGNED       DEFAULT '0' COMMENT '发送状态 0 待发送 1 已发送 2 发送失败',
    `send_result`  varchar(50)       DEFAULT '' COMMENT '发送结果',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='评论表';

INSERT INTO dict (parent_id, name, code, `rank`, status)
VALUES (761, '修改合同通过', '0092-8', 8, 1);
INSERT INTO dict (parent_id, name, code, `rank`, status)
VALUES (761, '数据覆盖后', '0092-9', 9, 1);


-- 审批code 业务Id 关联表
create table venue_inner_approval
(
    id            int(11) unsigned auto_increment comment '主键ID' primary key,
    biz_id        int(11) unsigned default 0                 not null comment '业务ID',
    type          varchar(10) default '0160-1'          not null comment '业务类型(字典0160)',
    instance_code varchar(32) default ''                not null comment '流程实例code',
    creator       int(11) unsigned default 0                 not null comment '创建人',
    create_time   datetime    default CURRENT_TIMESTAMP not null comment '创建时间'
) ENGINE = InnoDB comment '站内审批业务关联表';


-- 审批taskId 业务数据关联表（已办）
create table venue_inner_approval_task_finish
(
    id            int(11) unsigned auto_increment comment '主键ID' primary key,
    biz_id        int(11) unsigned        default 0                 not null comment '业务ID',
    task_id       int(11) unsigned        default 0                 not null comment '任务ID',
    type          varchar(10) default '0160-1'          not null comment '业务类型(字典0160)',
    apply_status  varchar(10) default ''                not null comment '申请状态',
    instance_code varchar(32) default ''                not null comment '流程实例code',
    contract_code varchar(32) default ''                not null comment '合同编号',
    apply_code    varchar(32) default ''                not null comment '合同修改申请编号',
    project_name  varchar(32) default ''                not null comment '项目名称，多个逗号分隔',
    supplier_name varchar(50) default ''                not null comment '供应商名称，多个逗号分隔',
    city_id       int(11) unsigned        default 0                 not null comment '城市ID',
    region_id     int(11) unsigned        default 0                 not null comment '大区ID',
    total_amount  decimal(12, 2) unsigned default '0.00'            not null comment '合同总金额',
    `period`      decimal(12, 2) unsigned default '0.00'            not null comment '年限',
    follower      int(11) unsigned        default 0                 not null comment '合同跟进人',
    creator       int(11) unsigned        default 0                 not null comment '创建人',
    create_time   datetime    default CURRENT_TIMESTAMP not null comment '创建时间'
) ENGINE = InnoDB comment '已办表';

-- 快照记录增加关联审批任务Id
alter table venue_contract_snapshot
    add instance_code varchar(32) default '' not null comment '流程实例code';