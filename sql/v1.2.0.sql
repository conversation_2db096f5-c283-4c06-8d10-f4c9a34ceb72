-- cheese_merchant
-- 楼宇/商机状态变更记录
CREATE TABLE `building_status_change_log`
(
    `id`            INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type`          VARCHAR(16)      NOT NULL DEFAULT '0042-5' COMMENT '数据类型 (字典0042)',
    `sub_type`      INT(11) UNSIGNED          DEFAULT '0' COMMENT '数据类型子类型',
    `biz_id`        INT(11) UNSIGNED          DEFAULT '0' COMMENT '业务ID (楼宇,客户,商机,...)',
    `biz_code`      VARCHAR(32)               DEFAULT '' COMMENT '业务编码 (楼宇,客户,商机,...)',
    `status`        VARCHAR(16)               DEFAULT '' COMMENT '业务状态(字典0043)',
    `change_time`   DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '状态变更时间',
    `operator`      INT(11) UNSIGNED          DEFAULT '0' COMMENT '状态变更操作人',
    `operator_wno`  VARCHAR(16)               DEFAULT '' COMMENT '状态变更操作人工号',
    `operator_name` VARCHAR(20)               DEFAULT '' COMMENT '状态变更操作人姓名',
    `content`       TEXT COMMENT '补充内容',
    `delete_flag`   TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `create_time`   DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`, `biz_id`) COMMENT '变更类型索引'
) ENGINE = InnoDB COMMENT ='楼宇/商机状态变更记录';


-- 物业公司联系人表
CREATE TABLE `property_company_person`
(
    `id`          INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `company_id`  INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '物业公司id',
    `role`        VARCHAR(16)      NOT NULL DEFAULT '' COMMENT '角色',
    `name`        VARCHAR(20)      NOT NULL DEFAULT '' COMMENT '姓名',
    `phone`       VARCHAR(100)     NOT NULL DEFAULT '' COMMENT '手机号码(加密存储)',
    `email`       VARCHAR(200)              DEFAULT '' COMMENT '电子邮箱(加密存储)',
    `create_by`   VARCHAR(10)      NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   VARCHAR(10)               DEFAULT '' COMMENT '操作人',
    `update_time` DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='物业公司联系人表';

-- 物业公司表
CREATE TABLE `property_company`
(
    `id`                         INT(11) UNSIGNED    NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type`                       TINYINT(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '物业公司类型：1企业、2个人',
    `name`                       VARCHAR(100)        NOT NULL DEFAULT '' COMMENT '物业公司名称',
    `unified_social_credit_code` VARCHAR(200)                 DEFAULT '' COMMENT '统一社会信用代码(加密存储)',
    `id_card`                    VARCHAR(200)                 DEFAULT '' COMMENT '身份证号(加密存储)',
    `phone`                      VARCHAR(100)                DEFAULT '' COMMENT '手机号码(加密存储)',
    `address`                    VARCHAR(500)                 DEFAULT '' COMMENT '物业地址(加密存储)',
    `status`                     TINYINT(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
    `create_by`                  VARCHAR(10)         NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`                DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`                  VARCHAR(10)                  DEFAULT '' COMMENT '操作人',
    `update_time`                DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='物业公司';

-- 楼宇物业公司关联表
CREATE TABLE `building_property_company`
(
    `id`            INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `building_no`   VARCHAR(50)      NOT NULL DEFAULT '' COMMENT '楼宇编码',
    `project_code`  VARCHAR(50)      NOT NULL DEFAULT '' COMMENT '项目编号',
    `project_name`  VARCHAR(100)     NOT NULL DEFAULT '' COMMENT '项目名称',
    `property_id`   INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '物业公司id',
    `property_name` VARCHAR(100)              DEFAULT NULL COMMENT '物业公司名称',
    `create_by`     VARCHAR(10)      NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`   DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`     VARCHAR(10)               DEFAULT '' COMMENT '操作人',
    `update_time`   DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='楼宇物业公司关联表';

-- 商机表
CREATE TABLE `business_opportunity`
(
    `id`              BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`            VARCHAR(100)        NOT NULL DEFAULT '' COMMENT '商机名称(基于收盘名称扩展)',
    `code`            VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '商机编码',
    `status`          VARCHAR(16)         NOT NULL DEFAULT '' COMMENT '商机状态',
    `customer_id`     VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '客户编码(来源于悟空CRM)',
    `submit_user`     VARCHAR(10)         NOT NULL DEFAULT '' COMMENT '提交人',
    `building_no`     VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '楼宇编码',
    `owner`           VARCHAR(10)         NOT NULL DEFAULT '' COMMENT '归属人',
    `crm_business_id` VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '悟空CRM商机编码',
    `create_by`       VARCHAR(10)         NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`     DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`       VARCHAR(10)                  DEFAULT '' COMMENT '操作人',
    `update_time`     DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='商机';

-- 客户跟进记录表
CREATE TABLE `customer_follow_record`
(
    `id`            BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `visit_time`    DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '拜访时间',
    `visit_type`    VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '拜访类型',
    `visit_objects` VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '拜访对象',
    `business_code` VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '商机',
    `visit_purpose` VARCHAR(200)        NOT NULL DEFAULT '' COMMENT '沟通目的',
    `visit_result`  VARCHAR(200)        NOT NULL DEFAULT '' COMMENT '沟通结果',
    `batch_id`      VARCHAR(40)         NOT NULL DEFAULT '' COMMENT '与crm关联id',
    `create_by`     VARCHAR(10)         NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`   DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`     VARCHAR(10)                  DEFAULT '' COMMENT '操作人',
    `update_time`   DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='客户跟进记录';


-- 楼宇新增字段
ALTER TABLE building_meta
    ADD COLUMN (
        floor_total_number INT(11) DEFAULT NULL COMMENT '最高楼层',
        building_total_number INT(11) DEFAULT NULL COMMENT '总楼栋数量',
        units_total_number INT(11) DEFAULT NULL COMMENT '总单元数量',
        hall_total_number INT(11) DEFAULT NULL COMMENT '总等候厅数',
        forbidden_industry VARCHAR(20) DEFAULT '' COMMENT '禁忌行业'
        );

-- 新增字典
INSERT INTO `dict` (  `parent_id`, `name`, `code`, `rank`, `status` )
VALUES
	(  0, '数据状态变更类型', '0042', 0, 1 ),
	(  0, '商机状态', '0043', 0, 1 ),
	(  0, '楼宇评级申请状态', '0046', 0, 1 ),
	(  406, '楼宇', '0042-1', 1, 1 ),
	(  406, '评级', '0042-2', 2, 1 ),
	(  406, '客户', '0042-3', 3, 1 ),
	(  406, '商机', '0042-4', 4, 1 ),
	(  406, '合同', '0042-5', 5, 1 ),

	(  412, '待洽谈', '0043-1', 1, 1),
	(  412, '初步洽谈', '0043-2', 2, 1 ),
	(  412, '达成意向', '0043-3', 3, 1 ),
	(  412, '方案报价', '0043-4', 4, 1 ),
	(  412, '合同流程', '0043-5', 5, 1 ),
	(  412, '成交', '0043-6', 6, 10 ),

	(  428, '待审核', '0046-1', 1, 1 ),
	(  428, '已通过', '0046-2', 2, 1 ),
	(  428, '不通过', '0046-3', 3, 1 ),
	(  428, '已驳回', '0046-4', 4, 1 ),
	(  428, '已放弃', '0046-5', 5, 1 );

INSERT INTO `dict` (`parent_id`, `name`, `code`, `rank`, `status`)
VALUES
(0, '物业联系人角色', '0047', 0, 1),
(434, '项目负责人', '0047-1', 1, 1),
(434, '签约负责人', '0047-2', 2, 1),
(434, '其他联系人', '0047-3', 3, 1);

-- 新增点位方案商机编码
ALTER TABLE `cheese_merchant`.`point_plan`
    ADD COLUMN `business_code` varchar(32) NOT NULL DEFAULT '' COMMENT '商机编码' AFTER `update_time`;

-- 新增价格申请商机编码
ALTER TABLE `cheese_merchant`.`price_apply`
    ADD COLUMN `business_code` varchar(32) NOT NULL DEFAULT '' COMMENT '商机编码' AFTER `update_time`;

-- 新增价点位商机编码
ALTER TABLE `cheese_merchant`.`point`
    ADD COLUMN `business_code` varchar(32) NOT NULL DEFAULT '' COMMENT '商机编码' AFTER `building_rating_no`;

-- 新增价点位商机编码
ALTER TABLE `cheese_merchant`.`point_plan`
    ADD COLUMN `business_code` varchar(32) NOT NULL DEFAULT '' COMMENT '商机编码' AFTER `update_time`;

-- 新增价点位商机编码
ALTER TABLE `cheese_merchant`.`point_price_snapshot`
    ADD COLUMN `business_code` varchar(32) NOT NULL DEFAULT '' COMMENT '商机编码' AFTER `floor_desc`;

-- 新增价点位商机编码
ALTER TABLE `cheese_merchant`.`point_contract_snapshot`
    ADD COLUMN `business_code` varchar(32) NOT NULL DEFAULT '' COMMENT '商机编码' AFTER `floor_desc`;




