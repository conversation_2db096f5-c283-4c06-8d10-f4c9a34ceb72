CREATE TABLE `core_area` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(10) NOT NULL DEFAULT '' COMMENT '核心区域类型（圆形、矩形、多边形）字典0171',
  `name` varchar(15) NOT NULL DEFAULT '' COMMENT '核心区名称',
  `name_area` text COMMENT '名称在地图上的区域',
  `city_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '所属城市id',
  `area` text COMMENT '核心区域范围json',
  `creator` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `operator` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新人id',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `delete_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记 [0:否, 1:是]',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='核心区域表';

CREATE TABLE `core_area_operate_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `operate_type` varchar(10) NOT NULL DEFAULT '' COMMENT '操作类型（新增、删除、编辑）字典0170',
  `content` text COMMENT '操作内容',
  `city_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '城市id',
  `creator` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT ='核心区域操作记录表';





-- 新增字典项
INSERT INTO `dict` (`name`, `code`, `rank`, `status`, `parent_code`) VALUES ('媒资-日志操作类型', '0170', 0, 1, '0');
INSERT INTO `dict` (`name`, `code`, `rank`, `status`, `parent_code`) VALUES ('新增', '0170-1', 1, 1, '0170');
INSERT INTO `dict` (`name`, `code`, `rank`, `status`, `parent_code`) VALUES ('编辑', '0170-2', 2, 1, '0170');
INSERT INTO `dict` (`name`, `code`, `rank`, `status`, `parent_code`) VALUES ('删除', '0170-3', 3, 1, '0170');
INSERT INTO `dict` (`name`, `code`, `rank`, `status`, `parent_code`) VALUES ('查询', '0170-4', 4, 0, '0170');
INSERT INTO `dict` (`name`, `code`, `rank`, `status`, `parent_code`) VALUES ('媒资-核心区域类型', '0171', 0, 1, '0');
INSERT INTO `dict` (`name`, `code`, `rank`, `status`, `parent_code`) VALUES ('圆形', '0171-1', 1, 1, '0171');
INSERT INTO `dict` (`name`, `code`, `rank`, `status`, `parent_code`) VALUES ('矩形', '0171-2', 2, 1, '0171');
INSERT INTO `dict` (`name`, `code`, `rank`, `status`, `parent_code`) VALUES ('多边形', '0171-3', 3, 1, '0171');
