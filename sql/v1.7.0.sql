ALTER TABLE `building_rating`
    ADD COLUMN `enter_sea_time` DATETIME DEFAULT NULL COMMENT '掉入公海时间' AFTER `enter_sea_calculate_time`,
    ADD COLUMN `enter_sea_reason` VARCHAR(10) DEFAULT NULL COMMENT '掉入公海原因：0132-1 员工离职，0132-2 无法完成签约，0132-3 联系不上客户，0132-4 客户暂无需求，0132-5 已与其他竞媒签约，0132-6 其他原因，系统触发' AFTER `enter_sea_time`;

CREATE TABLE `high_sea_record`
(
    `id`                 INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `building_no`        VARCHAR(20) NOT NULL DEFAULT '' COMMENT '客户编码',
    `building_name`      VARCHAR(50) NOT NULL DEFAULT '' COMMENT '客户名称',
    `operate_type`       TINYINT     NOT NULL DEFAULT 0 COMMENT '操作类型：0-转出，1-转入',
    `responsible_person` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '原负责人',
    `operator`           VARCHAR(10) NOT NULL DEFAULT '' COMMENT '操作人',
    `operate_time`       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `remark`             VARCHAR(10) NOT NULL DEFAULT '' COMMENT '备注：0132-1 员工离职，0132-2 无法完成签约，0132-3 联系不上客户，0132-4 客户暂无需求，0132-5 已与其他竞媒签约，0132-6 其他原因，系统触发',
    `create_by`          varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`          varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY                  `idx_building_no` (`building_no`)
) ENGINE = InnoDB COMMENT = '公海数据出入记录表';

-- 用户标签表
CREATE TABLE user_tag
(
    id            INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    tag_name      VARCHAR(20) NOT NULL default '' COMMENT '标签名称',
    user_count    INT         NOT NULL DEFAULT 0 COMMENT '用户数量',
    status        INT         NOT NULL DEFAULT 1 COMMENT '状态 0:禁用 1:启用',
    `create_by`   VARCHAR(20) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   VARCHAR(20) NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `delete_flag` tinyint(4)  NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
    INDEX         idx_tag_name (tag_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户标签表';

-- 用户标签详情表
CREATE TABLE `user_tag_relation`
(
    id          INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    emp_code    VARCHAR(10) not null default '' COMMENT '工号',
    user_tag_id INT         not null COMMENT '用户标签id',
    INDEX       idx_emp_code (emp_code),
    INDEX       idx_user_tag_id (user_tag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户标签映射表';