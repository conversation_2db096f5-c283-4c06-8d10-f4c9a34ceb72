-- meht--
CREATE TABLE `rating_attachment`
(
    `id`            int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `type`          tinyint(2) NOT NULL DEFAULT '0' COMMENT '附件归属类型',
    `business_type` varchar(20) NOT NULL DEFAULT '' COMMENT '业务子类型',
    `biz_id`        int(10) NOT NULL DEFAULT '0' COMMENT '业务id',
    `biz_code`      varchar(20)  NOT NULL DEFAULT '' COMMENT '业务编码',
    `url`           varchar(200) NOT NULL DEFAULT '' COMMENT '附件全路径',
    `create_by`     varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `update_by`     varchar(10)  NOT NULL DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='附件信息表';


CREATE TABLE `building_screen`
(
    `id`                      int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `building_rating_no`      varchar(20)  NOT NULL DEFAULT '' COMMENT '楼宇编号',
    `spec`                    varchar(100) NOT NULL DEFAULT '' COMMENT '规格',
    `total_building_count`    int(10) DEFAULT NULL COMMENT '总楼栋数量',
    `company_count`           int(10) DEFAULT NULL COMMENT '入驻企业数量',
    `elevator_count`          int(10) DEFAULT NULL COMMENT '电梯数量',
    `building_spacing`        decimal(5, 2)         DEFAULT NULL COMMENT '间距',
    `building_ceiling_height` decimal(5, 2)         DEFAULT NULL COMMENT '挑高',
    `submit_coefficient`      decimal(5, 2)         DEFAULT NULL COMMENT '提交系数',
    `final_coefficient`       decimal(5, 2)         DEFAULT NULL COMMENT '复核系数',
    `special_desc`            text COMMENT '特殊说明',
    `create_by`               varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `update_by`               varchar(10)  NOT NULL DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='楼宇大屏系数表';

CREATE TABLE `complete_building_screen`
(
    `id`                      int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `complete_rating_no`      varchar(20)  NOT NULL DEFAULT '' COMMENT '完善楼宇编号',
    `spec`                    varchar(100) NOT NULL DEFAULT '' COMMENT '规格',
    `total_building_count`    int(10) DEFAULT NULL COMMENT '总楼栋数量',
    `company_count`           int(10) DEFAULT NULL COMMENT '入驻企业数量',
    `elevator_count`          int(10) DEFAULT NULL COMMENT '电梯数量',
    `building_spacing`        decimal(5, 2)         DEFAULT NULL COMMENT '间距',
    `building_ceiling_height` decimal(5, 2)         DEFAULT NULL COMMENT '挑高',
    `submit_coefficient`      decimal(5, 2)         DEFAULT NULL COMMENT '提交系数',
    `final_coefficient`       decimal(5, 2)         DEFAULT NULL COMMENT '复核系数',
    `special_desc`            text COMMENT '特殊说明',
    `create_by`               varchar(10)  NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `update_by`               varchar(10)  NOT NULL DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='完善楼宇大屏系数表';

CREATE TABLE `complete_rating_detail`
(
    `id`                         int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `complete_rating_no`         varchar(20)   NOT NULL DEFAULT '' COMMENT '完善编码',
    `building_grade`             bigint(20) NOT NULL DEFAULT '0' COMMENT '等级id',
    `grade_name`                 varchar(50)   NOT NULL DEFAULT '' COMMENT '等级（选项文本）',
    `building_location`          bigint(20) NOT NULL DEFAULT '0' COMMENT '地理位置id',
    `location_name`              varchar(50)   NOT NULL DEFAULT '' COMMENT '地理位置（选项文本）',
    `building_number`            bigint(20) NOT NULL DEFAULT '0' COMMENT '楼层数id',
    `building_number_input`      varchar(200)  NOT NULL DEFAULT '' COMMENT '楼层数(输入)',
    `building_price`             bigint(20) NOT NULL DEFAULT '0' COMMENT '月租金id',
    `building_price_input`       varchar(200)  NOT NULL DEFAULT '' COMMENT '月租金(输入)',
    `daily_price_input`          decimal(8, 2) NOT NULL DEFAULT '0.00' COMMENT '日租金（输入）',
    `building_age`               bigint(20) NOT NULL DEFAULT '0' COMMENT '楼龄id',
    `building_age_input`         varchar(200)  NOT NULL DEFAULT '' COMMENT '楼龄(输入)',
    `delivery_date`              date                   DEFAULT NULL COMMENT '交付日期',
    `building_exterior`          bigint(20) NOT NULL DEFAULT '0' COMMENT '外观造型id',
    `exterior_name`              varchar(50)   NOT NULL DEFAULT '' COMMENT '外观造型（选项文本）',
    `building_lobby`             bigint(20) NOT NULL DEFAULT '0' COMMENT '楼盘大堂id',
    `lobby_name`                 varchar(50)   NOT NULL DEFAULT '' COMMENT '楼盘大堂（选项文本）',
    `building_garage`            bigint(20) NOT NULL DEFAULT '0' COMMENT '地下车库id',
    `garage_name`                varchar(50)   NOT NULL DEFAULT '' COMMENT '地下车库（选项文本）',
    `building_hall`              bigint(20) NOT NULL DEFAULT '0' COMMENT '侯梯厅id',
    `hall_name`                  varchar(50)   NOT NULL DEFAULT '' COMMENT '侯梯厅（选项文本）',
    `building_brand`             bigint(20) NOT NULL DEFAULT '0' COMMENT '综合体品牌id',
    `brand_name`                 varchar(50)   NOT NULL DEFAULT '' COMMENT '综合体品牌（选项文本）',
    `top_brand_id`               bigint(20) unsigned DEFAULT '0' COMMENT 'TOP100品牌名称id',
    `top_brand_name`             varchar(50)   NOT NULL DEFAULT '' COMMENT 'TOP100品牌名称',
    `building_rating`            bigint(20) NOT NULL DEFAULT '0' COMMENT '点评评分id',
    `building_settled`           bigint(20) NOT NULL DEFAULT '0' COMMENT '入驻率id',
    `third_building_grade`       varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方等级',
    `third_building_location`    varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方地理位置',
    `third_building_number`      varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方楼层数',
    `third_building_price`       varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方月租金',
    `third_daily_price`          varchar(20)   NOT NULL DEFAULT '' COMMENT '第三方日租金',
    `third_building_age`         varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方楼龄',
    `third_delivery_date`        varchar(20)   NOT NULL DEFAULT '' COMMENT '第三方交付日期',
    `third_building_exterior`    varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方外观造型',
    `third_building_lobby`       varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方楼盘大堂',
    `third_building_garage`      varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方地下车库',
    `third_building_hall`        varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方侯梯厅',
    `third_building_brand`       varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方综合体品牌',
    `third_building_rating`      varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方点评评分',
    `third_building_settled`     varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方入驻率',
    `create_by`                  varchar(10)   NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`                datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`                  varchar(10)   NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time`                datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `third_building_grade_id`    bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方等级分数ID',
    `third_building_location_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方地理位置分数ID',
    `third_building_number_id`   bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方楼层数分数ID',
    `third_building_price_id`    bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方月租金分数ID',
    `third_building_age_id`      bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方楼龄分数ID',
    `third_building_exterior_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方外观造型分数ID',
    `third_building_lobby_id`    bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方楼盘大堂分数ID',
    `third_building_garage_id`   bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方地下车库分数ID',
    `third_building_hall_id`     bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方侯梯厅分数ID',
    `third_building_brand_id`    bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方综合体品牌分数ID',
    `third_building_rating_id`   bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方点评评分分数ID',
    `third_building_settled_id`  bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方入驻率分数ID',
    `third_building_type`        varchar(50)   NOT NULL DEFAULT '' COMMENT '第三方楼宇类型',
    PRIMARY KEY (`id`),
    KEY                          `idx_ratingId` (`complete_rating_no`)
) ENGINE=InnoDB  COMMENT='完善楼宇选择表';

CREATE TABLE `complete_rating`
(
    `id`                        int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `building_rating_no`        varchar(20)   NOT NULL DEFAULT '' COMMENT '楼宇编号',
    `building_type`             tinyint(2) NOT NULL DEFAULT '0' COMMENT '楼宇类型 0写字楼 1商住楼 2综合体 3产业园区',
    `building_name`             varchar(50)   NOT NULL DEFAULT '' COMMENT '楼宇名称',
    `status`                    tinyint(1) NOT NULL DEFAULT 0 COMMENT '0待审核，1已审核 2已驳回 3审核不通过 4已放弃 5草稿',
    `large_screen_flag`         tinyint(1) NOT NULL DEFAULT '1' COMMENT '完善标识 1:完善小屏, 2:完善大屏, 3:完善大小屏',
    `complete_rating_no`        varchar(20)   NOT NULL DEFAULT '' COMMENT '完善编码',
    `project_level`             varchar(10)   NOT NULL DEFAULT '' COMMENT '等级评价',
    `project_review_level`      varchar(10)   NOT NULL DEFAULT '' COMMENT '等级评价',
    `building_score`            decimal(5, 2) NOT NULL DEFAULT '0.00' COMMENT '综合得分',
    `first_floor_exclusive`     decimal(5, 2) NOT NULL DEFAULT '0.00' COMMENT '一楼独享',
    `first_floor_share`         decimal(5, 2) NOT NULL DEFAULT '0.00' COMMENT '一楼共享',
    `negative_first_floor`      decimal(5, 2) NOT NULL DEFAULT '0.00' COMMENT '负一楼',
    `negative_second_floor`     decimal(5, 2) NOT NULL DEFAULT '0.00' COMMENT '负二楼',
    `second_floor_above`        decimal(5, 2) NOT NULL DEFAULT '0.00' COMMENT '二楼及以上',
    `third_floor_below`         decimal(5, 2) NOT NULL DEFAULT '0.00' COMMENT '负三楼及以下',
    `target_point_count`        int(10) NOT NULL DEFAULT '0' COMMENT '目标点位数',
    `project_ai_level`          varchar(10)   NOT NULL DEFAULT '' COMMENT 'AI等级评价',
    `building_ai_score`         decimal(5, 2) NOT NULL DEFAULT '0.00' COMMENT 'AI综合得分',
    `map_province`              varchar(50)   NOT NULL DEFAULT '' COMMENT '省名称',
    `map_city`                  varchar(50)   NOT NULL DEFAULT '' COMMENT '市名称',
    `map_region`                varchar(50)   NOT NULL DEFAULT '' COMMENT '区名称',
    `map_address`               varchar(800)  NOT NULL DEFAULT '' COMMENT '详细地址',
    `draft_text`                text COMMENT '草稿快照',
    `data_flag`                 tinyint(2) NOT NULL DEFAULT '0' COMMENT '楼宇参数版本标识',
    `submit_user`               varchar(10)   NOT NULL DEFAULT '' COMMENT '提交人工号',
    `version`                   varchar(20)   NOT NULL DEFAULT '' COMMENT '版本号',
    `building_exterior_pic`     varchar(50)   NOT NULL DEFAULT '' COMMENT '外墙材料附件地址',
    `building_lobby_pic`        varchar(200)  NOT NULL DEFAULT '' COMMENT '大堂高度及装饰附件地址',
    `building_elevator_pic`     varchar(100)  NOT NULL DEFAULT '' COMMENT '梯厅环境图附件地址',
    `building_gate_pic`         varchar(100)  NOT NULL DEFAULT '' COMMENT '闸口图附件地址',
    `building_installation_pic` varchar(100)  NOT NULL DEFAULT '' COMMENT '安装示意图附件地址',
    `building_hall_pic`         varchar(200)  NOT NULL DEFAULT '' COMMENT '楼梯厅装饰附件地址',
    `building_lobby_env_pic`    varchar(100)  NOT NULL DEFAULT '' COMMENT '大堂环境图附件地址',
    `submit_time`               datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_time`               datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`                 varchar(10)   NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time`               datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `update_by`                 varchar(10)   NOT NULL DEFAULT '' COMMENT '修改人',
    `delete_flag`               tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='完善楼宇申请信息表';


CREATE TABLE `operate_log`
(
    `id`           int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type`         tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '数据类型(1:楼宇)',
    `sub_type`     int(11) unsigned NOT NULL DEFAULT '0' COMMENT '数据类型子类型',
    `biz_id`       varchar(50) NOT NULL DEFAULT '0' COMMENT '业务ID',
    `description`  varchar(500)         DEFAULT '' COMMENT '描述说明',
    `operate_type` varchar(16) NOT NULL DEFAULT '' COMMENT '操作类型',
    `operate_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '操作时间',
    `content`      text COMMENT '补充内容',
    `creator`      int(11) unsigned NOT NULL DEFAULT '0' COMMENT '状态变更操作人',
    `create_time`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY            `idx_type` (`type`,`biz_id`) COMMENT '操作类型索引'
) ENGINE=InnoDB COMMENT='操作日志';

ALTER TABLE `building_rating`
    ADD COLUMN `rating_version` varchar(20) NOT NULL DEFAULT '0' COMMENT '评级版本' AFTER `enter_sea_reason`,
    ADD COLUMN `project_review_level` varchar(20) NOT NULL DEFAULT '' COMMENT '复核等级' AFTER `project_level`,
    ADD COLUMN `draft` text COMMENT '草稿' AFTER `rating_version`;

ALTER TABLE `price_apply`
    ADD COLUMN `rating_version` varchar(20) NOT NULL DEFAULT '0' COMMENT '评级版本' AFTER `large_screen_flag`,
    ADD COLUMN `project_level` varchar(20) NOT NULL DEFAULT '' COMMENT '等级评价' AFTER `rating_version`,
    ADD COLUMN `final_coefficient` decimal(5,2) DEFAULT NULL COMMENT '大屏复核系数' AFTER `project_level`,
    ADD COLUMN `small_water_mark_price` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '小屏水位价' AFTER `final_coefficient`,
    ADD COLUMN `big_water_mark_price` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '大屏水位价' AFTER `small_water_mark_price`,
    ADD COLUMN `draft` text COMMENT '草稿json，用于前端回显' AFTER `big_water_mark_price`,
    ADD COLUMN `map_province` varchar(32) NOT NULL DEFAULT '' COMMENT '省名称' AFTER `draft`,
    ADD COLUMN `map_city` varchar(32) NOT NULL DEFAULT '' COMMENT '市名称' AFTER `map_province`,
    ADD COLUMN `map_region` varchar(50) NOT NULL DEFAULT '' COMMENT '区名称' AFTER `map_city`,
    ADD COLUMN `map_address` varchar(800) NOT NULL DEFAULT '' COMMENT '加密后详细地址' AFTER `map_region`,
    ADD COLUMN `building_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区' AFTER `map_address`,
    ADD COLUMN `instance_code` varchar(32) NOT NULL DEFAULT '' COMMENT '审批实例编码' AFTER `building_type`,
    ADD COLUMN `version` varchar(20) NOT NULL DEFAULT '0' COMMENT '版本号' AFTER `instance_code`;

ALTER TABLE `building_snapshot`
    ADD COLUMN `type` TINYINT NOT NULL DEFAULT 0 COMMENT '快照类型：0-完善评级(老数据)，1-审核不通过快照，2-完善评级快照（1.7.8后新数据），3-评级编辑快照, 4-价格申请快照',
    ADD COLUMN `rating_version` VARCHAR ( 20 ) NOT NULL DEFAULT '0' COMMENT '评级版本',
    CHANGE COLUMN `gene_snapshot` `screen_snapshot` TEXT COMMENT '大屏快照json';

ALTER TABLE `building_details`
    CHANGE COLUMN `building_brand_name` `top_brand_name` varchar (50) NOT NULL DEFAULT '' COMMENT 'TOP100品牌名称',
    CHANGE COLUMN `building_brand_id` `top_brand_id` bigint(20) unsigned DEFAULT '0' COMMENT 'TOP100品牌名称',
    ADD COLUMN `daily_price_input` decimal (8,2) NOT NULL DEFAULT '0.00' COMMENT '日租金（输入）' AFTER `building_price_input`,
    ADD COLUMN `third_daily_price` varchar (20) NOT NULL DEFAULT '' COMMENT '第三方日租金' AFTER `third_building_price`,
    ADD COLUMN `delivery_date` date DEFAULT NULL COMMENT '交付日期' AFTER `building_age_input`,
    ADD COLUMN `third_delivery_date` varchar (20) NOT NULL DEFAULT '' COMMENT '第三方交付日期' AFTER `third_building_age`,
    ADD COLUMN `grade_name` varchar (50) NOT NULL DEFAULT '' COMMENT '等级（选项文本）' AFTER `building_grade`,
    ADD COLUMN `location_name` varchar (50) NOT NULL DEFAULT '' COMMENT '地理位置（选项文本）' AFTER `building_location`,
    ADD COLUMN `exterior_name` varchar (50) NOT NULL DEFAULT '' COMMENT '外观造型（选项文本）' AFTER `building_exterior`,
    ADD COLUMN `lobby_name` varchar (50) NOT NULL DEFAULT '' COMMENT '楼盘大堂（选项文本）' AFTER `building_lobby`,
    ADD COLUMN `garage_name` varchar (50) NOT NULL DEFAULT '' COMMENT '地下车库（选项文本）' AFTER `building_garage`,
    ADD COLUMN `hall_name` varchar (50) NOT NULL DEFAULT '' COMMENT '侯梯厅（选项文本）' AFTER `building_hall`,
    ADD COLUMN `rating_name` varchar (50) NOT NULL DEFAULT '' COMMENT '点评评分（选项文本）' AFTER `building_rating`,
    ADD COLUMN `settled_name` varchar (50) NOT NULL DEFAULT '' COMMENT '入驻率（选项文本）' AFTER `building_settled`,
    ADD COLUMN `brand_name` varchar (50) NOT NULL DEFAULT '' COMMENT '综合体品牌（选项文本）' AFTER `building_brand`;

ALTER TABLE `building_gene`
    ADD COLUMN `daily_price` decimal(8, 2) DEFAULT NULL COMMENT '日租金' AFTER `monthly_avg_price`,
    ADD COLUMN `delivery_date` date DEFAULT NULL COMMENT '交付日期' AFTER `building_age`,
    ADD COLUMN `property_fee` decimal(11,2) DEFAULT NULL COMMENT '物业费' AFTER `delete_flag`,
    ADD COLUMN `min_floor_count` int DEFAULT NULL COMMENT '最低楼层数' AFTER `property_fee`,
    ADD COLUMN `coverage_count` int DEFAULT NULL COMMENT '覆盖人数' AFTER `min_floor_count`;

ALTER TABLE `city_rent`
    ADD COLUMN `office_rent_daily` decimal(8, 2) NOT NULL DEFAULT '0.00' COMMENT '写字楼日租金' AFTER `office_rent`,
    ADD COLUMN `resid_rent_daily` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '商住楼日租金' AFTER `resid_rent`;

ALTER TABLE `building_city_rent`
    ADD COLUMN `office_rent_daily` decimal(8, 2) NOT NULL DEFAULT '0.00' COMMENT '日租金' AFTER `office_rent`;



ALTER TABLE `screen_approve_record`
    ADD COLUMN `rule_code` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '审批中心规则编号' AFTER `operate_type`,
    ADD COLUMN `version` VARCHAR (20) NOT NULL DEFAULT '0' COMMENT '版本号' AFTER `rule_code`,
    ADD COLUMN `instance_code` VARCHAR (32) NOT NULL DEFAULT '' COMMENT '审批实例编码' AFTER `version`,
    ADD COLUMN `node_id` INT (11) NOT NULL DEFAULT 0 COMMENT '审批中心节点ID' AFTER `instance_code`,
    ADD COLUMN `approve_user_id` INT (11) NOT NULL DEFAULT 0 COMMENT '审核人id' AFTER `node_id`,
    ADD COLUMN `approval_flag` TINYINT (1) NOT NULL DEFAULT 1 COMMENT '是否为审批节点，0：提交人节点，1：审批节点，2：结束节点' AFTER `approve_user_id`,
    ADD COLUMN `approval_result` VARCHAR (10) DEFAULT NULL COMMENT '审批结果，字典0138' AFTER `approval_flag`,
    ADD COLUMN `node_status` VARCHAR(10) DEFAULT NULL COMMENT '任务状态（字典0139）' AFTER `approval_result`,
    ADD COLUMN `submit_time` DATETIME  DEFAULT NULL COMMENT '节点提交时间' AFTER `node_status`,
    ADD COLUMN `cancel_reason` VARCHAR(10) DEFAULT NULL COMMENT '取消原因（字典0140）' AFTER `submit_time`,
    ADD COLUMN `approve_type` varchar(10)  NOT NULL DEFAULT '0' COMMENT '业务类型字典0164' AFTER `cancel_reason`,
    ADD INDEX `idx_instance_code`(`instance_code`) USING BTREE,
    ADD INDEX `idx_version`(`version`) USING BTREE;

CREATE TABLE `screen_approval_instance`
(
    `id`             INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `instance_code`  VARCHAR(32) NOT NULL DEFAULT '' COMMENT '审批中心实例编码',
    `rule_code`      VARCHAR(20) NOT NULL DEFAULT '' COMMENT '规则编码',
    `version`        VARCHAR(20) NOT NULL DEFAULT '0' COMMENT '规则编码',
    `biz_code`       VARCHAR(20) NOT NULL DEFAULT '' COMMENT '业务主键',
    `approve_type`   VARCHAR(10) NOT NULL DEFAULT '0' COMMENT '业务类型字典0164',
    `form_data`      text                 DEFAULT NULL COMMENT '审批表单数据,json数据',
    `approve_status` VARCHAR(20) DEFAULT NULL COMMENT '审批状态',
    `approve_result` VARCHAR(20) DEFAULT NULL   COMMENT '审批结果',
    `submit_user`    VARCHAR(10) NOT NULL DEFAULT '' COMMENT '提交人工号',
    `submit_time`    DATETIME    DEFAULT NULL COMMENT '审批开始时间',
    `create_by`      VARCHAR(10) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`      VARCHAR(10) NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `delete_flag`    TINYINT (1) NOT NULL DEFAULT 0 COMMENT '是否删除: 0否,1是',
    PRIMARY KEY (`id`),
    KEY              `idx_biz_code` (`biz_code`),
    KEY              `idx_instance_code` (`instance_code`),
    KEY              `idx_rule_code` (`rule_code`),
    KEY              `idx_submit_user` (`submit_user`)
) ENGINE = InnoDB COMMENT = '审批实例表';
