package com.coocaa.ad.cheese.cms.venue.validation.contract;

import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.common.util.BigDecimalUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.operatelog.ChangeExtractor;
import com.coocaa.ad.cheese.cms.common.tools.common.util.operatelog.ExpandChangedItem;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAmendmentApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectDepositPaidDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/11
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AmendmentProjectDepositEditableValidator implements ApplyValidator {

    private final IContractProjectService projectService;
    private final ContractConvert contractConvert;

    @Override
    public boolean support(ContractApplyParam applyParam, boolean submit) {
        return CollectionUtils.isNotEmpty(applyParam.getProjects())
                && BigDecimalUtils.gt(applyParam.getTotalAmount(), BigDecimal.ZERO)
                && (applyParam.getClass() == ContractAmendmentApplyParam.class);
    }

    @Override
    public List<String> validate(ContractApplyParam applyParam, boolean submit, boolean shortCircuit) {

        if (Objects.isNull(applyParam) || CollectionUtils.isEmpty(applyParam.getProjects())) {
            return List.of("项目列表为空");
        }
        ContractAmendmentApplyParam contractAmendmentApplyParam = (ContractAmendmentApplyParam) applyParam;
        //找出原合同下 有已付押金的项目
        List<ContractProjectDepositPaidDTO> contractProjectDepositPaidDTOs = projectService
                .selectProjectDepositPaidInfo(contractAmendmentApplyParam.getParentId());
        if (CollectionUtils.isEmpty(contractProjectDepositPaidDTOs)) {
            return Collections.emptyList();
        }
        Map<String, ContractProjectDepositPaidDTO> projectNamesMap = contractProjectDepositPaidDTOs
                .stream().collect(Collectors.toMap(ContractProjectDepositPaidDTO::getProjectName,
                        contractProjectDepositPaidDTO -> contractProjectDepositPaidDTO, (k1, k2) -> k2));
        List<String> errorProjectNames = Lists.newArrayList();
        int paidCount = 0;
        for (ContractProjectParam contractProjectParam : contractAmendmentApplyParam.getProjects()) {
            //查找押金是否付款
            if (!projectNamesMap.keySet().contains(contractProjectParam.getProjectName())) {
                continue;
            }
            paidCount++;
            ContractProjectDepositPaidDTO contractProjectDepositPaidDTO = contractConvert.toContractProjectDepositPaidDTO(contractProjectParam);
            List<ExpandChangedItem> extract = ChangeExtractor.extract(contractProjectDepositPaidDTO,
                    projectNamesMap.get(contractProjectParam.getProjectName()),
                    new ContractProjectDepositPaidDTO(), false);
            if (CollectionUtils.isNotEmpty(extract)) {
                errorProjectNames.add(contractProjectParam.getProjectName());
            }
        }
        if (CollectionUtils.isNotEmpty(errorProjectNames)) {
            return List.of(String.join(",", errorProjectNames).concat("->押金已付款,不可编辑"));
        }
        if (paidCount != projectNamesMap.keySet().size()) {
            return List.of("不允许删除已付押金的项目");
        }
        return List.of();
    }
}
