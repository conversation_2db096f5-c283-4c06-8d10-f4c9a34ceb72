package com.coocaa.ad.cheese.cms.dataimport.util;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.AgentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAbnormalDealEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAbnormalEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAbnormalProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDeviceEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPriceApplyEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractAbnormalDataBoardMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IAgentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAbnormalDealService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAbnormalService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDeviceService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPriceApplyService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPricePeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.IContractAbnormalProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierService;
import com.coocaa.ad.cheese.cms.common.rpc.FeignAuthorityRpc;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.UserVO;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AbContractDealTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractAbnormalFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.DealTypeEnum;
import com.coocaa.ad.cheese.cms.dataimport.convert.ContractAbnormalImportConvert;
import com.coocaa.ad.cheese.cms.dataimport.pojo.BuildingNoVo;
import com.coocaa.ad.cheese.cms.dataimport.pojo.BuildingNoVoRet;
import com.coocaa.ad.cheese.cms.dataimport.pojo.ChangeOwnerVo;
import com.coocaa.ad.cheese.cms.dataimport.pojo.ContractAbnormalParam;
import com.coocaa.ad.cheese.cms.dataimport.pojo.ContractExcelFeishuParam;
import com.coocaa.ad.cheese.cms.dataimport.pojo.ContractExcelOtherParam;
import com.coocaa.ad.cheese.cms.dataimport.pojo.ContractImportParam;
import com.coocaa.ad.cheese.cms.dataimport.pojo.ContractProjectImportParam;
import com.coocaa.ad.cheese.cms.dataimport.pojo.ContractUserVo;
import com.coocaa.ad.cheese.cms.dataimport.pojo.CrmProjectParam;
import com.coocaa.ad.cheese.cms.dataimport.pojo.DistrictVo;
import com.coocaa.ad.cheese.cms.dataimport.pojo.DistrictVoRet;
import com.coocaa.ad.cheese.cms.dataimport.pojo.FloorPoint;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PayCycleInfo;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PointData;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PointDataVO;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PointInfoVo;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PointPlanVo;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PriceApplyDevicePointVO;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PriceApplyDeviceVO;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PriceApplyVO;
import com.coocaa.ad.cheese.cms.dataimport.service.FeignCrmClient;
import com.coocaa.ad.cheese.cms.dataimport.service.FeignSspClient;
import com.coocaa.ad.cheese.cms.dataimport.service.FeignSysClient;
import com.coocaa.ad.cheese.cms.dataimport.service.HalfYearPayPeriodService;
import com.coocaa.ad.cheese.cms.dataimport.service.OncePayPeriodService;
import com.coocaa.ad.cheese.cms.dataimport.service.PayPeriodService;
import com.coocaa.ad.cheese.cms.dataimport.service.QuarterPayPeriodService;
import com.coocaa.ad.cheese.cms.dataimport.service.YearPayPeriodService;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractAbnormalProjectConvert;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignSspRpc;
import com.coocaa.ad.cheese.cms.venue.service.ContractNotifyService;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalProjectWithPointVO;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.ad.common.util.BigDecimalUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.coocaa.ad.cheese.cms.venue.service.ContractWriteService.DATE_FORMATTER;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-07
 */
@Slf4j
@Component
@RefreshScope
public class ParseContractExcel {
    @Autowired
    private IContractService contractService;

    @Autowired
    private ISupplierService supplierService;

    @Autowired
    private FeignSspClient feignSspClient;

    @Autowired
    private FeignSysClient feignSysClient;

    @Autowired
    private FeignCrmClient feignCrmClient;

    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    @Autowired
    private IContractSupplierService iContractSupplierService;

    @Autowired
    private IContractProjectService projectService;

    @Autowired
    private IContractPriceApplyService priceApplyService;

    @Autowired
    private IContractDeviceService deviceService;

    @Autowired
    private IContractDevicePointService pointService;

    @Autowired
    private IContractPaymentPeriodService paymentPeriodService;

    @Autowired
    private IContractPricePeriodService pricePeriodService;

    @Autowired
    private ISupplierBankService supplierBankService;
    @Autowired
    private IContractSupplierBankService contractSupplierBankService;

    private List<ContractImportParam> contractImportParams;

    private List<ContractImportParam> invalidList;

    private List<ContractImportParam> invalidPointList;

    private List<ContractImportParam> otherError;

    @Autowired
    private IContractAbnormalService contractAbnormalService;
    @Autowired
    private IContractAbnormalProjectService contractAbnormalProjectService;
    @Autowired
    private IContractAbnormalDealService contractAbnormalDealService;

    @Autowired
    private ContractAbnormalImportConvert contractAbnormalImportConvert;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ContractNotifyService contractNotifyService;

    @Value("${dataimport.cityToUse}")
    private String cityToUse;

    @Autowired
    private IAgentService agentService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ContractAbnormalDataBoardMapper dataBoardMapper;

    @Autowired
    private  FeignSspRpc feignSspRpc;

    @Autowired
    private  IContractProjectService contractProjectService;

    @Autowired
    private  ContractAbnormalProjectConvert contractAbnormalProjectConvert;

    @Autowired
    private  IContractDevicePointService contractDevicePointService;
    /**
     * 保存物业类型
     * key 中文名 写字楼
     * value 编码 0002-1
     */
    private static Map<String, String> propertyTypeMap = Maps.newHashMap();

    static {
        propertyTypeMap.put("写字楼", "0002-1");
        propertyTypeMap.put("综合体", "0002-2");
        propertyTypeMap.put("商住楼", "0002-3");
        propertyTypeMap.put("住宅", "0002-4");
        propertyTypeMap.put("产业园区", "0002-5");
    }



    /**
     * 保存支付类型
     * key 中文名 季度付
     * value 编码 0027-1
     */
    private static Map<String, String> payTypeMap = Maps.newHashMap();

    static {
        payTypeMap.put("季度付", "0027-1");
        payTypeMap.put("季付", "0027-1");
        payTypeMap.put("半年付", "0027-2");
        payTypeMap.put("一年付", "0027-3");
        payTypeMap.put("一次性", "0027-4");
        payTypeMap.put("其他", "0027-5");
        payTypeMap.put("其他（文字说明）", "0027-5");
        payTypeMap.put("年付", "0027-3");
    }

    public static Map<String, PayPeriodService> payTypeHandleMap = Maps.newHashMap();

    static {
        payTypeHandleMap.put("季度付", new QuarterPayPeriodService());
        payTypeHandleMap.put("季付", new QuarterPayPeriodService());
        payTypeHandleMap.put("半年付", new HalfYearPayPeriodService());
        payTypeHandleMap.put("一年付", new YearPayPeriodService());
        payTypeHandleMap.put("一次性", new OncePayPeriodService());
        payTypeHandleMap.put("年付", new YearPayPeriodService());
    }

    static {
        payTypeHandleMap.put("0027-1", new QuarterPayPeriodService());
        payTypeHandleMap.put("0027-2", new HalfYearPayPeriodService());
        payTypeHandleMap.put("0027-3", new YearPayPeriodService());
        payTypeHandleMap.put("0027-4", new OncePayPeriodService());
    }

    private static Map<String, String> payTypeMapCn = Maps.newHashMap();

    static {
        payTypeMapCn.put("季付", "季度付");
        payTypeMapCn.put("年付", "一年付");
        payTypeMapCn.put("其他（文字说明）", "其他");
    }


    // 城市缓存 名称和id对应关系
    private static final Cache<String, Integer> CITY_CACHE = Caffeine.newBuilder()
            .expireAfterAccess(10, TimeUnit.DAYS)
            .maximumSize(20000)
            .build();

    /**
     * BuildingNo 缓存
     * key 城市名称+区域名称+项目名称
     * value BuildingNo
     */
    private static final Cache<String, String> BUILDING_NO_CACHE = Caffeine.newBuilder()
            .expireAfterAccess(10, TimeUnit.DAYS)
            .maximumSize(20000)
            .build();

    /**
     * 点位缓存
     * key BuildingNo
     * value List<点位数据>
     */
    private static final Cache<String, List<PointInfoVo>> POINT_CACHE = Caffeine.newBuilder()
            .expireAfterAccess(10, TimeUnit.DAYS)
            .maximumSize(20000)
            .build();
    /**
     * 点位缓存
     * key BuildingNo
     * value 商机编码
     */
    private static final Cache<String, String> BUSINESS_CODE_CACHE = Caffeine.newBuilder()
            .expireAfterAccess(10, TimeUnit.DAYS)
            .maximumSize(20000)
            .build();

    private Map<String, String> contractUserMap = Maps.newHashMap();

    private Map<String, UserVO> userNameUserMap = Maps.newHashMap();

    private List<Object> changeOwnerError;

    /**
     * 根据项目:,城市:，区域:,未找到buildingNo的数据
     */
    private List<String> noBuildingNo;

    private List<ContractImportParam> noBuildingNoParam;

    private List<ContractImportParam> buildingNoMuti;

    private List<String> pointNotMatch;

    private Set<String> userSearchError;

    private List<String> noBusinessCode;

    @Autowired
    private ContractMapper contractMapper;

    List<String> noProjectContract;

    /**
     * 获取未导入的
     *
     * @return
     */
    public List<ContractImportParam> getInvalid() {
        return invalidList;
    }


    /**
     * 获取可以导入的
     *
     * @return
     */
    public List<ContractImportParam> getValid() {
        return contractImportParams;
    }


    /**
     * 以飞书为主 飞书没有的 other有 丢弃
     * 飞书有 other有 使用other的字段覆盖飞书
     *
     * @param fileFeishu
     * @param fileOther
     */

    public void importContract(MultipartFile fileFeishu, MultipartFile fileOther, MultipartFile fileContractUser) throws IOException {
//
        noBuildingNo = new ArrayList<>();
        pointNotMatch = new ArrayList<>();
        userSearchError = new HashSet<>();
        changeOwnerError = Lists.newArrayList();
        contractImportParams = Lists.newArrayList();
        invalidList = Lists.newArrayList();
        invalidPointList = Lists.newArrayList();
        noBuildingNoParam = Lists.newArrayList();
        buildingNoMuti = Lists.newArrayList();
        //星光的台账excel
        List<ContractExcelOtherParam> contractExcelOtherParams = dealOtherExcel(fileOther);
        //飞书的excel处理 key为cccode
        Map<String, ContractImportParam> mapFeishu = dealFeishuExcel(fileFeishu);
        //合同和申请人映射
        contractUserMap = dealContractUserExcel(fileContractUser);
        //处理信息合并
        for (ContractExcelOtherParam contractExcelOtherParam : contractExcelOtherParams) {
            try {
                String ccCode = contractExcelOtherParam.getContractCodeCC();
                if (mapFeishu.containsKey(ccCode)) {
                    ContractImportParam contractImportParam = mapFeishu.get(ccCode);
                    //使用 contractExcelOtherParam 填充一些字段
                    contractImportParam.setSupplierName(contractExcelOtherParam.getSupplierName());
                    contractImportParam.setYgd(contractExcelOtherParam.getYgd());
                    contractImportParam.setGdTime(contractExcelOtherParam.getGdDate());
                    contractImportParam.setHtqyYear(contractExcelOtherParam.getHtqyYear());
                    contractImportParam.setDevUserName(contractExcelOtherParam.getDevUserName());
                    contractImportParam.setBuzLeader(contractExcelOtherParam.getBuzLeader());
                    StringBuilder sb = new StringBuilder();
                    if (StringUtils.isNotBlank(contractImportParam.getNote())) {
                        sb.append(contractImportParam.getNote());
                    }
                    if (StringUtils.isNotBlank(contractExcelOtherParam.getNote())) {
                        sb.append("----").append(contractExcelOtherParam.getNote());
                    }
                    contractImportParam.setNote(sb.toString());
                    contractImportParam.setDevTypeName(contractExcelOtherParam.getDevTypeName());
                    contractImportParam.setContractCode(contractExcelOtherParam.getContractCode());
                    contractImportParam.setSupplierName(contractExcelOtherParam.getSupplierName());

                    List<ContractProjectImportParam> projects = contractImportParam.getProjects();
//                    if(projects.size()==1){
//                        projects.get(0).setProjectName(contractExcelOtherParam.getProjectName());
//                    }
                    boolean find = false;
                    for (ContractProjectImportParam project : projects) {
                        if (contractExcelOtherParam.getProjectName().equals(project.getProjectName())) {
                            find = true;
                            project.setCityProjectName(contractExcelOtherParam.getCityProjectName());
                            project.setPointNum(Integer.valueOf(contractExcelOtherParam.getPointNum()));
                            project.setAmount(contractExcelOtherParam.getContractTotalAmount());
                            project.setProjectName(contractExcelOtherParam.getProjectName());
                            break;

                        }
                    }
                    if (!find) {
                        ContractProjectImportParam project = new ContractProjectImportParam();
                        project.setProjectName(contractExcelOtherParam.getProjectName());
                        project.setCityProjectName(contractExcelOtherParam.getCityProjectName());
                        project.setPointNum(Integer.valueOf(contractExcelOtherParam.getPointNum()));
                        project.setAmount(contractExcelOtherParam.getContractTotalAmount());
                        project.setCity("省-" + contractExcelOtherParam.getCityName() + "-");
                        projects.add(project);
//                    BigDecimal amountContract = BigDecimal.valueOf(Double.valueOf(contractImportParam.getContractTotalAmount()))
//                            .add(BigDecimal.valueOf(Double.valueOf(project.getAmount())));
                        BigDecimal amountContract = contractImportParam.getContractTotalAmount()
                                .add(project.getAmount());
                        contractImportParam.setContractTotalAmount(amountContract);
                    }
                    mapFeishu.put(ccCode, contractImportParam);
                }
                //飞书没有 这一块处理不了 约定的是不在导入中处理
                else {
                    //使用ContractExcelOtherParam 构造新的 ContractImportParam 并设置到map
                    ContractImportParam contractImportParam = new ContractImportParam();
                    contractImportParam.setDevTypeName(contractExcelOtherParam.getDevTypeName());
                    contractImportParam.setContractCodeCC(contractExcelOtherParam.getContractCodeCC());
                    contractImportParam.setContractCode(contractExcelOtherParam.getContractCode());
                    contractImportParam.setSupplierName(contractExcelOtherParam.getSupplierName());
                    contractImportParam.setYgd(contractExcelOtherParam.getYgd());
                    contractImportParam.setGdTime(contractExcelOtherParam.getGdDate());
                    contractImportParam.setHtqyYear(contractExcelOtherParam.getHtqyYear());
                    contractImportParam.setDevUserName(contractExcelOtherParam.getDevUserName());
                    contractImportParam.setBuzLeader(contractExcelOtherParam.getBuzLeader());
                    if (contractUserMap.containsKey(contractExcelOtherParam.getContractCodeCC())) {
                        contractImportParam.setApplyUserName(contractUserMap.get(contractExcelOtherParam.getContractCodeCC()));
                    } else {
                        contractImportParam.setApplyUserName(contractExcelOtherParam.getDevUserName());
                    }
                    String[] strs = contractExcelOtherParam.getFqDate().split("/");
                    StringBuilder sb = new StringBuilder();
                    sb.append(strs[0]).append("-");
                    if (strs[1].length() == 1) {
                        sb.append("0");
                    }
                    sb.append(strs[1]).append("-");
                    if (strs[2].length() == 1) {
                        sb.append("0");
                    }
                    sb.append(strs[2]);
                    sb.append(" 00:00:00");
                    contractImportParam.setApplyTime(sb.toString());
                    contractImportParam.setContractTotalAmount(contractExcelOtherParam.getContractTotalAmount());
                    //设置项目
                    List<ContractProjectImportParam> projects = Lists.newArrayList();
                    ContractProjectImportParam project = new ContractProjectImportParam();
                    project.setProjectName(contractExcelOtherParam.getProjectName());
                    project.setCityProjectName(contractExcelOtherParam.getCityProjectName());
                    project.setPointNum(Integer.valueOf(contractExcelOtherParam.getPointNum()));
                    project.setAmount(contractExcelOtherParam.getContractTotalAmount());
                    project.setCity("省-" + (contractExcelOtherParam.getCityName().contains("市") ? contractExcelOtherParam.getCityName()
                            : contractExcelOtherParam.getCityName() + "市") + "-");
                    project.setPrice(project.getAmount().divide(BigDecimal.valueOf(project.getPointNum()), 2, RoundingMode.HALF_UP).
                            divide(contractExcelOtherParam.getHtqyYear(), 2, RoundingMode.HALF_UP));
                    projects.add(project);
                    contractImportParam.setProjects(projects);
                    mapFeishu.put(ccCode, contractImportParam);
                }
            } catch (Exception e) {
                log.error("dataimport contractExcelOtherParam异常:{}", contractExcelOtherParam.getContractCodeCC());
            }

        }
        contractImportParams.addAll(mapFeishu.values());
        for (ContractImportParam contractImportParam : mapFeishu.values()) {
            //根据项目名称+城市+区域 查询 buildingRatingNo->根据buildingRatingNo查询点位
            // ->比对这个点位和excel的点位数量是否一致 一致则正常处理，
            // 不一致则需要记录人工介入 || !ccCodeSetOther.contains(contractImportParam.getContractCodeCC())
            if (!checkPoint(contractImportParam)
                    || contractImportParam.getContractName().contains("补充协议")
                    || contractImportParam.getContractName().contains("终止协议")
                    || contractImportParam.getContractName().contains("变更")
                    || (contractImportParam.getNote() != null && contractImportParam.getNote().contains("系统一个项目，台账多条记录"))
                    || contractImportParam.getHtqyYear() == null
            ) {
                invalidList.add(contractImportParam);
                contractImportParams.remove(contractImportParam);
                continue;
            }
            //调用crm
            invokeCrmBusiness(contractImportParam);
        }
        log.error("dataimport 根据项目名称+城市+区域 未查询到 buildingRatingNo的数据:大小: {},数据:{}", noBuildingNo.size(), JSON.toJSONString(noBuildingNo));
        log.error("dataimport changeowner异常 ,大小:{},参数:{}", changeOwnerError.size(), JSON.toJSONString(changeOwnerError));
        log.error("dataimport 点位数据异常: {}", JSON.toJSONString(pointNotMatch));
        log.error("dataimport 用户查询异常: {}", JSON.toJSONString(userSearchError));
        log.error("dataimport buildingno多个: {}", JSON.toJSONString(buildingNoMuti));
        log.error("dataimport buildingno没找到: {}", JSON.toJSONString(noBuildingNoParam));
        if (invalidList != null && invalidList.size() > 0) {
            //异常数据排除 点位 以及 buildingno
            invalidList.removeAll(invalidPointList);
            invalidList.removeAll(noBuildingNoParam);
            invalidList.removeAll(buildingNoMuti);
        }
        log.error("dataimport 其他异常的数据: {}", JSON.toJSONString(invalidList));
        log.error("dataimport 正常的数据: {}", JSON.toJSONString(contractImportParams));
    }

    /**
     * 处理合同人员映射
     *
     * @param fileContractUser
     * @return
     */
    private Map<String, String> dealContractUserExcel(MultipartFile fileContractUser) throws IOException {
        Map<String, String> map = Maps.newHashMap();
        List<ContractUserVo> contractUserVos = parseRowContractUser(fileContractUser.getInputStream());
        map = contractUserVos.stream().collect(Collectors.toMap(contractUserVo -> contractUserVo.getContractCodeCC(), contractUserVo -> contractUserVo.getUserName()
                , (existingValue, newValue) -> existingValue));
        return map;
    }

    private List<ContractUserVo> parseRowContractUser(InputStream inputStream) {
        List<ContractUserVo> contractUserVos = Lists.newArrayList();
        EasyExcel.read(inputStream, ContractUserVo.class, new AnalysisEventListener<ContractUserVo>() {
            private int rowIndex = 0;

            @Override
            public void invoke(ContractUserVo data, AnalysisContext context) {
                rowIndex++;
                contractUserVos.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("dataimport Sheet 1 读取完成，总行数：{}", rowIndex);
            }
        }).sheet(0).doRead();
        return contractUserVos;
    }


    /**
     * 处理本地合同
     *
     * @param contractImportParam
     */
    private Integer dealLocalContract(ContractImportParam contractImportParam) {
        try {
            //处理合同基本信息
            log.info("开始处理合同基本信息 ccode,{}", contractImportParam.getContractCodeCC());
            ContractEntity contractEntity = null;
            if (noProjectContract.contains(contractImportParam.getContractCode())) {
                QueryWrapper queryWrapper = new QueryWrapper<>().eq("contract_code", contractImportParam.getContractCode())
                        .eq("contract_type", ContractTypeEnum.HISTORY.getCode());
                contractEntity = contractService.getOne(queryWrapper);
            } else {
                contractEntity = dealLocalContractBase(contractImportParam);
            }
            log.info("结束处理合同基本信息 ccode,{}", contractImportParam.getContractCodeCC());

            //处理合同代理商
            log.info("开始处理合同代理商 ccode,{}", contractImportParam.getContractCodeCC());
            dealLocalContractProxy(contractImportParam, contractEntity);
            log.info("结束处理合同代理商 ccode,{}", contractImportParam.getContractCodeCC());
            //处理合同代理商
            //处理合同供应商
            log.info("开始处理合同供应商 ccode,{}", contractImportParam.getContractCodeCC());
            dealLocalContractSupplier(contractImportParam, contractEntity);
            log.info("结束处理合同供应商 ccode,{}", contractImportParam.getContractCodeCC());
            //处理合同项目 价格申请 终端 点位 支付周期
            log.info("开始处理合同项目 价格申请 终端 点位 支付周期  ccode,{}", contractImportParam.getContractCodeCC());
            dealLocalContractProject(contractImportParam, contractEntity);
            log.info("结束处理合同项目 价格申请 终端 点位 支付周期  ccode,{}", contractImportParam.getContractCodeCC());
            return contractEntity.getId();
        } catch (Exception e) {
            log.error("dealLocalContract出错,{},{}", e.getMessage(), JSON.toJSONString(contractImportParam), e);
            contractImportParam.setNote(e.getMessage());
            otherError.add(contractImportParam);
//            return -1;
            throw new RuntimeException(e.getMessage());
        }

    }

    /**
     * 处理合同代理商
     *
     * @param contractImportParam
     * @param contractEntity
     */
    private void dealLocalContractProxy(ContractImportParam contractImportParam, ContractEntity contractEntity) {
        if (contractImportParam == null || StringUtils.isBlank(contractImportParam.getProxyName())) {
            return;
        }
        AgentEntity agentEntity = null;
        List<AgentEntity> agentEntities = agentService.lambdaQuery().select().eq(AgentEntity::getAgentName, contractImportParam.getProxyName()).list();
        if (agentEntities != null && agentEntities.size() > 0) {
            agentEntity = agentEntities.get(0);
        }

        if (agentEntity == null) {
            //新增代理商
            agentEntity = new AgentEntity();
            agentEntity.setAgentName(contractImportParam.getProxyName());
            agentEntity.setAgentTypes("[2]");
            agentEntity.setStatus(1);
            agentEntity.setCreator(contractEntity.getCreator());
            agentEntity.setCreateTime(LocalDateTime.now());
            agentService.save(agentEntity);
        }
        contractEntity.setAgentId(agentEntity.getId());
        contractEntity.setAgentName(agentEntity.getAgentName());
        contractService.updateById(contractEntity);
    }

    /**
     * 处理项目
     *
     * @param contractImportParam
     * @param
     */

    private void dealLocalContractProject(ContractImportParam contractImportParam, ContractEntity contractEntity) {
        if (contractImportParam == null) {
            return;
        }
        boolean payCycle = false;
        Integer cityId = null;
        //方案书
        PointData pointData = new PointData();
        List<PointDataVO> pointDataVOS = Lists.newArrayList();
        List<PointPlanVo> pointPlanVos = Lists.newArrayList();
        pointData.setPointList(pointDataVOS);
        pointData.setPlanList(pointPlanVos);
        for (ContractProjectImportParam project : contractImportParam.getProjects()) {
            log.info("开始遍历contractImportParam.getProjects()，保存到本地:{},飞书编码:{}", project.getProjectName(), contractImportParam.getContractCodeCC());
            List<String> list = Splitter.on("-").splitToList(project.getCity());
            log.info("split city list:{}", JSON.toJSONString(list));
            String cityName = list.get(1);
            String districtName = list.get(2);
            cityName = fixCity(cityName);
            districtName = fixRegion(districtName);
            String buildingNo = BUILDING_NO_CACHE.getIfPresent(cityName + districtName + project.getProjectName());
            log.info("缓存中的buildingNo:{}", buildingNo);
            if (buildingNo == null) {
                log.error("缓存中的buildingno为空:{}", cityName + districtName + project.getProjectName());
            }
            String businessCode = null;
            if(BUSINESS_CODE_CACHE.asMap().containsKey(buildingNo)){
                businessCode = BUSINESS_CODE_CACHE.getIfPresent(buildingNo);
                log.info("dataimport 从缓存获取businessCode:{},buildingNo:{}",businessCode,buildingNo);
            }else {
                ResultTemplate<String> businessCodeRet = feignCrmClient.businessCode(buildingNo);
                if("200".equals(businessCodeRet.getCode())){
                    businessCode = businessCodeRet.getData();
                    log.info("dataimport 根据buildingNo,{}查询商机编码:{}",buildingNo,businessCodeRet);
                }else {
                    noBusinessCode.add(buildingNo);
                    log.error("dataimport 根据buildingNo,{}查询商机编码:{}",buildingNo,businessCodeRet);
                    throw new RuntimeException(JSON.toJSONString(businessCodeRet));
                }
                BUSINESS_CODE_CACHE.put(buildingNo,StringUtils.isBlank(businessCode)?buildingNo+"-1":businessCode);
            }
            businessCode = StringUtils.isNotBlank(businessCode)?businessCode:buildingNo+"-1";
            //项目
            ContractProjectEntity projectEntity = new ContractProjectEntity();
            projectEntity.setContractId(contractEntity.getId());
            projectEntity.setAmount(project.getAmount());
            projectEntity.setCreator(contractEntity.getCreator());
            String userName = contractImportParam.getCreatorName();
            if (!userNameUserMap.containsKey(userName)) {
                ResultTemplate<List<UserVO>> userByNames1 = feignAuthorityRpc.getUserByNames(Arrays.asList(userName));
                if (userByNames1.getSuccess()) {
                    log.info("dataimport 根据用户名:{},称查询到数据:{}", userName, userByNames1);
                } else {
                    userSearchError.add(userName);
                    log.error("dataimport 根据用户名:{},称查询到数据:{}", userName, userByNames1);
                }
                if (userByNames1.getData() == null || userByNames1.getData().size() == 0) {
                    userSearchError.add(userName);
                }else {
                    userNameUserMap.put(userName, userByNames1.getData().get(0));
                }
            }

            String followerName = contractImportParam.getFollowerName();
            if (!userNameUserMap.containsKey(followerName)) {
                ResultTemplate<List<UserVO>> userByNames1 = feignAuthorityRpc.getUserByNames(Arrays.asList(followerName));
                if (userByNames1.getSuccess()) {
                    log.info("dataimport 根据用户名:{},称查询到数据:{}", followerName, userByNames1);
                } else {
                    userSearchError.add(userName);
                    log.error("dataimport 根据用户名:{},称查询到数据:{}", followerName, userByNames1);
                }
                if (userByNames1.getData() == null || userByNames1.getData().size() == 0) {
                    userSearchError.add(followerName);
                }else {
                    userNameUserMap.put(followerName, userByNames1.getData().get(0));
                }
            }

            projectEntity.setCreateTime(LocalDateTime.parse(contractImportParam.getApplyTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            projectEntity.setAddress(project.getProjectAddr());
            projectEntity.setPeriod(contractImportParam.getHtqyYear());
            projectEntity.setProjectName(project.getProjectName());
            projectEntity.setLevel(project.getLevelName());
            projectEntity.setPropertyType(propertyTypeMap.get(project.getProjectType()));
            projectEntity.setCityId(CITY_CACHE.getIfPresent(project.getCity().split("-")[1]));
            if (cityId == null) {
                contractEntity.setCityId(projectEntity.getCityId());
                contractService.updateById(contractEntity);
            }
            projectEntity.setIntervalDay(0);
            projectEntity.setPaymentType(payTypeMap.get(contractImportParam.getPayCycle()));
            projectEntity.setAddress(project.getProjectAddr());
            projectEntity.setStartDate(contractEntity.getStartDate());
            projectEntity.setEndDate(contractEntity.getEndDate());
            projectEntity.setDepositFlag(StringUtils.isBlank(contractImportParam.getYjAmount()) ? 0 : 1);
            if (!StringUtils.isBlank(contractImportParam.getYjAmount())) {
                String yjAmount = contractImportParam.getYjAmount().split(" ")[0];
                projectEntity.setDepositAmount(BigDecimal.valueOf(Double.valueOf(yjAmount)));
                if (StringUtils.isNotBlank(contractImportParam.getYjDeadLine())) {
                    projectEntity.setDepositPaymentDate(LocalDate.parse(contractImportParam.getYjDeadLine(), DateTimeFormatter.ofPattern("yyyy/MM/dd")));
                }
            }
            if (!StringUtils.isBlank(contractImportParam.getBzjAmount())) {
                String yjAmount = contractImportParam.getBzjAmount().split(" ")[0];
                projectEntity.setDepositAmount(BigDecimal.valueOf(Double.valueOf(yjAmount)));
                if (StringUtils.isNotBlank(contractImportParam.getBzjDeadLine())) {
                    projectEntity.setDepositPaymentDate(LocalDate.parse(contractImportParam.getBzjDeadLine(), DateTimeFormatter.ofPattern("yyyy/MM/dd")));
                }
            }
            projectEntity.setProjectCode(businessCode);
            projectService.save(projectEntity);
            Integer projectId = projectEntity.getId();

            contractEntity.setCityId(projectEntity.getCityId());
            contractService.updateById(contractEntity);


            String who = null;
            if(userNameUserMap.containsKey(userName)) {
                who = userNameUserMap.get(userName).getWno();
            }else if(userNameUserMap.containsKey(followerName)) {
                who = userNameUserMap.get(followerName).getWno();
            }else {
                who = "CC0000";
            }
            //crm的价格申请
            //调用价格申请
            //调用crm生成价格申请（cms合同->项目->价格申请要用，返回编码，写回合同系统）
            // （终端，点位），生成完了发送mq,生成快照（mq由crm那边发）
            PriceApplyVO priceApplyVo = new PriceApplyVO();
            //查询接口使用中文名称查询 工号
            priceApplyVo.setWno(who);
            priceApplyVo.setBuildingNo(buildingNo);
            priceApplyVo.setTotalAmount(project.getAmount());
            priceApplyVo.setContractDuration(contractImportParam.getHtqyYear());
//            priceApplyVo.setPaymentType(payTypeMap.get(contractImportParam.getPayCycle()));
            if (StringUtils.isNotBlank(contractImportParam.getPayCycle())) {
                //只有台账的情况下 没有支付周期信息.
                priceApplyVo.setPaymentType(payTypeMapCn.containsKey(contractImportParam.getPayCycle()) ? payTypeMapCn.get(contractImportParam.getPayCycle()) : contractImportParam.getPayCycle());
            }
            if (StringUtils.isNotBlank(contractImportParam.getYjAmount())) {
                priceApplyVo.setDepositAmount(BigDecimal.valueOf(Double.valueOf(contractImportParam.getYjAmount())));
            }
            if (StringUtils.isNotBlank(contractImportParam.getBzjAmount())) {
                priceApplyVo.setDepositAmount(BigDecimal.valueOf(Double.valueOf(contractImportParam.getBzjAmount())));
            }
            priceApplyVo.setIsDeposit(priceApplyVo.getDepositAmount() != null ? 1 : 0);
            priceApplyVo.setBuildingName(project.getProjectName());
            priceApplyVo.setCreateTime(LocalDateTime.now());

            priceApplyVo.setBusinessCode(businessCode);
            //设备
            PriceApplyDeviceVO deviceVo = new PriceApplyDeviceVO();
            deviceVo.setSize("");
            deviceVo.setSignPrice(project.getPrice());

            //点位方案
            PointPlanVo pointPlanVo = new PointPlanVo();
            pointPlanVo.setBusinessCode(businessCode);
            pointPlanVo.setCreateBy(who);
            pointPlanVo.setUpdateBy(pointPlanVo.getCreateBy());
            pointPlanVo.setBuildingRatingNo(buildingNo);
            pointPlanVo.setCreateTime(contractEntity.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            pointPlanVo.setUpdateTime(contractEntity.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            pointPlanVo.setStatus("0037-3");
            pointPlanVos.add(pointPlanVo);
            List<PointInfoVo> pointInfoVos = POINT_CACHE.getIfPresent(buildingNo + project.getProjectName());
            List<PriceApplyDevicePointVO> pointVos = Lists.newArrayList();
            for (PointInfoVo pointInfoVo : pointInfoVos) {
                PriceApplyDevicePointVO pointVo = new PriceApplyDevicePointVO();
                pointVo.setCode(pointInfoVo.getCode());
                pointVo.setName(pointInfoVo.getName());
                pointVos.add(pointVo);

                PointDataVO pointDataVO = new PointDataVO();
                pointDataVO.setCode(pointInfoVo.getCode());
                pointDataVO.setCreateTime(contractEntity.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));


                pointDataVO.setCreateBy(who);
                pointDataVO.setUpdateBy(who);

                pointDataVO.setBuildingRatingNo(buildingNo);
                pointDataVO.setUpdateTime(contractEntity.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                String endDate = contractImportParam.getEndDate();
                if (StringUtils.isNotBlank(endDate)) {
                    LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy/M/d"));
                    if (StringUtils.isNotBlank(contractImportParam.getFreeQx())) {
                        List<String> freeQx = Splitter.on("-").trimResults().omitEmptyStrings().splitToList(contractImportParam.getFreeQx());
                        LocalDate freeEnd = LocalDate.parse(freeQx.get(0), DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                        if (freeEnd.isAfter(end)) {
                            pointDataVO.setExpireTime(freeEnd.atTime(0, 0, 0).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        } else {
                            pointDataVO.setExpireTime(end.atTime(0, 0, 0).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        }
                    } else {
                        pointDataVO.setExpireTime(end.atTime(0, 0, 0).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }
                }else {
                    pointDataVO.setExpireTime(LocalDateTime.now().plusYears(1).format(DateTimeFormatter.ofPattern(VenueConstants.DATE_TIME_FORMAT)));
                }
                pointDataVO.setDeviceSize("");
                pointDataVO.setBusinessCode(businessCode);
                pointDataVOS.add(pointDataVO);
            }
            deviceVo.setQuantity(pointVos.size());
            deviceVo.setPoints(pointVos);
            priceApplyVo.setDevices(Arrays.asList(deviceVo));
            log.info("dataimport 价格申请参数:{}", JSON.toJSONString(priceApplyVo));
            ResultTemplate<String> priceApply = feignCrmClient.priceApply(priceApplyVo);
            if ("200".equals(priceApply.getCode())) {
                log.info("dataimport 价格申请返回数据:{}", priceApply.getData());
            } else {
                log.error("dataimport 价格申请返回数据:{}", priceApply.getData());
                throw new RuntimeException(JSON.toJSONString(priceApply));
            }
            //价格申请
            ContractPriceApplyEntity contractPriceApplyEntity = new ContractPriceApplyEntity();
            contractPriceApplyEntity.setContractId(contractEntity.getId());
            contractPriceApplyEntity.setProjectId(projectId);
            contractPriceApplyEntity.setCreator(contractEntity.getCreator());
            contractPriceApplyEntity.setCreateTime(LocalDateTime.now());
            contractPriceApplyEntity.setAmount(projectEntity.getAmount());
            contractPriceApplyEntity.setDeviceCount(1);
            contractPriceApplyEntity.setApplyCode(priceApply.getData());
            priceApplyService.save(contractPriceApplyEntity);

            //终端
            ContractDeviceEntity contractDeviceEntity = new ContractDeviceEntity();
            contractDeviceEntity.setContractId(contractEntity.getId());
            contractDeviceEntity.setCreator(contractEntity.getCreator());
            contractDeviceEntity.setSignCount(pointVos.size());
            contractDeviceEntity.setPriceApplyId(contractPriceApplyEntity.getId());
            contractDeviceEntity.setProjectId(projectId);
            contractDeviceEntity.setSize("");
            contractDeviceEntity.setCreateTime(LocalDateTime.now());
            contractDeviceEntity.setType("0036-1");
            deviceService.save(contractDeviceEntity);
            List<ContractDevicePointEntity> pointEntities = Lists.newArrayList();
            //点位
            for (PointInfoVo pointInfoVo : pointInfoVos) {
                ContractDevicePointEntity contractDevicePointEntity = new ContractDevicePointEntity();
                contractDevicePointEntity.setContractId(contractEntity.getId());
                contractDevicePointEntity.setProjectId(projectId);
                contractDevicePointEntity.setCode(pointInfoVo.getCode());
                contractDevicePointEntity.setName(pointInfoVo.getName());
                contractDevicePointEntity.setCreator(contractEntity.getCreator());
                contractDevicePointEntity.setCreateTime(LocalDateTime.now());
                contractDevicePointEntity.setDeviceId(contractDeviceEntity.getId());
                pointEntities.add(contractDevicePointEntity);
            }
            pointService.saveBatch(pointEntities);


//            if (!payCycle) {
//                if (StringUtils.isNotBlank(contractImportParam.getPayCycle())) {
//                    //使用合同开始日期 结束日期 作为项目的开始和结束日期 以及付款方式计算付款周期
////                    if(payTypeHandleMap.containsKey(contractImportParam.getPayCycle())){
////                        List<ContractPaymentPeriodEntity> paymentPeriodEntities = payTypeHandleMap.get(contractImportParam.getPayCycle())
////                                .getPayPeriod(contractEntity.getStartDate(),contractEntity.getEndDate()
////                                ,project.getAmount(),contractImportParam,contractEntity,contractDeviceEntity,projectId);
////                        paymentPeriodService.saveBatch(paymentPeriodEntities);
////                    }
//                    //原样导入
//                    //支付周期 只生成一次 默认挂到第一个项目
//                    List<ContractPaymentPeriodEntity> paymentPeriodEntities = Lists.newArrayList();
//                    for (int i = 0; i < contractImportParam.getPayCycleInfos().size(); i++) {
//                        PayCycleInfo payCycleInfo = contractImportParam.getPayCycleInfos().get(i);
//                        ContractPaymentPeriodEntity contractPaymentPeriodEntity = new ContractPaymentPeriodEntity();
//                        contractPaymentPeriodEntity.setContractId(contractEntity.getId());
//                        contractPaymentPeriodEntity.setProjectId(projectId);
//                        contractPaymentPeriodEntity.setCreateTime(LocalDateTime.now());
//                        contractPaymentPeriodEntity.setCreator(contractEntity.getCreator());
//                        contractPaymentPeriodEntity.setDeviceId(contractDeviceEntity.getId());
//                        contractPaymentPeriodEntity.setSupplierId(0);
//                        contractPaymentPeriodEntity.setSupplierName(contractImportParam.getSupplierName());
//                        contractPaymentPeriodEntity.setAmount(BigDecimal.valueOf(Double.valueOf(payCycleInfo.getPayAmount())));
//                        contractPaymentPeriodEntity.setStartDate(LocalDate.parse(payCycleInfo.getPayDeadLine(), DateTimeFormatter.ofPattern("yyyy/MM/dd")));
//                        if (i == contractImportParam.getPayCycleInfos().size() - 1) {
//                            //结束日期需要处理一下 第一条数据的结束日期为第二条数据的开始日期 最后一条数据的结束日期是合同结束日期
//                            contractPaymentPeriodEntity.setEndDate(contractEntity.getEndDate());
//                        } else {
//                            //结束日期需要处理一下 第一条数据的结束日期为第二条数据的开始日期 最后一条数据的结束日期是合同结束日期
//                            contractPaymentPeriodEntity.setEndDate(LocalDate.parse(contractImportParam.getPayCycleInfos().get(i + 1).getPayDeadLine(), DateTimeFormatter.ofPattern("yyyy/MM/dd")).minusDays(1));
//                        }
//                        paymentPeriodEntities.add(contractPaymentPeriodEntity);
//                    }
//                    paymentPeriodService.saveBatch(paymentPeriodEntities);
//                }
//                //价格周期 有免租则生成两条 一条免租 一条正常
//                List<ContractPricePeriodEntity> pricePeriodEntities = Lists.newArrayList();
//                if (StringUtils.isNotBlank(contractImportParam.getFreeQx())) {
//                    //免租周期
//                    ContractPricePeriodEntity pricePeriodEntity = new ContractPricePeriodEntity();
//                    pricePeriodEntity.setContractId(contractEntity.getId());
//                    pricePeriodEntity.setProjectId(projectId);
//                    pricePeriodEntity.setDeviceId(contractDeviceEntity.getId());
//                    pricePeriodEntity.setCreator(contractEntity.getCreator());
//                    pricePeriodEntity.setCreateTime(LocalDateTime.now());
//                    List<String> freeQx = Splitter.on("-").trimResults().omitEmptyStrings().splitToList(contractImportParam.getFreeQx());
//                    pricePeriodEntity.setStartDate(LocalDate.parse(freeQx.get(0), DateTimeFormatter.ofPattern("yyyy/MM/dd")));
//                    pricePeriodEntity.setEndDate(LocalDate.parse(freeQx.get(1), DateTimeFormatter.ofPattern("yyyy/MM/dd")));
//                    pricePeriodEntity.setFreeFlag(1);
//                    pricePeriodEntity.setPriceYear(BigDecimal.ZERO);
//                    pricePeriodEntity.setPriceMonth(BigDecimal.ZERO);
//                    pricePeriodEntities.add(pricePeriodEntity);
//                }
//                //正常周期
//                ContractPricePeriodEntity pricePeriodEntity = new ContractPricePeriodEntity();
//                pricePeriodEntity.setContractId(contractEntity.getId());
//                pricePeriodEntity.setProjectId(projectId);
//                pricePeriodEntity.setDeviceId(contractDeviceEntity.getId());
//                pricePeriodEntity.setCreator(contractEntity.getCreator());
//                pricePeriodEntity.setCreateTime(LocalDateTime.now());
//                if (StringUtils.isNotBlank(contractImportParam.getFreeQx())) {
//                    List<String> freeQx = Splitter.on("-").trimResults().omitEmptyStrings().splitToList(contractImportParam.getFreeQx());
//                    pricePeriodEntity.setStartDate(LocalDate.parse(freeQx.get(1), DateTimeFormatter.ofPattern("yyyy/MM/dd")).plusDays(1));
//                    pricePeriodEntity.setEndDate(contractEntity.getEndDate());
//                } else {
//                    pricePeriodEntity.setStartDate(contractEntity.getStartDate());
//                    pricePeriodEntity.setEndDate(contractEntity.getEndDate());
//                }
//                pricePeriodEntity.setFreeFlag(0);
//                BigDecimal priceYear = projectEntity.getAmount().divide(projectEntity.getPeriod(), 2, RoundingMode.HALF_UP);
//                priceYear = priceYear.divide(BigDecimal.valueOf(project.getPointNum()), 2, RoundingMode.HALF_UP);
//
//
//                pricePeriodEntity.setPriceYear(priceYear);
//                BigDecimal month = BigDecimal.valueOf(12);
//                BigDecimal priceMonth = priceYear.divide(month, 2, RoundingMode.HALF_UP);
//                pricePeriodEntity.setPriceMonth(priceMonth);
//                pricePeriodEntities.add(pricePeriodEntity);
//                pricePeriodService.saveBatch(pricePeriodEntities);
//                payCycle = true;
//            }

        }
        log.info("dataimport sycPointData参数:{}", JSON.toJSONString(pointData));
        ResultTemplate<Boolean> sycPointData = feignCrmClient.sycPointData(pointData);
        if ("200".equals(sycPointData.getCode())) {
            log.info("dataimport sycPointData返回数据:{}", sycPointData);
        } else {
            log.error("dataimport sycPointData返回数据:{}", sycPointData);
            throw new RuntimeException(JSON.toJSONString(sycPointData));
        }
    }

    /**
     * 处理供应商
     *
     * @param contractImportParam
     * @param
     */
    private void dealLocalContractSupplier(ContractImportParam contractImportParam, ContractEntity contractEntity) {
        if (contractImportParam == null) {
            return;
        }
        String[] strs = contractImportParam.getSupplierName().split(";");
        for (String str : strs) {
            //excel只有供应商名称 也就是 交易方 如果系统不存在供应商怎么处理 银行账号怎么处理
            ContractSupplierEntity contractSupplierEntity = new ContractSupplierEntity();
            List<SupplierEntity> supplierEntities = supplierService.lambdaQuery().select().eq(SupplierEntity::getSupplierName, str).list();
            SupplierEntity supplierEntity = null;
            if (supplierEntities != null && supplierEntities.size() > 0) {
                supplierEntity = supplierEntities.get(0);
            }

            if (supplierEntity == null) {
                //新增一个供应商
                supplierEntity = new SupplierEntity();
                supplierEntity.setSupplierName(str);
                supplierEntity.setIdCard("");
                supplierEntity.setSupplierCode(str);
                supplierEntity.setStatus(1);
                supplierEntity.setCreator(contractEntity.getCreator());
                supplierEntity.setCreateTime(LocalDateTime.now());
                supplierService.save(supplierEntity);
            }
            contractSupplierEntity.setSupplierId(supplierEntity.getId());
            contractSupplierEntity.setSupplierName(supplierEntity.getSupplierName());
            contractSupplierEntity.setCreator(contractEntity.getCreator());
            contractSupplierEntity.setCreateTime(LocalDateTime.now());
            contractSupplierEntity.setOperator(contractEntity.getCreator());
            contractSupplierEntity.setContactPerson("");
            contractSupplierEntity.setContactPhone("");
            contractSupplierEntity.setContactEmail("");
            contractSupplierEntity.setContactAddress("");
            contractSupplierEntity.setContractId(contractEntity.getId());
            iContractSupplierService.save(contractSupplierEntity);

            //合同银行账户
            ContractSupplierBankEntity contractSupplierBankEntity = new ContractSupplierBankEntity();
            contractSupplierBankEntity.setSupplierId(supplierEntity.getId());
            contractSupplierBankEntity.setBankAccountCode("temp");
            contractSupplierBankEntity.setContractId(contractEntity.getId());
            contractSupplierBankService.save(contractSupplierBankEntity);
            //银行账户
        }

    }

    /**
     * 保存合同基本信息 返回id
     *
     * @param contractImportParam
     * @return
     */
    private ContractEntity dealLocalContractBase(ContractImportParam contractImportParam) {
        if (contractImportParam == null) {
            return null;
        }
        ContractEntity contractEntity = new ContractEntity();
        contractEntity.setApplyCode(getApplyCode());
        contractEntity.setImportFlag(1);
        contractEntity.setContractType(ContractTypeEnum.HISTORY.getCode());
        contractEntity.setContractCode(contractImportParam.getContractCode());
        contractEntity.setCooperateType(1);
        contractEntity.setBusinessType("自营".equals(contractImportParam.getDevTypeName()) ? 1 : 2);
        String startDate = contractImportParam.getStartDate();
        if (StringUtils.isNotBlank(startDate)) {
            contractEntity.setStartDate(LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy/M/d")));
        }
        String endDate = contractImportParam.getEndDate();
        if (StringUtils.isNotBlank(endDate)) {
            contractEntity.setEndDate(LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy/M/d")));
        }
        //如果有免租 需要处理一下 合同开始时间和结束时间
//        if (StringUtils.isNotBlank(contractImportParam.getFreeQx())) {
//            List<String> freeQx = Splitter.on("-").trimResults().omitEmptyStrings().splitToList(contractImportParam.getFreeQx());
//            LocalDate freeStart = LocalDate.parse(freeQx.get(0), DateTimeFormatter.ofPattern("yyyy/MM/dd"));
//            LocalDate freeEnd = LocalDate.parse(freeQx.get(0), DateTimeFormatter.ofPattern("yyyy/MM/dd"));
//            if (freeStart.isBefore(contractEntity.getStartDate())) {
//                contractEntity.setStartDate(freeStart);
//            } else if (freeEnd.isAfter(contractEntity.getEndDate())) {
//                contractEntity.setEndDate(freeEnd);
//            }
//        }
        contractEntity.setPeriod(contractImportParam.getHtqyYear());
        contractEntity.setTotalAmount(contractImportParam.getContractTotalAmount());

        //固定值
        contractEntity.setSignPartyId(1);
        contractEntity.setSignPartyName("广东创视科技广告有限公司");
        contractEntity.setContactPerson("余云勇");
        contractEntity.setContactPhone("13032820727");
        contractEntity.setContactEmail("<EMAIL>");
        contractEntity.setContactAddress("深圳市宝安区石岩街道塘头社区塘头1号路8号创维创新谷2#楼B1009");

        String creatorName = contractImportParam.getCreatorName();
        Integer creator = null;
        if (userNameUserMap.containsKey(creatorName)) {
            creator = userNameUserMap.get(creatorName).getId();
        } else {
            ResultTemplate<List<UserVO>> userByNames = feignAuthorityRpc.getUserByNames(Arrays.asList(creatorName));
            if (userByNames.getSuccess()) {
                log.info("dataimport 根据用户名:{},称查询到数据:{}", creatorName, userByNames);
            } else {
                log.error("dataimport 根据用户名:{},称查询到数据:{}", creatorName, userByNames);
            }
            creator = (userByNames.getData() == null || userByNames.getData().size() == 0) ? 0 : userByNames.getData().get(0).getId();
        }
        contractEntity.setCreator(creator);
        contractEntity.setCreatorName(creatorName);

        contractEntity.setCreateTime(LocalDateTime.parse(contractImportParam.getApplyTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        if ("是".equals(contractImportParam.getGift())) {
            contractEntity.setGiftFlag(BooleFlagEnum.YES.getCode());
        } else if ("否".equals(contractImportParam.getGift())) {
            contractEntity.setGiftFlag(BooleFlagEnum.NO.getCode());
        }  else {
            //后续统一把2置空
            contractEntity.setGiftFlag(2);
        }

        if ("合同章".equals(contractImportParam.getYzTypeName())) {
            contractEntity.setSealType("0040-2");
        } else if ("公章".equals(contractImportParam.getYzTypeName())) {
            contractEntity.setSealType("0040-1");
        }
        contractEntity.setInvoiceType("");
        contractEntity.setTaxPoint("");

        contractEntity.setOldFlag(0);
        contractEntity.setOldContractCode("");
        contractEntity.setDescription(contractImportParam.getNote());
        contractEntity.setOperator(creator);
        contractEntity.setSubmitter(creator);
        contractEntity.setApplyStatus("0025-4");

        String followerName = contractImportParam.getFollowerName();
        Integer follower = null;
        if (userNameUserMap.containsKey(followerName)) {
            follower = userNameUserMap.get(followerName).getId();
        } else {
            ResultTemplate<List<UserVO>> userByNames = feignAuthorityRpc.getUserByNames(Arrays.asList(followerName));
            if (userByNames.getSuccess()) {
                log.info("dataimport 根据用户名:{},称查询到数据:{}", followerName, userByNames);
            } else {
                log.error("dataimport 根据用户名:{},称查询到数据:{}", followerName, userByNames);
            }
            follower = (userByNames.getData() == null || userByNames.getData().size() == 0) ? 0 : userByNames.getData().get(0).getId();
        }
        contractEntity.setFollower(follower);
        contractEntity.setFollowerName(followerName);

        contractEntity.setSealParty1Flag(1);
        contractEntity.setSealParty2Flag(1);
        contractEntity.setSealParty3Flag(1);
        contractEntity.setFormalFlag(1);
        //已归档未归档有区别
        if ("已归档".equals(contractImportParam.getYgd())) {
            contractEntity.setFormalStatus("0028-3");
            contractEntity.setArchiveFlag(1);
            contractEntity.setArchiveTime(LocalDateTime.parse(contractImportParam.getGdTime(), DateTimeFormatter.ofPattern(VenueConstants.DATE_TIME_FORMAT)));
        } else {
            contractEntity.setFormalStatus("0028-1");
            contractEntity.setArchiveFlag(0);
        }

        if ("标准".equals(contractImportParam.getContractMbType())) {
            contractEntity.setNormalFlag(1);
        } else if ("非标".equals(contractImportParam.getContractMbType())) {
            contractEntity.setNormalFlag(0);
        }  else {
            //后续统一把2置空
            contractEntity.setNormalFlag(2);
        }

        contractService.save(contractEntity);
        return contractEntity;
    }

    /**
     * 调用crm生成商机和客户
     *
     * @param contractImportParam
     */
    private void invokeCrmBusiness(ContractImportParam contractImportParam) {


//        String userName = contractImportParam.getApplyUserName();
//        if(contractUserMap.containsKey(contractImportParam.getContractCodeCC())){
//            userName = contractUserMap.get(contractImportParam.getContractCodeCC());
//        }
//        Integer applyUserId = null;
//        if(userNameUserMap.containsKey(userName)){
//            applyUserId = userNameUserMap.get(userName).getId();
//        }else {
//            ResultTemplate<List<UserVO>> userByNames = feignAuthorityRpc.getUserByNames(Arrays.asList(userName));
//            log.error("dataimport 根据用户名:{},称查询到数据:{}",userName,userByNames);
//            applyUserId = (userByNames.getData()==null||userByNames.getData().size()==0)?0:userByNames.getData().get(0).getId();
//        }

        String creatorName = contractImportParam.getCreatorName();
        String who = null;
        if (userNameUserMap.containsKey(creatorName)) {
            who = userNameUserMap.get(creatorName).getWno();
        } else {
            ResultTemplate<List<UserVO>> userByNames = feignAuthorityRpc.getUserByNames(Arrays.asList(creatorName));
            if (userByNames.getSuccess()) {
                log.info("dataimport 根据用户名:{},称查询到数据:{}", creatorName, userByNames);
            } else {
                userSearchError.add(creatorName);
                log.error("dataimport 根据用户名:{},称查询到数据:{}", creatorName, userByNames);
            }
            if (userByNames.getData().size() == 0) {
                userSearchError.add(creatorName);
                log.error("dataimport 根据用户名:{},称查询到数据:{}", creatorName, userByNames);
            }else {
                userNameUserMap.put(creatorName,userByNames.getData().get(0));
                who = userByNames.getData().get(0).getWno();
            }
            if (who == null) {
                //使用跟进人
                String followerName = contractImportParam.getFollowerName();
                if (userNameUserMap.containsKey(followerName)) {
                    who = userNameUserMap.get(followerName).getWno();
                } else {
                    userByNames = feignAuthorityRpc.getUserByNames(Arrays.asList(followerName));
                    if (userByNames.getSuccess()) {
                        log.info("dataimport 根据用户名:{},称查询到数据:{}", followerName, userByNames);
                        if (userByNames.getData().size() == 0) {
                            userSearchError.add(followerName);
                            log.error("dataimport 根据用户名:{},称查询到数据:{}", followerName, userByNames);
                            who = "CC0000";
                        }else {
                            userNameUserMap.put(followerName,userByNames.getData().get(0));
                            who = userByNames.getData().get(0).getWno();
                        }
                    } else {
                        userSearchError.add(followerName);
                        log.error("dataimport 根据用户名:{},称查询到数据:{}", followerName, userByNames);
                        who = "CC0000";
                    }
                }
            }
        }

        for (ContractProjectImportParam project : contractImportParam.getProjects()) {
            List<String> list = Splitter.on("-").splitToList(project.getCity());
            String cityName = list.get(1);
            String districtName = list.get(2);
            cityName = fixCity(cityName);
            districtName = fixRegion(districtName);
            ChangeOwnerVo changeOwnerVo = new ChangeOwnerVo();
            String buildingNo = BUILDING_NO_CACHE.getIfPresent(cityName + districtName + project.getProjectName());
            changeOwnerVo.setBuildingNo(buildingNo);


            //翻译成工号
            changeOwnerVo.setWno(who);
            changeOwnerVo.setCreateTime(contractImportParam.getApplyTime().replaceAll("/", "-"));
            log.info("dataimport changeOwner参数:{}", JSON.toJSONString(changeOwnerVo));
            ResultTemplate<Object> changeOwner = feignCrmClient.changeOwner(changeOwnerVo);
            if (changeOwner.getCode().equals("200")) {
                log.info("dataimport changeOwner返回数据:{}", changeOwner);
            } else {
                changeOwnerError.add(changeOwnerVo);
                log.error("dataimport changeOwner返回数据:{}", changeOwner);
            }
        }
    }

    /**
     * 校验点位数量是否匹配
     *
     * @return
     */
    private boolean checkPoint(ContractImportParam contractImportParam) {

        if (contractImportParam == null) {
            return false;
        }
        try {
            List<ContractProjectImportParam> projects = contractImportParam.getProjects();
            log.info("开始循环项目校验,飞书编码,{}", contractImportParam.getContractCodeCC());
            for (ContractProjectImportParam project : projects) {
                log.info("开始循环项目校验,项目: {},飞书编码,{}", project.getProjectName(), contractImportParam.getContractCodeCC());
                String city = project.getCity();
                if (StringUtils.isBlank(city)) {
                    log.info("城市为空,项目: {},飞书编码,{}", project.getProjectName(), contractImportParam.getContractCodeCC());
                    return false;
                }
                if (StringUtils.isNotBlank(cityToUse)) {
                    if (!city.contains(cityToUse)) {
                        log.info("城市非nacos指定,项目: {},飞书编码,{}", project.getProjectName(), contractImportParam.getContractCodeCC());
                        return false;
                    }
                }
                List<String> list = Splitter.on("-").splitToList(city);
                if (list == null || list.size() != 3) {
                    log.info("城市split后非3个长度,项目: {},飞书编码,{}", project.getProjectName(), contractImportParam.getContractCodeCC());
                    return false;
                }
                String cityName = list.get(1);
                String districtName = list.get(2);
                Integer cityId = null;
                Integer districId = null;
                //翻译为对应的id
                if (CITY_CACHE.asMap().containsKey(cityName) && CITY_CACHE.asMap().containsKey(districtName)) {
                    cityId = CITY_CACHE.getIfPresent(cityName);
                    districId = CITY_CACHE.getIfPresent(districtName);
                } else {
                    if (StringUtils.isNotBlank(districtName)) {
                        DistrictVo districtVo = new DistrictVo();
                        cityName = fixCity(cityName);
                        districtVo.setCityName(cityName);
                        districtName = fixRegion(districtName);
                        districtVo.setCountyName(districtName.trim());
                        log.info("dataimport sys county 参数:{}", JSON.toJSONString(districtVo));
                        ResultTemplate<DistrictVoRet> county = feignSysClient.county(districtVo);
                        if (county.getSuccess()) {
                            log.info("dataimport sys county 返回:{}", county);
                        } else {
                            log.error("dataimport sys county 返回:{}", county);
                        }
                        //暂时不做异常判断 跑主流程 比如success false的情况
                        if (county.getData() == null) {
                            log.error("dataimport 城市:{},区域:{},未找到对应id", cityName, districtName);
                            return false;
                        }
                        cityId = county.getData().getParentId();
                        districId = county.getData().getId();
                        CITY_CACHE.put(cityName, cityId);
                        CITY_CACHE.put(districtName, districId);
                    } else {
                        log.info("dataimport sys city 参数:{}", cityName);
                        cityName = fixCity(cityName);
                        ResultTemplate<DistrictVoRet> cityRet = feignSysClient.city(cityName);
                        if (cityRet.getSuccess()) {
                            log.info("dataimport sys city 返回:{}", cityRet);
                        } else {
                            log.error("dataimport sys city 返回:{}", cityRet);
                        }

                        //暂时不做异常判断 跑主流程 比如success false的情况
                        if (cityRet.getData() == null) {
                            log.error("dataimport 城市:{},未找到对应id", cityName);
                            return false;
                        }
                        cityId = cityRet.getData().getId();
                        CITY_CACHE.put(cityName, cityId);
                    }

                }
                //查询buildingNo
                String buildingNo;
                BuildingNoVo buildingNoVo = new BuildingNoVo();
                buildingNoVo.setProjectName(project.getProjectName());
                buildingNoVo.setCityId(cityId);
                buildingNoVo.setDistrictId(districId);
                if (districId == null) {
                    log.info("dataimport ssp getBuildingNoOnlyCity 参数:{}", JSON.toJSONString(buildingNoVo));
                    ResultTemplate<List<BuildingNoVoRet>> r11 = feignSspClient.getBuildingNoOnlyCity(buildingNoVo);
                    if (r11.getSuccess()) {
                        log.info("dataimport ssp getBuildingNoOnlyCity 返回:{}", r11);
                    } else {
                        log.error("dataimport ssp getBuildingNoOnlyCity 返回:{}", r11);
                        noBuildingNo.add(String.format("根据项目:%s,城市:%s，区域:%s,未找到buildingNo", project.getProjectName(), cityName, districtName));
                        noBuildingNoParam.add(contractImportParam);
                        return false;
                    }
                    if (r11.getData() == null || r11.getData().size() == 0) {
                        log.error("dataimport 根据项目:{},城市:{}，区域:{},未找到buildingNo", project.getProjectName(), cityName, districtName);
                        noBuildingNo.add(String.format("根据项目:%s,城市:%s，区域:%s,未找到buildingNo", project.getProjectName(), cityName, districtName));
                        noBuildingNoParam.add(contractImportParam);
                        return false;
                    }
                    if (r11.getData().size() > 1) {
                        log.error("dataimport 根据项目:{},城市:{}，区域:{},找到多个buildingNo", project.getProjectName(), cityName, districtName);
                        buildingNoMuti.add(contractImportParam);
                        return false;
                    }

                    buildingNo = r11.getData().get(0).getBuildingRatingNo();
                } else {
                    log.info("dataimport ssp getBuildingNo 参数:{}", JSON.toJSONString(buildingNoVo));
                    ResultTemplate<BuildingNoVoRet> r12 = feignSspClient.getBuildingNo(buildingNoVo);
                    if (r12.getSuccess()) {
                        log.info("dataimport ssp getBuildingNo 返回:{}", r12);
                    } else {
                        log.error("dataimport ssp getBuildingNo 返回:{}", r12);
                        noBuildingNo.add(String.format("feignSspClient.getBuildingNo 返回success false 根据项目:%s,城市:%s，区域:%s,未找到buildingNo", project.getProjectName(), cityName, districtName));
                        noBuildingNoParam.add(contractImportParam);
                        return false;
                    }

                    if (r12.getData() == null || StringUtils.isBlank(r12.getData().getBuildingRatingNo())) {
                        log.error("dataimport 根据项目:{},城市:{}，区域:{},未找到buildingNo", project.getProjectName(), cityName, districtName);
                        noBuildingNo.add(String.format("根据项目:%s,城市:%s，区域:%s,未找到buildingNo", project.getProjectName(), cityName, districtName));
                        noBuildingNoParam.add(contractImportParam);
                        return false;
                    }
                    buildingNo = r12.getData().getBuildingRatingNo();
                }

                log.info("buildingNo放入缓存 key:{}，value:{}", cityName + districtName + project.getProjectName(), buildingNo);
                BUILDING_NO_CACHE.put(cityName + districtName + project.getProjectName(), buildingNo);

                String key = buildingNo + project.getProjectName();
                List<PointInfoVo> pointInfoVos = POINT_CACHE.getIfPresent(key);
                if (pointInfoVos == null) {

                    CrmProjectParam crmProjectParam = new CrmProjectParam();
                    crmProjectParam.setBuildingRatingNo(buildingNo);
                    crmProjectParam.setName(project.getProjectName());
                    log.info("dataimport ssp getPointInfo 参数:{}", crmProjectParam);
                    ResultTemplate<List<PointInfoVo>> r2 = feignSspClient.building_rating_no(crmProjectParam);
                    if (r2.getSuccess()) {
                        log.info("dataimport ssp getPointInfo 返回:{}", r2);
                    } else {
                        log.error("dataimport ssp getPointInfo 返回:{}", r2);
                        invalidPointList.add(contractImportParam);
                        pointNotMatch.add(String.format("ssp返回的success为false,项目名称:%s,城市:%s,区域:%s,ssp点位:%s,excel点位:%s", project.getProjectName(), cityName, districtName, r2.getData(), project.getPointNum()));
                        return false;
                    }
                    if (r2.getData() == null || r2.getData().size() == 0) {
                        log.error("dataimport 根据buildingNo:{},未找到点位数据", buildingNo);
                        invalidPointList.add(contractImportParam);
                        pointNotMatch.add(String.format("ssp返回的点位数据为空,项目名称:%s,城市:%s,区域:%s,ssp点位:空,excel点位:%s", project.getProjectName(), cityName, districtName, project.getPointNum()));
                        return false;
                    }
                    POINT_CACHE.put(buildingNo + project.getProjectName(), r2.getData());
                    pointInfoVos = r2.getData();
                }
                if (pointInfoVos.size() != project.getPointNum()) {
                    log.error("dataimport ssp返回的点位数量和excel的点位数量不一致,项目名称:{},城市:{},区域:{},ssp点位:{},excel点位:{}", project.getProjectName(), cityName, districtName, pointInfoVos.size(), project.getPointNum());
                    invalidPointList.add(contractImportParam);
                    pointNotMatch.add(String.format("ssp返回的点位数量和excel的点位数量不一致,项目名称:%s,城市:%s,区域:%s,ssp点位:%s,excel点位:%s", project.getProjectName(), cityName, districtName, pointInfoVos.size(), project.getPointNum(), pointInfoVos.size(), project.getPointNum()));
                    return true;
                }

                String creatorName = contractImportParam.getCreatorName();
                //校验一下用户
                if (!userNameUserMap.containsKey(creatorName)) {
                    ResultTemplate<List<UserVO>> userByNames1 = feignAuthorityRpc.getUserByNames(Arrays.asList(creatorName));
                    if (userByNames1.getSuccess()) {
                        log.info("dataimport 根据用户名:{},称查询到数据:{}", creatorName, userByNames1);
                        if (userByNames1.getData() == null || userByNames1.getData().size() == 0) {
                            userSearchError.add(creatorName);
                            log.error("dataimport 根据用户名:{},称查询到没有数据:{}", creatorName, userByNames1);
                        }else {
                            userNameUserMap.put(creatorName, userByNames1.getData().get(0));
                        }
                    } else {
                        userSearchError.add(creatorName);
                        log.error("dataimport 根据用户名:{},称查询到数据:{}", creatorName, userByNames1);
                    }
                }
                String followerName = contractImportParam.getFollowerName();
                //校验一下用户
                if (!userNameUserMap.containsKey(followerName)) {
                    ResultTemplate<List<UserVO>> userByNames1 = feignAuthorityRpc.getUserByNames(Arrays.asList(followerName));
                    if (userByNames1.getSuccess()) {
                        log.info("dataimport 根据用户名:{},称查询到数据:{}", followerName, userByNames1);
                    } else {
                        userSearchError.add(followerName);
                        log.error("dataimport 根据用户名:{},称查询到数据:{}", followerName, userByNames1);
                        return false;
                    }
                    if (userByNames1.getData() == null || userByNames1.getData().size() == 0) {
                        userSearchError.add(followerName);
                        log.error("dataimport 根据用户名:{},称查询到没有数据:{}", followerName, userByNames1);
                        return false;
                    }
                    userNameUserMap.put(followerName, userByNames1.getData().get(0));
                }
                //校验一下单价，年限，点位数量
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    String fixCity(String cityName) {
        if (StringUtils.isBlank(cityName)) {
            return "";
        }
        if (!cityName.contains("市")) {
            return cityName + "市";
        }
        return cityName;
    }

    String fixRegion(String region) {
        if (StringUtils.isBlank(region)) {
            return "";
        }
        if (!region.contains("区") && !region.contains("市")
                && !region.contains("县")) {
            return region + "区";
        }
        return region;
    }

    private Map<String, ContractImportParam> dealFeishuExcel(MultipartFile fileFeishu) throws IOException {
        List<ContractExcelFeishuParam> contractExcelFeishuParams = parseRowFeishu(fileFeishu.getInputStream());
        Map<String, ContractImportParam> map = Maps.newHashMap();
        ContractImportParam contractApplyParam = null;
        List<ContractProjectImportParam> projects = null;
        List<String> suppliers = null;
        List<PayCycleInfo> payCycleInfos = null;
        List<FloorPoint> floorPoints = null;
        String ccCode = null;
        for (ContractExcelFeishuParam contractExcelFeishuParam : contractExcelFeishuParams) {
            try {
                //合同基本信息
                if (StringUtils.isNotBlank(contractExcelFeishuParam.getContractCode())) {
                    map.put(ccCode, contractApplyParam);
                    ccCode = contractExcelFeishuParam.getContractCodeCC();
                    contractApplyParam = new ContractImportParam();
                    projects = Lists.newArrayList();
                    suppliers = Lists.newArrayList();
                    payCycleInfos = Lists.newArrayList();
                    floorPoints = Lists.newArrayList();
                    //设置属性
                    contractApplyParam.setProjects(projects);
                    contractApplyParam.setSupplierNames(suppliers);
                    contractApplyParam.setPayCycleInfos(payCycleInfos);
                    contractApplyParam.setFloorPoints(floorPoints);

                    //合同基本
                    contractApplyParam.setContractName(contractExcelFeishuParam.getContractName());
                    contractApplyParam.setContractCode(contractExcelFeishuParam.getContractCode());
                    contractApplyParam.setYgd(contractExcelFeishuParam.getYgd());
                    contractApplyParam.setDevTypeName(contractExcelFeishuParam.getDevTypeName());
                    contractApplyParam.setStartDate(contractExcelFeishuParam.getStartDate());
                    contractApplyParam.setEndDate(contractExcelFeishuParam.getEndDate());
                    contractApplyParam.setHtqyYear(contractExcelFeishuParam.getHtqyYear());
                    contractApplyParam.setContractTotalAmount(BigDecimal
                            .valueOf(Double.valueOf(Optional.ofNullable(contractExcelFeishuParam.getContractTotalAmount()).orElse("0").replaceAll(",", ""))));
                    contractApplyParam.setMyself(contractExcelFeishuParam.getMyself());
                    contractApplyParam.setContractType(contractExcelFeishuParam.getContractType());
                    contractApplyParam.setGift(contractExcelFeishuParam.getGift());
                    contractApplyParam.setYzTypeName(contractExcelFeishuParam.getYzTypeName());
                    contractApplyParam.setNote(contractExcelFeishuParam.getNote() == null ? "" : contractExcelFeishuParam.getNote());
                    contractApplyParam.setApplyUserName(contractExcelFeishuParam.getApplyUserName());
                    String strsDate = contractExcelFeishuParam.getApplyTime().split(" ")[0];
                    String strsTime = contractExcelFeishuParam.getApplyTime().split(" ")[1];
                    String[] strs = strsDate.split("/");
                    StringBuilder sb = new StringBuilder();
                    sb.append(strs[0]).append("-");
                    if (strs[1].length() == 1) {
                        sb.append("0");
                    }
                    sb.append(strs[1]).append("-");
                    if (strs[2].length() == 1) {
                        sb.append("0");
                    }
                    sb.append(strs[2]);
                    sb.append(" ").append(strsTime);
                    contractApplyParam.setApplyTime(sb.toString());
                    contractApplyParam.setSupplierName(contractExcelFeishuParam.getSupplierName());

                    //存储付款周期合并信息
                    contractApplyParam.setPayCycle(contractExcelFeishuParam.getPayCycle());
                    contractApplyParam.setYjDeadLine(contractExcelFeishuParam.getYjDeadLine());
                    contractApplyParam.setBzjDeadLine(contractExcelFeishuParam.getBzjDeadLine());
                    contractApplyParam.setFreeQx(contractExcelFeishuParam.getFreeQx());
                    contractApplyParam.setBzjAmount(contractExcelFeishuParam.getBzjAmount());
                    if (StringUtils.isNotBlank(contractExcelFeishuParam.getYjAmount())) {
                        contractApplyParam.setYjAmount(contractExcelFeishuParam.getYjAmount().split(" ")[0]);
                    }
                    if (StringUtils.isNotBlank(contractExcelFeishuParam.getBzjAmount())) {
                        contractApplyParam.setBzjAmount(contractExcelFeishuParam.getBzjAmount().split(" ")[0]);
                    }
                    contractApplyParam.setYjOrBzj(contractExcelFeishuParam.getYjOrBzj());
                    contractApplyParam.setIsAb(contractExcelFeishuParam.getIsAb());
                    contractApplyParam.setProxyName(contractExcelFeishuParam.getProxyName());
                    contractApplyParam.setContractMbType(contractExcelFeishuParam.getContractMbType());
                    contractApplyParam.setGdTime(contractExcelFeishuParam.getGdTime());
                    contractApplyParam.setPayCycleDesc(contractExcelFeishuParam.getPayCycleDesc());
                    contractApplyParam.setFirstAmount(contractExcelFeishuParam.getFirstAmount());
                    contractApplyParam.setFirstDeadLine(contractExcelFeishuParam.getFirstDeadLine());
                    contractApplyParam.setYjAddr(contractExcelFeishuParam.getYjAddr());
                    contractApplyParam.setGdCode(contractExcelFeishuParam.getGdCode());
                    contractApplyParam.setContractCodeCC(contractExcelFeishuParam.getContractCodeCC());
                    suppliers.add(contractExcelFeishuParam.getSupplierName());

                }
                if (StringUtils.isNotBlank(contractExcelFeishuParam.getProjectDetail())) {
                    //项目
                    ContractProjectImportParam project = new ContractProjectImportParam();
                    project.setProjectDetail(contractExcelFeishuParam.getProjectDetail());
                    project.setLypjApplyCode(contractExcelFeishuParam.getLypjApplyCode());
                    project.setAiLevel(contractExcelFeishuParam.getAiLevel());
                    project.setProjectName(contractExcelFeishuParam.getProjectName());
                    project.setCityNameNotUse(contractExcelFeishuParam.getCityNameNotUse());
                    project.setProjectAddr(contractExcelFeishuParam.getProjectAddr());
                    project.setProjectTypeNameNotUse(contractExcelFeishuParam.getProjectTypeNameNotUse());
                    project.setLevelName(contractExcelFeishuParam.getLevelName());
                    project.setSocreDetail(contractExcelFeishuParam.getSocreDetail());
                    project.setN1301(contractExcelFeishuParam.getN1301());
                    project.setMapPc(contractExcelFeishuParam.getMapPc());
                    project.setMapApp(contractExcelFeishuParam.getMapApp());
                    project.setN2357(contractExcelFeishuParam.getN2357());
                    project.setP132(contractExcelFeishuParam.getP132());
                    project.setPrice(BigDecimal.valueOf(Double.valueOf(contractExcelFeishuParam.getPrice())));
                    project.setPointNum(Integer.valueOf(contractExcelFeishuParam.getPointNum()));
                    project.setPointSpec(contractExcelFeishuParam.getPointSpec());
                    project.setPriceNotUse(contractExcelFeishuParam.getPriceNotUse());
                    project.setCity(contractExcelFeishuParam.getCity());
                    project.setProjectType(contractExcelFeishuParam.getProjectType());

                    BigDecimal year = contractApplyParam.getHtqyYear();
                    if (year != null) {
                        BigDecimal price = project.getPrice();
                        BigDecimal pointNum = BigDecimal.valueOf(Double.valueOf(project.getPointNum()));
                        BigDecimal amount = price.multiply(year);
                        amount = amount.multiply(pointNum);
                        project.setAmount(amount);
                    } else {
                        project.setAmount(contractApplyParam.getContractTotalAmount());
                    }

                    projects.add(project);
                }
                if (StringUtils.isNotBlank(contractExcelFeishuParam.getPayDtail())) {
                    //处理付款周期 只处理一个合同 一个项目 所以 周期直接对应上项目 读取信息到对象 后续王强使用其来生成支付周期 价格周期 台账
                    PayCycleInfo payCycleInfo = new PayCycleInfo();
                    payCycleInfo.setPayDtail(contractExcelFeishuParam.getPayDtail());
                    payCycleInfo.setPayQs(contractExcelFeishuParam.getPayQs());
                    payCycleInfo.setPayAmount(contractExcelFeishuParam.getPayAmount().split(" ")[0]);
                    payCycleInfo.setPayDeadLine(contractExcelFeishuParam.getPayDeadLine());
                    payCycleInfos.add(payCycleInfo);
                }
                if (StringUtils.isNotBlank(contractExcelFeishuParam.getFloorPoint())) {
                    FloorPoint floorPoint = new FloorPoint();
                    floorPoint.setFloorPoint(contractExcelFeishuParam.getFloorPoint());
                    floorPoint.setProjectName2(contractExcelFeishuParam.getProjectName2());
                    floorPoint.setFloor(contractExcelFeishuParam.getFloor());
                    floorPoint.setPointNum2(contractExcelFeishuParam.getPointNum2());
                    floorPoints.add(floorPoint);
                }
            } catch (Exception e) {
                log.error("dataimport contractExcelFeishuParam数据异常:{}", contractExcelFeishuParam.getContractCode(), e);
            }

        }
        map.put(ccCode, contractApplyParam);
        return map;
    }

    private List<ContractExcelOtherParam> dealOtherExcel(MultipartFile fileOther) throws IOException {
        List<ContractExcelOtherParam> contractExcelOtherParams = parseRow(fileOther.getInputStream());
        return contractExcelOtherParams;
    }

    private List<ContractExcelOtherParam> parseRow(InputStream inputStream) {
        List<ContractExcelOtherParam> datas = new ArrayList<>();
        EasyExcel.read(inputStream, ContractExcelOtherParam.class, new AnalysisEventListener<ContractExcelOtherParam>() {
            private int rowIndex = 0;

            @Override
            public void invoke(ContractExcelOtherParam data, AnalysisContext context) {
                rowIndex++;
                datas.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("dataimport Sheet 1 读取完成，总行数：{}", rowIndex);
            }
        }).sheet(0).doRead();
        return datas;
    }

    private List<ContractExcelFeishuParam> parseRowFeishu(InputStream inputStream) {
        List<ContractExcelFeishuParam> datas = new ArrayList<>();
        EasyExcel.read(inputStream, ContractExcelFeishuParam.class, new AnalysisEventListener<ContractExcelFeishuParam>() {
            private int rowIndex = 0;

            @Override
            public void invoke(ContractExcelFeishuParam data, AnalysisContext context) {
                rowIndex++;
                datas.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("dataimport Sheet 1 读取完成，总行数：{}", rowIndex);
            }
        }).sheet(0).doRead();
        return datas;
    }

    /**
     * 生成本地合同
     */
    @Transactional
    public void saveLocalContract() {
        try {
            noBusinessCode = new ArrayList<>();
            otherError = new ArrayList<>();
            //查询已有的合同 有了就不进行导入
            List<ContractEntity> contractEntities = contractService.lambdaQuery().select(ContractEntity::getOldContractCode,
                    ContractEntity::getContractCode).list();
            List<String> codecc = contractEntities.stream()
                    .map(contractEntity -> contractEntity.getOldContractCode()).collect(Collectors.toList());
            List<String> code = contractEntities.stream()
                    .map(contractEntity -> contractEntity.getContractCode()).collect(Collectors.toList());
            noProjectContract = contractMapper.findNoProjectContract();
            if (noProjectContract == null) {
                noProjectContract = new ArrayList<>();
            }
            //利用之前的数据
            List<Integer> ids = Lists.newArrayList();
            List<Integer> idsNotArc = Lists.newArrayList();
            for (ContractImportParam contractImportParam : contractImportParams) {
                //处理cms本地的合同入库
                if ((codecc.contains(contractImportParam.getContractCodeCC()) || code.contains(contractImportParam.getContractCode()))
                        && !noProjectContract.contains(contractImportParam.getContractCode())
                ) {
                    continue;
                }
                Integer id = dealLocalContract(contractImportParam);
                if (id == -1) {
                    continue;
                }
                //入库完了需要发送mq
                if ("已归档".equals(contractImportParam.getYgd())) {
                    ids.add(id);
                } else {
                    idsNotArc.add(id);
                }
            }
            for (Integer id : ids) {
                contractNotifyService.notifyOnStatusChanged(id, ContractApplyStatusEnum.APPROVED);
                contractNotifyService.notifyBusinessOnStatusChanged(id, "0043-6");
            }
            for (Integer id : idsNotArc) {
                contractNotifyService.notifyOnStatusChanged(id, ContractApplyStatusEnum.APPROVED);
                contractNotifyService.notifyBusinessOnStatusChanged(id, "0043-5");
            }
            log.error("dataimport 根据buildingno:{}未找到商机编码", JSON.toJSONString(noBusinessCode));
            log.error("dataimport 保存本地时其他错误:{}", JSON.toJSONString(otherError));
            noBusinessCode.clear();
            otherError.clear();
        } catch (Exception e) {
            log.error("dataimport saveLocalContract异常:{}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        } finally {
            noBusinessCode.clear();
            otherError.clear();
        }

    }

    /**
     * 生成申请编号
     * WYHTSQ+年月日+流水号（4位，从0001开始）
     * WYHTSQ202412010001
     */
    private String getApplyCode() {
        String today = DATE_FORMATTER.format(LocalDate.now());
        String cacheKey = "cms:venue:apply:code:" + today;
        Long index = redisTemplate.opsForValue().increment(cacheKey);
        redisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return String.format("WYHTSQ%s%04d", today, index);
    }


    public static void main(String[] args) {
//        LocalDate start = LocalDate.parse("2024/9/1",DateTimeFormatter.ofPattern("yyyy/M/d"));
//        LocalDate end = LocalDate.parse("2027/10/31",DateTimeFormatter.ofPattern("yyyy/M/d"));
//        // 计算两个日期之间的总天数
//        long daysBetween = ChronoUnit.DAYS.between(start, end);
//        BigDecimal years = BigDecimal.valueOf(daysBetween).divide(BigDecimal.valueOf(365.00),2,BigDecimal.ROUND_HALF_UP);
//        // 将总天数转换为年数，按一年 365 天计算
//        System.out.println(years);
//        List<Integer> list = Arrays.asList(1,2,3,4,5,6,7,8,9,10);
//        for (Integer i : list) {
//            try {
//                System.out.println("第"+i);
//                if(i==5){
//                    int x = 1/0;
//                }
//            }catch (Exception e){
//                System.out.println("异常了....");
//            }
//        }
        List<String> list = Splitter.on("-").splitToList("广东省-东莞市-");
        System.out.println(list.size());

    }

    public void importContractV2(MultipartFile fileFeishu, MultipartFile fileOther) throws IOException {
        noBuildingNo = new ArrayList<>();
        pointNotMatch = new ArrayList<>();
        userSearchError = new HashSet<>();
        changeOwnerError = Lists.newArrayList();
        contractImportParams = Lists.newArrayList();
        invalidList = Lists.newArrayList();
        invalidPointList = Lists.newArrayList();
        noBuildingNoParam = Lists.newArrayList();
        buildingNoMuti = Lists.newArrayList();
        //星光的台账excel
        List<ContractExcelOtherParam> contractExcelOtherParams = dealOtherExcel(fileOther);
        //飞书的excel处理 key为cccode
        Map<String, ContractImportParam> mapFeishu = dealFeishuExcel(fileFeishu);
        Map<String, ContractImportParam> mapOther = new HashMap<>();
        //处理信息合并
        for (ContractExcelOtherParam contractExcelOtherParam : contractExcelOtherParams) {
            try {
                String ccCode = contractExcelOtherParam.getContractCodeCC();
                if (mapOther.containsKey(ccCode)) {
                    //已经处理过该合同的第一个项目
                    ContractImportParam contractImportParam = mapOther.get(ccCode);
                    List<ContractProjectImportParam> projects = contractImportParam.getProjects();
                    ContractProjectImportParam project = new ContractProjectImportParam();
                    project.setProjectName(contractExcelOtherParam.getProjectName());
                    project.setCityProjectName(contractExcelOtherParam.getCityProjectName());
                    project.setPointNum(Integer.valueOf(contractExcelOtherParam.getPointNum()));
                    project.setAmount(contractExcelOtherParam.getContractTotalAmount());
                    project.setCity("省-" + (contractExcelOtherParam.getCityName().contains("市") ? contractExcelOtherParam.getCityName()
                            : contractExcelOtherParam.getCityName() + "市") + "-");
                    projects.add(project);
                    BigDecimal totalAmount = contractImportParam.getContractTotalAmount();
                    BigDecimal projectAmount = contractExcelOtherParam.getContractTotalAmount();
                    contractImportParam.setContractTotalAmount(BigDecimalUtils.add(totalAmount, projectAmount));
                    mapOther.put(ccCode, contractImportParam);
                } else {
                    //该合同的第一个项目
                    ContractImportParam contractImportParam = new ContractImportParam();
                    contractImportParam.setDevTypeName(contractExcelOtherParam.getDevTypeName());

                    if (mapFeishu.containsKey(contractExcelOtherParam.getContractCodeCC())) {
                        ContractImportParam contractImportParam1 = mapFeishu.get(contractExcelOtherParam.getContractCodeCC());
                        if ("代理".equals(contractExcelOtherParam.getDevTypeName())) {
                            //查找飞书代理商公司
                            contractImportParam.setProxyName(contractImportParam1.getProxyName());
                        }
                        //是否赠播
                        contractImportParam.setGift(contractImportParam1.getGift());
                        contractImportParam.setYzTypeName(contractImportParam1.getYzTypeName());
                        contractImportParam.setContractMbType(contractImportParam1.getContractMbType());

                    }
                    contractImportParam.setContractCodeCC(contractExcelOtherParam.getContractCodeCC());
                    contractImportParam.setContractCode(contractExcelOtherParam.getContractCode());
                    contractImportParam.setSupplierName(contractExcelOtherParam.getSupplierName());
                    contractImportParam.setYgd(StringUtils.isNotBlank(contractExcelOtherParam.getYgd()) ? contractExcelOtherParam.getYgd() : "未归档");
                    contractImportParam.setGdTime(contractExcelOtherParam.getGdDate());
                    contractImportParam.setHtqyYear(contractExcelOtherParam.getHtqyYear());
                    contractImportParam.setDevUserName(contractExcelOtherParam.getDevUserName());
                    contractImportParam.setBuzLeader(contractExcelOtherParam.getBuzLeader());
                    //跟进人
                    contractImportParam.setFollowerName(contractExcelOtherParam.getFollowerName());
                    //原始创建人 提交人也设置为创建人
                    contractImportParam.setCreatorName(contractExcelOtherParam.getOldDevUserName());

                    contractImportParam.setApplyTime(contractExcelOtherParam.getFqDate());
                    contractImportParam.setContractTotalAmount(contractExcelOtherParam.getContractTotalAmount());
                    //设置项目
                    List<ContractProjectImportParam> projects = Lists.newArrayList();
                    ContractProjectImportParam project = new ContractProjectImportParam();
                    project.setProjectName(contractExcelOtherParam.getProjectName());
                    project.setCityProjectName(contractExcelOtherParam.getCityProjectName());
                    project.setPointNum(Integer.valueOf(contractExcelOtherParam.getPointNum()));
                    project.setAmount(contractExcelOtherParam.getContractTotalAmount());
                    project.setCity("省-" + (contractExcelOtherParam.getCityName().contains("市") ? contractExcelOtherParam.getCityName()
                            : contractExcelOtherParam.getCityName() + "市") + "-");
                    project.setPrice(project.getAmount().divide(BigDecimal.valueOf(project.getPointNum()), 2, RoundingMode.HALF_UP).
                            divide(contractExcelOtherParam.getHtqyYear(), 2, RoundingMode.HALF_UP));
                    projects.add(project);
                    contractImportParam.setProjects(projects);
                    mapOther.put(ccCode, contractImportParam);
                }
            } catch (Exception e) {
                log.error("dataimport contractExcelOtherParam异常:{}", contractExcelOtherParam.getContractCodeCC(), e);
            }

        }
        contractImportParams.addAll(mapOther.values());
        for (ContractImportParam contractImportParam : mapOther.values()) {
            //根据项目名称+城市+区域 查询 buildingRatingNo->根据buildingRatingNo查询点位
            // ->比对这个点位和excel的点位数量是否一致 一致则正常处理，
            // 不一致则需要记录人工介入 || !ccCodeSetOther.contains(contractImportParam.getContractCodeCC())
            if (!checkPoint(contractImportParam)) {
                invalidList.add(contractImportParam);
                contractImportParams.remove(contractImportParam);
                continue;
            }
            //调用crm
            invokeCrmBusiness(contractImportParam);
        }
        log.error("dataimport 根据项目名称+城市+区域 未查询到 buildingRatingNo的数据:大小: {},数据:{}", noBuildingNo.size(), JSON.toJSONString(noBuildingNo));
        log.error("dataimport changeowner异常 ,大小:{},参数:{}", changeOwnerError.size(), JSON.toJSONString(changeOwnerError));
        log.error("dataimport 点位数据异常: {}", JSON.toJSONString(pointNotMatch));
        log.error("dataimport 用户查询异常: {}", JSON.toJSONString(userSearchError));
        log.error("dataimport buildingno多个: {}", JSON.toJSONString(buildingNoMuti));
        log.error("dataimport buildingno没找到: {}", JSON.toJSONString(noBuildingNoParam));
        if (invalidList != null && invalidList.size() > 0) {
            //异常数据排除 点位 以及 buildingno
            invalidList.removeAll(invalidPointList);
            invalidList.removeAll(noBuildingNoParam);
            invalidList.removeAll(buildingNoMuti);
        }
        log.error("dataimport 其他异常的数据: {}", JSON.toJSONString(invalidList));
        log.error("dataimport 正常的数据: {}", JSON.toJSONString(contractImportParams));
    }

    @Transactional
    public void importContractAbnormal(MultipartFile abnormal) throws IOException {
        List<ContractAbnormalParam> contractAbnormalParams = parseRowAbnormal(abnormal.getInputStream());
        //入库
        saveAbnormal(contractAbnormalParams);
    }


    private void saveAbnormal(List<ContractAbnormalParam> contractAbnormalParams) {
        if (CollectionUtils.isEmpty(contractAbnormalParams)) {
            return;
        }
        List<ContractEntity> contractEntities = new ArrayList<>();
        Map<String, String> projectCodeMap = projectService.lambdaQuery().list()
                .stream().collect(Collectors.toMap(p -> p.getContractId() + p.getProjectName(),
                        p -> p.getProjectCode(), (k1, k2) -> k1));
        Map<String, MutableTriple<ContractAbnormalEntity, List<ContractAbnormalProjectEntity>, List<ContractAbnormalDealEntity>>> map = new HashMap<>();
        for (ContractAbnormalParam contractAbnormalParam : contractAbnormalParams) {
            //查找合同id
            ContractEntity contractEntity = contractService.lambdaQuery().select(ContractEntity::getId)
                    .eq(ContractEntity::getContractCode, contractAbnormalParam.getContractCode())
                    .in(ContractEntity::getContractType, Arrays.asList(ContractTypeEnum.HISTORY.getCode(),
                            ContractTypeEnum.NORMAL.getCode(),
                            ContractTypeEnum.CHANGE.getCode()))
                    .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .one();
            Map<String,Integer> installMap = getProjectWithPointVOs(contractEntity.getId()).stream()
                    .collect(Collectors.toMap(ContractAbnormalProjectWithPointVO::getProjectName, ContractAbnormalProjectWithPointVO::getInstallCount));
            if (map.containsKey(contractAbnormalParam.getContractCode())) {
                //循环到的同一个合同的非第一个项目
                ContractAbnormalProjectEntity abnormalProjectEntity = contractAbnormalImportConvert
                        .toProjectEntity(contractAbnormalParam);
                abnormalProjectEntity.setCreator(contractAbnormalParam.getCreatorId());
                abnormalProjectEntity.setProjectCode(projectCodeMap.get(contractEntity.getId() + contractAbnormalParam.getProjectName()));
                abnormalProjectEntity.setInstallCount(installMap.getOrDefault(abnormalProjectEntity.getProjectName(), 0));
                List<ContractAbnormalProjectEntity> abnormalProjectEntities = map.get(contractAbnormalParam.getContractCode()).getMiddle();
                abnormalProjectEntities.add(abnormalProjectEntity);
                map.get(contractAbnormalParam.getContractCode()).setMiddle(abnormalProjectEntities);
            } else {
                List<ContractAbnormalProjectEntity> abnormalProjectEntities = new ArrayList<>();
                List<ContractAbnormalDealEntity> abnormalDealEntities = new ArrayList<>();
                //循环到的同一个合同的第一个项目
                ContractAbnormalEntity abnormalEntity = contractAbnormalImportConvert
                        .toEntity(contractAbnormalParam);
                abnormalEntity.setCreator(contractAbnormalParam.getCreatorId());
                abnormalEntity.setOperator(contractAbnormalParam.getCreatorId());
                abnormalEntity.setCreateTime(contractAbnormalParam.getApplyTime());
                abnormalEntity.setApplyTime(contractAbnormalParam.getApplyTime());
                abnormalEntity.setApplyCode(getContractAbnormalApplyCode());

                //导入增加一个0106-1的deal
                ContractAbnormalDealEntity abnormalDealEntityBd = new ContractAbnormalDealEntity();
                abnormalDealEntityBd.setType(AbContractDealTypeEnum.ABNORMAL_CONTRACT_ADD.getCode());
                //bd
                abnormalDealEntityBd.setCreator(contractAbnormalParam.getCreatorId());
                //申请时间
                abnormalDealEntityBd.setCreateTime(contractAbnormalParam.getApplyTime());
                abnormalDealEntityBd.setAbnormalId(abnormalEntity.getId());
                abnormalDealEntityBd.setVersion(1);
                abnormalDealEntities.add(abnormalDealEntityBd);


                if ("是".equals(contractAbnormalParam.getDealFlagName())) {
                    //生成一个type为0106-7 填写处理意见的deal
                    ContractAbnormalDealEntity abnormalDealEntity2 = new ContractAbnormalDealEntity();
                    abnormalDealEntity2.setType(AbContractDealTypeEnum.WRITE_HANDLE_RESULT.getCode());
                    //处理结果
                    abnormalDealEntity2.setRemark(contractAbnormalParam.getDealResult());
                    //处理人
                    abnormalDealEntity2.setCreator(contractAbnormalParam.getFinishUserId());
                    //完成时间
                    abnormalDealEntity2.setCreateTime(contractAbnormalParam.getFinishTime());
                    abnormalDealEntity2.setVersion(1);
                    abnormalDealEntities.add(abnormalDealEntity2);
                    abnormalEntity.setApplyStatus(ApplyStatusEnum.FINISH.getCode());
                    abnormalEntity.setHandler(contractAbnormalParam.getFinishUserId());
                    abnormalEntity.setHandleRemark(contractAbnormalParam.getDealResult());
                    abnormalEntity.setHandleTime(contractAbnormalParam.getFinishTime());
                    if (!DealTypeEnum.BUSINESS.getCode().equals(contractAbnormalParam.getDealType())) {
                        contractEntity.setAbnormalFlag(ContractAbnormalFlagEnum.ABNORMAL.getCode());
                        contractEntities.add(contractEntity);
                    }
                } else {
                    contractEntity.setAbnormalFlag(ContractAbnormalFlagEnum.ABNORMAL.getCode());
                    contractEntities.add(contractEntity);
                    abnormalEntity.setApplyStatus(ApplyStatusEnum.PROCESSING.getCode());
                }
                abnormalEntity.setAbnormalReason(contractAbnormalParam.getAbnormalReason());
                abnormalEntity.setAbnormalRemark(contractAbnormalParam.getAbnormalRemark());
                abnormalEntity.setDealType(contractAbnormalParam.getDealType());

                abnormalEntity.setContractId(contractEntity.getId());
                ContractAbnormalProjectEntity abnormalProjectEntity = contractAbnormalImportConvert
                        .toProjectEntity(contractAbnormalParam);
                abnormalProjectEntity.setCreator(contractAbnormalParam.getCreatorId());
                abnormalProjectEntity.setProjectCode(projectCodeMap.get(contractEntity.getId() + contractAbnormalParam.getProjectName()));
                abnormalProjectEntity.setInstallCount(installMap.getOrDefault(abnormalProjectEntity.getProjectName(), 0));

                abnormalProjectEntities.add(abnormalProjectEntity);
                ContractAbnormalDealEntity abnormalDealEntity = contractAbnormalImportConvert
                        .toDealEntity(contractAbnormalParam);
                //处理方式填写人 媒资负责人
                abnormalDealEntity.setCreator(contractAbnormalParam.getDealTypeCreatorId());
                abnormalDealEntity.setType(AbContractDealTypeEnum.MEDIA.getCode());
                abnormalDealEntity.setVersion(1);
                abnormalDealEntities.add(abnormalDealEntity);


                map.put(contractAbnormalParam.getContractCode(), MutableTriple.of(abnormalEntity, abnormalProjectEntities, abnormalDealEntities));
            }
        }
        // 后续优化数据库调用次数
        for (MutableTriple<ContractAbnormalEntity, List<ContractAbnormalProjectEntity>, List<ContractAbnormalDealEntity>> value : map.values()) {

            contractAbnormalService.save(value.getLeft());
            List<ContractAbnormalProjectEntity> projectEntities = value.getMiddle().stream().peek(contractAbnormalProjectEntity -> {
                contractAbnormalProjectEntity.setAbnormalId(value.getLeft().getId());
            }).toList();
            contractAbnormalProjectService.saveBatch(projectEntities);

            List<ContractAbnormalDealEntity> dealEntities = value.getRight().stream().peek(contractAbnormalDealEntity -> {
                contractAbnormalDealEntity.setAbnormalId(value.getLeft().getId());
            }).toList();
            contractAbnormalDealService.saveBatch(dealEntities);
        }
        // 更新原合同异常状态
        contractService.updateBatchById(contractEntities);

    }

    /**
     * 生成异常合同申请单编号
     * YCHT+年月日+流水号（4位，从0001开始）
     * YCHT202412010001
     */
    private String getContractAbnormalApplyCode() {
        String today = DATE_FORMATTER.format(LocalDate.now());
        String cacheKey = "cms:venue:abnormal:apply:code:" + today;
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        stringRedisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return String.format("YCHT%s%04d", today, index);
    }

    private List<ContractAbnormalParam> parseRowAbnormal(InputStream inputStream) {
        List<ContractAbnormalParam> datas = new ArrayList<>();
        EasyExcel.read(inputStream, ContractAbnormalParam.class, new AnalysisEventListener<ContractAbnormalParam>() {
            private int rowIndex = 0;

            @Override
            public void invoke(ContractAbnormalParam data, AnalysisContext context) {
                rowIndex++;
                datas.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("dataimport Sheet 1 读取完成，总行数：{}", rowIndex);
            }
        }).sheet(0).doRead();
        return datas;
    }
    @Transactional
    public void fixAbnormalCityRegion() {
        dataBoardMapper.updateAbnormalCity();
        dataBoardMapper.updateAbnormalRegion();
        dataBoardMapper.updateAbnormalProjectCity();
        dataBoardMapper.updateAbnormalProjectRegion();
    }

    /**
     * 查询项目点位信息
     *
     * @param contractId
     * @return
     */
    private List<ContractAbnormalProjectWithPointVO> getProjectWithPointVOs(Integer contractId){
        // 查询项目
        List<ContractProjectEntity> projects = contractProjectService.lambdaQuery().eq(ContractProjectEntity::getContractId, contractId).list();

        List<ContractAbnormalProjectWithPointVO> projectWithPointVOs = new ArrayList<>();
        for (ContractProjectEntity contractProjectEntity : projects) {
            ContractAbnormalProjectWithPointVO contractAbnormalProjectWithPointVO = contractAbnormalProjectConvert
                    .toAbnormalProjectWithPointVO(contractProjectEntity);

            //查询项目下的点位
            List<ContractDevicePointEntity> points = contractDevicePointService.lambdaQuery().eq(ContractDevicePointEntity::getProjectId,
                    contractProjectEntity.getId()).list();
            contractAbnormalProjectWithPointVO.setSignCount(points.size());

            List<String> codes = points.stream().map(ContractDevicePointEntity::getCode).toList();

            //SSP查询点位信息
            if (CollectionUtils.isNotEmpty(codes)) {
                ResultTemplate<Integer> response = feignSspRpc.countPointStatusByPointCode(codes);
                Integer pointCount = Optional.ofNullable(response).filter(ResultTemplate::getSuccess)
                        .map(ResultTemplate::getData).orElseGet(() -> {
                            log.warn("获取SSP点位统计数失败");
                            return 0;
                        });
                contractAbnormalProjectWithPointVO.setInstallCount(pointCount);
                projectWithPointVOs.add(contractAbnormalProjectWithPointVO);
            }
        }
        return projectWithPointVOs;
    }
}