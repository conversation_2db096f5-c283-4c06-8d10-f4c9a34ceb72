package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.tools.common.util.operatelog.ChangeExtract;
import com.coocaa.ad.cheese.cms.common.util.translate.VenueTransTypes;
import com.coocaa.ad.translate.anno.TransField;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 合同-项目转换DTO
 * @since 2025-02-25 11:48
 */
@Data
public class ContractProjectEntityConvertDTO extends ContractProjectEntity {
    @ChangeExtract(readableName = "物业类型", hide = true)
    @TransField(type = VenueTransTypes.DICT, target = "propertyTypeName")
    private String propertyType;
    @ChangeExtract(readableName = "物业类型")
    private String propertyTypeName;

    @ChangeExtract(readableName = "付款方式", hide = true)
    @TransField(type = VenueTransTypes.DICT, target = "paymentTypeName")
    private String paymentType;
    @ChangeExtract(readableName = "付款方式")
    private String paymentTypeName;
}
