package com.coocaa.ad.cheese.cms.venue.vo.contract;

/**
 * 填充城市
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-09
 */
public interface IFillingCity {
    /**
     * 合同ID
     */
    Integer getContractId();

    /**
     * 更新城市ID
     */
    default void setCityId(Integer cityId) {
    }


    /**
     * 更新城市名称
     */
    void setCityName(String cityName);

    /**
     * 更新大区名称
     */
    default void setRegionName(String regionName) {

    }

    /**
     * 设置大区id
     *
     * @param regionId
     */
    default void setRegionId(Integer regionId) {

    }

}
