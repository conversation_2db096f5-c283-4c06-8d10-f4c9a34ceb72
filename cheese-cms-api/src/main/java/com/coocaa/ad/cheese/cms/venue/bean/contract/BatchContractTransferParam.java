package com.coocaa.ad.cheese.cms.venue.bean.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.groups.Default;
import lombok.Data;

import java.util.List;

/**
 * 合同批量转交
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-12
 */
@Data
public class BatchContractTransferParam {

    @Schema(description = "合同ID集合", type = "List", example = "合同ID集合")
    @NotEmpty(message = "合同ID集合不能为空", groups = {Default.class})
    private List<Integer> contractIds;

    @NotNull(message = "跟进人不能为空")
    @Schema(description = "跟进人", type = "Integer", example = "跟进人")
    private Integer follower;

    @Schema(description = "跟进人姓名", type = "String", example = "跟进人姓名")
    private String followerName;
}
