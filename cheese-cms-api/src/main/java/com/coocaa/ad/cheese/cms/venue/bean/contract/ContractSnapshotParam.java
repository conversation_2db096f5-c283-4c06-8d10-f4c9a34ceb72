package com.coocaa.ad.cheese.cms.venue.bean.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合同快照数据参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-12 19:42
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "合同快照数据参数")
public class ContractSnapshotParam {
    @Schema(description = "来源类型(字典0092)", type = "String", example = "0092-1")
    @NotEmpty(message = "来源类型不能为空")
    private String sourceType;

    @Schema(description = "合同ID", type = "Int", example = "1")
    @NotNull(message = "合同ID不能为空")
    private Integer contractId;

    @Schema(description = "关联ID", type = "Int", example = "1")
    private Integer involvedId = 0;

    @Schema(description = "指定用户ID", type = "Int", hidden = true)
    private Integer userId = 0;

    @Schema(description = "流程实例code",  hidden = true)
    private String instanceCode;
}
