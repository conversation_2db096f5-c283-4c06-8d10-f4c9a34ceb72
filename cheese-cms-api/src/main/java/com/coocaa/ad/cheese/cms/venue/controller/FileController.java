package com.coocaa.ad.cheese.cms.venue.controller;

import com.coocaa.ad.cheese.cms.venue.bean.supplier.ImportResult;
import com.coocaa.ad.cheese.cms.venue.service.SupplierService;
import com.coocaa.ad.common.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 供应商文件模板管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02
 */
@Slf4j
@RestController
@RequestMapping("/file")
@Tag(name = "供应商管理", description = "供应商管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class FileController {
    private final SupplierService supplierService;

    /**
     * 上传文件模板
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文件模板")
    public ResultTemplate<String> uploadFile(@RequestParam(name = "file") MultipartFile file,
                                             @RequestParam(name = "fileName") String fileName) {
        ImportResult importResult = supplierService.uploadFile(file, fileName);
        if (importResult.isSuccess()) {
            return ResultTemplate.success(importResult.getFileUrl());
        } else {
            return ResultTemplate.fail(importResult.getMessage());
        }
    }
}
