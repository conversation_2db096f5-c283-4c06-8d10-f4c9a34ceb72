package com.coocaa.ad.cheese.cms.venue.controller;

import com.coocaa.ad.cheese.cms.venue.bean.comment.ConfigStatusParam;
import com.coocaa.ad.cheese.cms.venue.service.ConfigService;
import com.coocaa.ad.cheese.cms.venue.vo.ConfigVO;
import com.coocaa.ad.common.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-13
 */
@Slf4j
@RestController
@RequestMapping("/config")
@Tag(name = "系统配置", description = "系统配置")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ConfigController {
    private final ConfigService configService;

    /**
     * 刷新配置信息缓存
     */
    @Operation(summary = "刷新配置信息缓存")
    @PutMapping
    public ResultTemplate<Boolean> refreshConfigCache() {
        return ResultTemplate.success(configService.refreshCache());
    }

    /**
     * 获取配置信息
     */
    @Operation(summary = "获取配置信息")
    @Parameter(name = "code", description = "配置编码", required = true, example = "city_watermark_price")
    @GetMapping("/{code}")
    public ResultTemplate<ConfigVO> getConfig(@PathVariable(name = "code") String code) {
        return ResultTemplate.success(configService.getConfig(code));
    }

    /**
     * 获取配置信息
     */
    @Operation(summary = "根据父编码获取配置信息")
    @Parameter(name = "code", description = "父配置编码", required = true, example = "city_watermark_price")
    @GetMapping("/parent/{code}")
    public ResultTemplate<List<ConfigVO>> listConfigs(@PathVariable(name = "code") String code) {
        return ResultTemplate.success(configService.listConfigByParent(code));
    }

    /**
     * 修改启用禁用状态
     */
    @Operation(summary = "修改启用禁用状态")
    @Parameter(name = "code", description = "配置编码", required = true, example = "auto-audit")
    @Parameter(name = "status", description = "配置状态 [0:禁用, 1:启用]", required = true, example = "0")
    @PutMapping("/{code}")
    public com.coocaa.ad.common.result.ResultTemplate<Boolean> updateConfigStatus(@PathVariable(name = "code") String code,
                                                                                  @Validated @RequestBody ConfigStatusParam param) {
        return com.coocaa.ad.common.result.ResultTemplate.success(configService.updateConfigStatus(code, param));
    }

    /**
     * 新建或修改配置
     */
    @Operation(summary = "新建或修改配置")
    @PostMapping
    public ResultTemplate<Boolean> createOrUpdateConfig(@RequestBody ConfigVO config) {
        return ResultTemplate.success(configService.createOrUpdate(config));
    }
}
