package com.coocaa.ad.cheese.cms.venue.bean.contract;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.EncryptDeserializer;
import com.coocaa.ad.cheese.cms.common.validation.ValidationGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.groups.Default;
import lombok.Data;

import java.util.List;

/**
 * 合同-供应商 参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04
 */
@Data
public class ContractSupplierParam {
    @NotNull(message = "供应商ID不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "供应商ID", type = "Int", example = "1")
    private Integer supplierId;

    @Schema(description = "供应商名称", type = "String", example = "深圳市XX广告有限公司")
    @NotBlank(message = "供应商名称不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    private String supplierName;

    @NotBlank(message = "供应商联系人不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Size(max = 50, message = "联系人长度不能超过50个字符", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "联系人姓名", type = "String", example = "张三")
    private String contactPerson;

    @JSONField(deserializeUsing = EncryptDeserializer.class)
    @Schema(description = "联系电话", type = "String", example = "13800138000")
    private String contactPhone;

    @JSONField(deserializeUsing = EncryptDeserializer.class)
    @Schema(description = "供应商联系邮箱", type = "String", example = "<EMAIL>")
    private String contactEmail;

    // @JSONField(deserializeUsing = EncryptDeserializer.class)
    @Schema(description = "供应商联系地址", type = "String", example = "广东省深圳市南山区科技园")
    @NotBlank(message = "供应商联系人不能为空", groups = {Default.class})
    private String contactAddress;

    @Valid
    @NotEmpty(message = "供应商银行账户不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "供应商银行账户列表")
    private List<String> bankAccountCodes;
}
