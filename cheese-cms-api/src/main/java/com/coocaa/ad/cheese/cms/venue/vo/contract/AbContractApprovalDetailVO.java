package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.util.translate.VenueTransTypes;
import com.coocaa.ad.translate.anno.TransField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24
 */
@Data
public class AbContractApprovalDetailVO {

    @Schema(description = "异常合同id")
    private Integer abContractId;

    @Schema(description = "申请单号")
    private String abContractCode;

    @Schema(description = "原合同加密id")
    private String encryptId;

    @Schema(description = "原合同id")
    private Integer contractId;

    @Schema(description = "原合同编号")
    private String code;

    @Schema(description = "原合同类型 1 默认 2 变更 3 导入历史 4 补充协议")
    private Integer contractType;

    @Schema(description = "供应商")
    private String agentName;

    @Schema(description = "合同年限")
    private BigDecimal period;

    @Schema(description = "合同总金额(元)")
    private BigDecimal totalAmount;

    @Schema(description = "申请人")
    @TransField(type = VenueTransTypes.USER, target = "creatorName")
    private Integer creator;
    private String creatorName;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime applyTime;

    @Schema(description = "跟进人")
    @TransField(type = VenueTransTypes.USER, target = "followerName")
    private Integer follower;
    private String followerName;

    @Schema(description = "总异常点位数")
    private Integer abnormalCount;

    @Schema(description = "状态(字典0102)")
    @TransField(type = VenueTransTypes.DICT, target = "applyStatusName")
    private String applyStatus;
    private String applyStatusName;

    @Schema(description = "付款回票情况")
    private List<ContractAbnormalProjectVO> contractAbnormalProjects;

    @Schema(description = "异常类型(字典0099)")
    @TransField(type = VenueTransTypes.DICT, target = "typeName")
    private String type;
    private String typeName;

    @Schema(description = "异常说明")
    private String abnormalRemark;

    @Schema(description = "异常原因(字典0101)")
    @TransField(type = VenueTransTypes.DICT, target = "abnormalReasonName")
    private String abnormalReason;
    private String abnormalReasonName;

    @Schema(description = "当前审批节点code")
    private String nodeCode;

    @Schema(description = "处理意见")
    private List<ContractAbnormalDealVO> contractAbnormalDeals;

    @Schema(description = "附件")
    private List<ContractAttachmentVO> contractAttachments;

    @Schema(description = "审批流程")
    private List<ExamineApproveVO> examineApproves;
}
