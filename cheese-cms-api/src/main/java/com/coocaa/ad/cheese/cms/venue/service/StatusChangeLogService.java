package com.coocaa.ad.cheese.cms.venue.service;

import cn.hutool.json.JSONUtil;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.StatusChangeLogEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IStatusChangeLogService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.StatusChangeLogParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.StatusChangeLogConvert;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.ad.common.user.bean.CachedUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * 合同状态变更记录
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-31
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class StatusChangeLogService {
    private static final String LOG_TYPE = "0042-5";
    private static final int LOG_SUB_TYPE = 0;

    private final UserCacheHelper userCacheHelper;
    private final IStatusChangeLogService statusChangeLogService;

    /**
     * 记录合同申请单的状态变更过程
     *
     * @param contractId 合同ID
     * @param applyCode  合同申请编码
     * @param statusCode 状态码 (来自字典)
     * @return true:记录成功
     */
    public boolean recordContractStatusChange(Integer contractId, String applyCode, String statusCode) {
        Integer userId = Optional.ofNullable(UserThreadLocal.getUserId()).orElse(0);

        StatusChangeLogEntity entity = new StatusChangeLogEntity()
                .setType(LOG_TYPE).setSubType(LOG_SUB_TYPE)
                .setBizId(contractId).setBizCode(applyCode)
                .setStatus(statusCode)
                .setChangeTime(LocalDateTime.now())
                .setOperator(userId)
                .setDeleteFlag(BooleFlagEnum.NO.getCode());

        // 更新用户相关信息
        CachedUser user = userCacheHelper.getUser(userId);
        if (Objects.nonNull(user)) {
            entity.setOperatorWno(user.getWno());
            entity.setOperatorName(user.getName());
        }

        log.info("记录合同申请单({})的状态({})变更过程", contractId, statusCode);
        return statusChangeLogService.save(entity);
    }

    public void saveChangeLogAsync(StatusChangeLogParam param) {
        log.info("cms状态变更: {}", JSONUtil.toJsonStr(param));
        StatusChangeLogEntity entity = StatusChangeLogConvert.INSTANCE.toEntity(param);
        statusChangeLogService.save(entity);
    }
}
