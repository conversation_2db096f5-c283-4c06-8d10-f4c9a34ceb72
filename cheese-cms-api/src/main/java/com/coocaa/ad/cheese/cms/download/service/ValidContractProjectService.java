package com.coocaa.ad.cheese.cms.download.service;

import com.alibaba.fastjson2.JSONObject;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.download.handler.CmsSysTypeHandlerService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.service.ContractExportService;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.ad.downloader.bean.dto.AttachmentDTO;
import com.coocaa.ad.downloader.core.AbstractDownloaderProcessor;
import com.coocaa.ad.downloader.enums.IDownloaderType;
import com.coocaa.ad.downloader.rpc.FeignAttachmentRpc;
import com.coocaa.ad.downloader.rpc.FeignTaskRpc;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 有效合同-按照项目导出
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/9
 */
@Slf4j
@Service
public class ValidContractProjectService extends AbstractDownloaderProcessor {

    private final ContractExportService contractExportService;

    public ValidContractProjectService(RedissonClient redissonClient, FeignTaskRpc taskRpc,
                                       FeignAttachmentRpc attachmentRpc,
                                       UserCacheHelper userCacheHelper,
                                       CmsSysTypeHandlerService cmsSysTypeHandlerService,
                                       ContractExportService contractExportService) {
        super(redissonClient, taskRpc, attachmentRpc, userCacheHelper, cmsSysTypeHandlerService);
        this.contractExportService = contractExportService;
    }

    @Override
    protected IDownloaderType getProcessorType() {
        return DownLoadTypeEnum.YXHT_PROJECT;
    }

    @Override
    protected List<AttachmentDTO> doProcess(String executeParams) {
        ContractQueryParam queryParam = JSONObject.parseObject(executeParams, ContractQueryParam.class);
        String url = contractExportService.exportContractByProject(queryParam);
        return List.of(new AttachmentDTO().setName(DownLoadTypeEnum.YXHT_PROJECT.getDesc()).setUrl(url));
    }
}
