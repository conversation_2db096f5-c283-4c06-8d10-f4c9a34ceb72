package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.tools.common.util.operatelog.ChangeExtract;
import com.coocaa.ad.cheese.cms.common.util.translate.VenueTransTypes;
import com.coocaa.ad.translate.anno.TransField;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 合同实体和转换DTO
 * @since 2025-02-25 11:18
 */
@Data
public class ContractEntityConvertDTO extends ContractEntity {
    @ChangeExtract(readableName = "合作模式")
    private String cooperateTypeName;

    @ChangeExtract(readableName = "业务类型")
    private String businessTypeName;

    @ChangeExtract(readableName = "发票类型(字典0044)", hide = true)
    @TransField(type = VenueTransTypes.DICT, target = "invoiceTypeName")
    private String invoiceType;
    @ChangeExtract(readableName = "发票类型")
    private String invoiceTypeName;

    @ChangeExtract(readableName = "税点(字典0045)", hide = true)
    @TransField(type = VenueTransTypes.DICT, target = "taxPointName")
    private String taxPoint;
    @ChangeExtract(readableName = "税点")
    private String taxPointName;
}
