package com.coocaa.ad.cheese.cms.venue.rpc.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 审批任务列表查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-04
 */
@Data
public class ApprovalTaskUserListParam {

    /**
     * 不能为空
     */
    @Schema(description = "审批类型")
    @NotBlank(message = "审批类型不能为空")
    private String approveType;

    /**
     * 可以不传，默认不查询节点名称
     */
    @Schema(description = "是否查询节点名称")
    private Boolean nodeNameFlag = false;

    /**
     * 可以不传，默认查询待办
     */
    @Schema(description = "状态，1:待办，2：已办")
    @NotBlank(message = "状态不能为空")
    private Integer status;
} 