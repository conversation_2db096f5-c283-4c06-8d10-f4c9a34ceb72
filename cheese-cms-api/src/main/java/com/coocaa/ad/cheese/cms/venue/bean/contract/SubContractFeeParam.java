package com.coocaa.ad.cheese.cms.venue.bean.contract;

import com.coocaa.ad.cheese.cms.common.validation.ValidationGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-05
 */
@Data
public class SubContractFeeParam {
    @NotNull(message = "费用类型不能为空")
    @Schema(description = "费用类型(字典0031)", type = "String", example = "1")
    private String feeType;

    @NotNull(message = "发票类型不能为空", groups = {ValidationGroup.ContractAmendment.class, ValidationGroup.ContractModify.class})
    @Schema(description = "发票类型(字典0044)", type = "String", example = "0040-1")
    private String invoiceType;

    @NotNull(message = "税点不能为空", groups = {ValidationGroup.ContractAmendment.class, ValidationGroup.ContractModify.class})
    @Schema(description = "税点(字典0045)", type = "String", example = "0040-1")
    private String taxPoint;

    @Valid
    @Schema(description = "子合同价格周期列表")
    @NotEmpty(message = "子合同价格周期不能为空")
    private List<ContractPricePeriodParam> pricePeriods;

    @Valid
    @Schema(description = "子合同付款周期列表")
    @NotEmpty(message = "子合同付款周期不能为空")
    private List<ContractPaymentPeriodParam> paymentPeriods;
}
