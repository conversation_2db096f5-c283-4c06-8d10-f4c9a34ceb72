package com.coocaa.ad.cheese.cms.venue.vo.comment;

import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/6
 */
@Data
public class CommentAttachmentVO {
    @Schema(description = "附件ID", type = "integer", example = "1")
    private Integer id;

    @Schema(description = "附件类型", type = "integer", example = "1")
    private Integer type;
    private String typeName;

    @Schema(description = "附件名称", type = "string", example = "合同文件2024")
    private String name;

    @Schema(description = "附件全路径", type = "string", example = "https://example.com/files/contract.pdf")
    private String url;

    @Schema(description = "大小(字节)", type = "long", format = "int64", example = "1024000")
    private Long size;

    @Schema(description = "文件类型 (pdf, doc,...)", type = "string", example = "pdf")
    private String fileType;

    @Schema(description = "创建时间", type = "String", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;
}
