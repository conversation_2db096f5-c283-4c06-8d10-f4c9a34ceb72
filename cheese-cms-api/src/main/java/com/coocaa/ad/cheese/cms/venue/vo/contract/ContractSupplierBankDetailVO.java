package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.DesensitizeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 合同供应商银行信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10
 */
@Data
public class ContractSupplierBankDetailVO {
    @Schema(description = "供应商的银行ID", type = "Int", example = "1")
    private Integer id;

    @Schema(description = "开户银行", type = "String", example = "中国工商银行")
    private String bankName;

    @Schema(description = "收款户名", type = "String", example = "王三")
    private String accountName;

    @JSONField(serializeUsing = DesensitizeSerializer.class)
    @Schema(description = "收款账号", type = "String", example = "6222021234567890123")
    private String accountCode;
}
