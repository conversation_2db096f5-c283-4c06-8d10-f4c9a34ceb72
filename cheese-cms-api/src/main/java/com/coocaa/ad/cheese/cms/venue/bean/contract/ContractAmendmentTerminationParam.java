package com.coocaa.ad.cheese.cms.venue.bean.contract;

import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/13
 */
@Data
public class ContractAmendmentTerminationParam {

    @Schema(description = "是否付款 [0:否, 1:是]", type = "Int", example = "0")
    private Integer paid;

    @Schema(description = "已付款金额（元）", type = "BigDecimal", example = "10000.00")
    private BigDecimal paidAmount;

    @Schema(description = "安装情况 [0:未安装, 1:已安装]", type = "Int", example = "0")
    private Integer installFlag;

    @Schema(description = "已安装单位数", type = "Int", example = "1")
    private Integer installedUnit;

    @Schema(description = "未安装单位数", type = "Int", example = "1")
    private Integer uninstalledUnit;

    @Schema(description = "履约情况备注", type = "String", example = "履约情况备注")
    private String performanceRemark;

    @Schema(description = "终止协议类型 [1:合作方违约, 2:我方原因, 3:协商一致, 9:其他原因]", type = "Int", example = "1")
    private Integer terminationType;

    @Schema(description = "终止原因", type = "String", example = "终止原因")
    private String terminationReason;

    @Schema(description = "终止日期", type = "String", example = "2024-01-01")
    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    private LocalDate terminationDate;

    @Schema(description = "终止后业务安排", type = "String", example = "终止后业务安排")
    private String followUpPlan;

    @Schema(description = "涉及退款[0:否, 1:是]", type = "Boolean", example = "false")
    private Integer refunded;

    @Schema(description = "退款金额（元）", type = "BigDecimal", example = "10000.00")
    private BigDecimal refundAmount;

    @Schema(description = "退款付款日期", type = "String", example = "2024-01-01")
    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    private LocalDate refundPaymentDate;

    @Schema(description = "涉及赔偿[0:否, 1:是]", type = "Boolean", example = "false")
    private Integer compensated;

    @Schema(description = "赔偿金额（元）", type = "BigDecimal", example = "10000.00")
    private BigDecimal compensationAmount;

    @Schema(description = "赔偿付款日期", type = "String", example = "2024-01-01")
    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    private LocalDate compensationPaymentDate;

    @Schema(description = "终止后业务备注", type = "String", example = "终止后业务备注")
    private String followUpRemark;
}
