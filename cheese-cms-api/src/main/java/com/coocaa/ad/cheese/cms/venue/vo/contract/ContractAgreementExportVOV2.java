package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.coocaa.ad.cheese.cms.common.serializer.LocalDateTimeSerializer;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 补充协议-按照合同
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-10
 */
@Data
public class ContractAgreementExportVOV2 {

    @ExcelProperty(value = "变更申请编号")
    private String applyCode;

    @ExcelProperty(value = "合同编号")
    private String contractCode;

    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ExcelProperty(value = "物业类型")
    private String propertyTypeName;

    @ExcelProperty(value = "所属大区")
    private String region;

    @ExcelProperty(value = "城市")
    private String cityName;

    @ExcelProperty(value = "楼宇等级")
    private String buildingLevel;

    @ExcelProperty(value = "楼宇AI等级")
    private String aiBuildingLevel;

    @ExcelProperty(value = "终止协议")
    private String stopFlagName;

    @ExcelProperty(value = "标准合同")
    private String normalFlagName;

    @ExcelProperty(value = "审批状态")
    private String applyStatusName;

    @ExcelProperty(value = "执行状态")
    private String formalStatusName;

    @ExcelProperty(value = "供应商1")
    private String supplierName1;

    @ExcelProperty(value = "供应商2")
    private String supplierName2;

    @ExcelProperty(value = "押金")
    private BigDecimal depositAmount;

    @ExcelProperty(value = "发票类型")
    private String invoiceTypeName;

    @ExcelProperty(value = "税点")
    private String taxPointName;

    @ExcelProperty(value = "业务类型")
    private String businessTypeName;

    @ExcelProperty(value = "公司")
    private String agentName;

    @ExcelProperty(value = "是否大屏")
    private String largeScreen;

    @ExcelProperty(value = "尺寸规格")
    private String sizeSpec;

    @ExcelProperty(value = "合同总金额(元)")
    private BigDecimal totalAmount;

    @ExcelProperty(value = "合同开始时间")
    @DateTimeFormat(VenueConstants.DATE_FORMAT)
    private LocalDate startDate;

    @ExcelProperty(value = "合同结束时间")
    @DateTimeFormat(VenueConstants.DATE_FORMAT)
    private LocalDate endDate;

    @ExcelProperty(value = "合同年限")
    private BigDecimal period;

    @ExcelProperty(value = "签约点位数")
    private Integer signCount;

    @ExcelProperty(value = "付款要求")
    private String claimName;

    @ExcelProperty(value = "免租期")
    private String freeFlagName;

    @ExcelProperty(value = "免租期开始日期")
    private LocalDate freeStartDate;

    @ExcelProperty(value = "免租期结束日期")
    private LocalDate freeEndDate;

    @ExcelProperty(value = "是否归档")
    private String archiveFlagName;

    @ExcelProperty(value = "归档时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime archiveTime;

    @ExcelProperty(value = "是否生效")
    private String effectFlagName;

    @ExcelProperty(value = "申请时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime applyTime;

    @ExcelProperty(value = "审批通过时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime approveTime;

    @ExcelProperty(value = "申请人")
    private String creatorName;

    @ExcelProperty(value = "跟进人")
    private String followerName;
}
