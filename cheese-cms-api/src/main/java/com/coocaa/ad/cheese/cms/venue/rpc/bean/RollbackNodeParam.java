package com.coocaa.ad.cheese.cms.venue.rpc.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/24
 */
@Data
public class RollbackNodeParam {

    /**
     * 必传
     */
    @Schema(description = "节点唯一标识")
    private String nodeKey;

    /**
     * 必传
     */
    @Schema(description = "发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
