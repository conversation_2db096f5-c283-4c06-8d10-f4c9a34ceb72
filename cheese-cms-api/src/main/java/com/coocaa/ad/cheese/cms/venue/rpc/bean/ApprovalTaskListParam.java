package com.coocaa.ad.cheese.cms.venue.rpc.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 审批任务列表查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-04
 */
@Data
public class ApprovalTaskListParam {

    @Schema(description = "审批类型,销售合同：saleContract")
    @NotBlank(message = "审批类型不能为空")
    private String approveType;

    @Schema(description = "默认100，最大值200")
    private Integer pageSize = 100;

    @Schema(description = "分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时返回新的page_token,下次遍历可采用该page_token获取查询结果")
    private String pageToken;

    @Schema(description = "排序，0：按任务更新时间倒排，1：按任务更新时间正排，2：按任务开始时间倒排，3：按任务开始时间正排")
    private Integer order = 0;

    @Schema(description = "多个状态，PENDING：审批中，REJECTED：拒绝，APPROVED：通过，TRANSFERRED：转交，DONE：已完成，PROCESSED：已处理")
    private List<String> taskStatus;

    /**
     * 可以不传，默认不查询节点名称
     */
    @Schema(description = "是否查询节点名称")
    private Boolean nodeNameFlag = false;

    /**
     * 可以不传，默认只查询当前用户
     */
    @Schema(description = "是否根据用户查询")
    private Boolean userFlag = true;
} 