package com.coocaa.ad.cheese.cms.venue.service.helper;

import com.alibaba.nacos.api.model.v2.Result;
import com.alibaba.nacos.shaded.com.google.common.collect.Sets;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.LedgerEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.ILedgerService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierService;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.PaymentTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.PropertyTypeEnum;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethH5Rpc;
import com.coocaa.ad.cheese.cms.venue.util.MehtUtils;
import com.coocaa.ad.cheese.cms.venue.vo.building.BuildingGeneVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDeviceDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPaymentPeriodDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPriceApplyDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractProjectDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSupplierBankDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSupplierDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.SubContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.SubContractFeeDetailVO;
import com.coocaa.ad.common.core.util.SecurityUtils;
import com.coocaa.ad.translate.TranslatorFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 合同申请服务Helper
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-14 11:33
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractApplyServiceHelper {
    private final ContractConvert contractConvert;
    private final TranslatorFactory translatorFactory;
    private final IContractSupplierService contractSupplierService;
    private final IContractSupplierBankService contractSupplierBankService;
    private final ISupplierService supplierService;
    private final ISupplierBankService supplierBankService;
    private final IContractProjectService projectService;
    private final IContractPaymentPeriodService paymentPeriodService;
    private final FeignMethH5Rpc feignMethH5Rpc;
    private final ILedgerService ledgerService;
    private final MehtUtils mehtUtils;

    private static final Set<Integer> CONTRACT_CHANGE_TYPES = Set.of(ContractTypeEnum.AGREEMENT.getCode(), ContractTypeEnum.AMENDMENT.getCode());

    /**
     * 填充主合同的供应商信息
     */
    public void fillingMainContractSuppliers(ContractEntity contract, boolean expand, Consumer<List<ContractSupplierDetailVO>> supplierConsumer) {
        // 查询合同-供应商
        List<ContractSupplierEntity> suppliers = contractSupplierService.lambdaQuery()
                .eq(ContractSupplierEntity::getSubContractId, 0)
                .eq(ContractSupplierEntity::getContractId, contract.getId()).list();
        if (CollectionUtils.isEmpty(suppliers)) return;

        // 查询合同-供应商-银行信息
        Map<Integer, List<String>> contractSupplierBankMap = contractSupplierBankService.lambdaQuery()
                .eq(ContractSupplierBankEntity::getSubContractId, 0)
                .eq(ContractSupplierBankEntity::getContractId, contract.getId()).list().stream()
                .collect(Collectors.groupingBy(ContractSupplierBankEntity::getSupplierId, Collectors.mapping(ContractSupplierBankEntity::getBankAccountCode, Collectors.toList())));

        // 查询供应商-银行信息
        Map<String, ContractSupplierBankDetailVO> supplierBankMap = Maps.newHashMap();
        Set<Integer> supplierIds = suppliers.stream().map(ContractSupplierEntity::getSupplierId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, String> supplierMap = supplierService.lambdaQuery()
                .in(SupplierEntity::getId, supplierIds).list().stream()
                .collect(Collectors.toMap(SupplierEntity::getId, SupplierEntity::getSupplierName));
        supplierBankMap.putAll(supplierBankService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(supplierIds), SupplierBankEntity::getSupplierId, supplierIds).list()
                .stream()
                .collect(Collectors.toMap(item -> String.format("%s|%s", item.getSupplierId(), item.getAccountNo()), contractConvert::toDetailVO)));

        // 返回合同的供应商信息
        supplierConsumer.accept(getContractSuppliers(expand, suppliers, contractSupplierBankMap, supplierMap, supplierBankMap));
    }

    /**
     * 获取合同供应商信息
     */
    private List<ContractSupplierDetailVO> getContractSuppliers(boolean expand, List<ContractSupplierEntity> suppliers,
                                                                Map<Integer, List<String>> contractSupplierBankMap,
                                                                Map<Integer, String> supplierMap,
                                                                Map<String, ContractSupplierBankDetailVO> supplierBankMap) {
        if (CollectionUtils.isEmpty(suppliers) || MapUtils.isEmpty(contractSupplierBankMap)) {
            return Collections.emptyList();
        }

        return suppliers.stream().map(supplier -> {
            ContractSupplierDetailVO vo = contractConvert.toEditDetailVO(supplier);
            vo.setSupplierName(supplierMap.getOrDefault(supplier.getSupplierId(), vo.getSupplierName()));
            List<String> bankAccountCodes = contractSupplierBankMap.get(supplier.getSupplierId());
            if (CollectionUtils.isNotEmpty(bankAccountCodes)) {
                List<String> codes = bankAccountCodes.stream().distinct().toList();
                if (expand) {
                    vo.setBanks(codes.stream()
                            .map(code -> supplierBankMap.get(String.format("%s|%s", supplier.getSupplierId(), code)))
                            .filter(Objects::nonNull).toList());
                } else {
                    vo.setBankAccountCodes(codes.stream()
                            .filter(code -> Objects.nonNull(supplierBankMap.get(String.format("%s|%s", supplier.getSupplierId(), code))))
                            .filter(StringUtils::isNotBlank)
                            .map(SecurityUtils::encryptToFront)
                            .toList());
                }
            }
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 填充子合同的供应商信息
     */
    public void fillingSubContractSuppliers(ContractEntity contract, boolean expand, Supplier<List<ContractProjectDetailVO>> projectSupplier) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) return;

        // 查询子合同-供应商, key:项目ID, value: 供应商列表
        Set<Integer> supplierIds = Sets.newHashSet();
        Set<Integer> projectIds = projects.stream().map(ContractProjectDetailVO::getId).collect(Collectors.toSet());
        Map<Integer, List<ContractSupplierEntity>> supplierMap = contractSupplierService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(supplierIds), ContractSupplierEntity::getProjectId, projectIds)
                .eq(ContractSupplierEntity::getContractId, contract.getId()).list().stream()
                .peek(supplier -> supplierIds.add(supplier.getSupplierId()))
                .collect(Collectors.groupingBy(ContractSupplierEntity::getProjectId));
        if (MapUtils.isEmpty(supplierMap)) return;

        // 查询合同-供应商-银行信息,  key:子合同ID, value: 供应商ID -> 银行账号列表
        Map<Integer, Map<Integer, List<String>>> contractSupplierBankMap = contractSupplierBankService.lambdaQuery()
                .gt(ContractSupplierBankEntity::getSubContractId, 0)
                .eq(ContractSupplierBankEntity::getContractId, contract.getId())
                .in(ContractSupplierBankEntity::getSupplierId, supplierIds).list().stream()
                .collect(Collectors.groupingBy(ContractSupplierBankEntity::getSubContractId, Collectors.groupingBy(ContractSupplierBankEntity::getSupplierId, Collectors.mapping(ContractSupplierBankEntity::getBankAccountCode, Collectors.toList()))));

        // 查询供应商-银行信息
        Map<String, ContractSupplierBankDetailVO> supplierBankMap = Maps.newHashMap();
        Map<Integer, String> supplier2Map = supplierService.lambdaQuery()
                .in(SupplierEntity::getId, supplierIds).list().stream()
                .collect(Collectors.toMap(SupplierEntity::getId, SupplierEntity::getSupplierName));
        supplierBankMap.putAll(supplierBankService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(supplierIds), SupplierBankEntity::getSupplierId, supplierIds).list()
                .stream()
                .collect(Collectors.toMap(item -> String.format("%s|%s", item.getSupplierId(), item.getAccountNo()), contractConvert::toDetailVO)));

        // 返回合同的供应商信息
        for (ContractProjectDetailVO project : projects) {
            if (CollectionUtils.isEmpty(project.getSubContracts())) continue;

            // 找到项目的所有供应商
            Map<Integer, List<ContractSupplierEntity>> subSupplierMap = supplierMap.getOrDefault(project.getId(), Collections.emptyList())
                    .stream().collect(Collectors.groupingBy(ContractSupplierEntity::getSubContractId));
            if (MapUtils.isEmpty(subSupplierMap)) continue;
            for (SubContractDetailVO subContract : project.getSubContracts()) {
                subContract.setSuppliers(getContractSuppliers(expand, subSupplierMap.get(subContract.getId()),
                        contractSupplierBankMap.getOrDefault(subContract.getId(), Collections.emptyMap()), supplier2Map, supplierBankMap));
            }
        }
    }

    /**
     * 填充项目信息
     */
    public void fillingProjects(ContractEntity contract, Consumer<List<ContractProjectDetailVO>> projectConsumer) {
        List<ContractProjectEntity> projects = projectService.lambdaQuery()
                .eq(ContractProjectEntity::getContractId, contract.getId()).list();
        if (CollectionUtils.isEmpty(projects)) return;

        // 项目列表
        List<ContractProjectDetailVO> projectVos = projects.stream()
                .peek(project -> mehtUtils.fixProjectInfo(project, null, Boolean.FALSE))
                .map(contractConvert::toEditDetailVO).toList();

        List<String> buildingNos = projectVos.stream().filter(item -> StringUtils.isNotEmpty(item.getProjectCode()))
                .map(e -> e.getProjectCode().split("-")[0]).distinct().toList();
        Result<List<BuildingGeneVO>> result = feignMethH5Rpc.buildingGeneByNo(buildingNos);
        Map<String, BigDecimal> buildingFinalCoefficientMap = new HashMap<>();
        if (result.getCode() == HttpStatus.OK.value() || CollectionUtils.isNotEmpty(result.getData())) {
            buildingFinalCoefficientMap = result.getData().stream()
                    // valueMapper不能为null
                    .filter(e -> Objects.nonNull(e.getFinalCoefficient()))
                    .collect(Collectors.toMap(BuildingGeneVO::getBuildingRatingNo, BuildingGeneVO::getFinalCoefficient));
        }

        // 翻译物业类型名称及获取大屏系数
        for (ContractProjectDetailVO project : projectVos) {
            project.setPropertyTypeName(PropertyTypeEnum.getDesc(project.getPropertyType()));
            project.setPaymentTypeName(PaymentTypeEnum.getDesc(project.getPaymentType()));
            project.setLargeScreenCoefficient(buildingFinalCoefficientMap.get(project.getProjectCode().split("-")[0]));
        }

        // // 有押金，需要填充押金收款方
        // Map<Integer, ContractDepositSupplierEntity> depositSupplierEntityMap = depositSupplierService.lambdaQuery()
        //         .eq(ContractDepositSupplierEntity::getContractId, contract.getId()).list().stream()
        //         .collect(Collectors.toMap(ContractDepositSupplierEntity::getProjectId, depositSupplier -> depositSupplier));
        //
        // projectVos.stream().filter(project -> BooleFlagEnum.isYes(project.getDepositFlag())).forEach(project -> {
        //     ContractDepositSupplierEntity depositSupplierEntity = depositSupplierEntityMap.get(project.getId());
        //     if (Objects.isNull(depositSupplierEntity)) {
        //         return;
        //     }
        //     project.setDepositId(depositSupplierEntity.getId());
        //     project.setDepositSupplierId(depositSupplierEntity.getSupplierId());
        //     project.setDepositSupplierName(depositSupplierEntity.getSupplierName());
        //     project.setDepositSupplierAccountNo(depositSupplierEntity.getAccountNo());
        //     project.setDepositSupplierBankId(depositSupplierEntity.getSupplierBankId());
        // });

        // 填充到返回结果上
        projectConsumer.accept(projectVos);
    }

    /**
     * 主合同付款周期的数据
     */
    public void fillingMainContractPaymentPeriods(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier, boolean fillingLedger, boolean planPaymentDateOrder) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) {
            return;
        }
        // 查询合同-供应商
        List<ContractSupplierEntity> suppliers = contractSupplierService.lambdaQuery()
                .eq(ContractSupplierEntity::getSubContractId, 0)
                .eq(ContractSupplierEntity::getContractId, contract.getId()).list();
        if (CollectionUtils.isEmpty(suppliers)) {
            return;
        }
        // 找到所有终端信息
        List<ContractDeviceDetailVO> devices = projects.stream().map(ContractProjectDetailVO::getPriceApplies)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                .map(ContractPriceApplyDetailVO::getDevices).filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream).filter(Objects::nonNull).toList();
        if (CollectionUtils.isEmpty(devices)) {
            return;
        }

        // 按终端ID分组
        Map<Integer, List<ContractPaymentPeriodEntity>> paymentPeriodMap = paymentPeriodService.lambdaQuery()
                .eq(ContractPaymentPeriodEntity::getContractId, contract.getId())
                .gt(ContractPaymentPeriodEntity::getDeviceId, 0)
                .orderByAsc(ContractPaymentPeriodEntity::getPlanPaymentDate, ContractPaymentPeriodEntity::getId).list()
                .stream().collect(Collectors.groupingBy(ContractPaymentPeriodEntity::getDeviceId));

        // 获取台账数据
        Map<Integer, LedgerEntity> ledgerEntityMap = getLedgerOfPaymentPeriod(paymentPeriodMap.values().stream()
                .flatMap(Collection::stream)
                .map(e -> CONTRACT_CHANGE_TYPES.contains(contract.getContractType()) ? e.getParentId() : e.getId())
                .collect(Collectors.toSet()), fillingLedger);

        // 供应商银行信息
        Set<Integer> supplierIds = suppliers.stream().map(ContractSupplierEntity::getSupplierId).collect(Collectors.toSet());
        Map<Integer, String> supplierMap = supplierService.lambdaQuery()
                .in(SupplierEntity::getId, supplierIds).list().stream()
                .collect(Collectors.toMap(SupplierEntity::getId, SupplierEntity::getSupplierName));

        // 为终端生成单价周期
        for (ContractDeviceDetailVO device : devices) {
            device.setPaymentPeriods(paymentPeriodWithLedgerAdapter(contract, paymentPeriodMap.getOrDefault(device.getId(), Collections.emptyList()),
                    ledgerEntityMap, planPaymentDateOrder, supplierMap));
        }
    }

    /**
     * 获取台账数据
     */
    private Map<Integer, LedgerEntity> getLedgerOfPaymentPeriod(Set<Integer> paymentPeriodIds, boolean fillingLedger) {
        if (Boolean.FALSE.equals(fillingLedger) || CollectionUtils.isEmpty(paymentPeriodIds)) {
            return Map.of();
        }
        //
        return ledgerService.lambdaQuery().in(LedgerEntity::getPaymentPeriodId, paymentPeriodIds)
                .select(LedgerEntity::getPaymentPeriodId, LedgerEntity::getPaidAmount).list().stream()
                .filter(e -> !Objects.equals(e.getPaymentPeriodId(), 0))
                .collect(Collectors.toMap(LedgerEntity::getPaymentPeriodId, Function.identity(), (o, n) -> n));
    }

    /**
     * 根据合同类型匹配付款周期的台账数据
     */
    private List<ContractPaymentPeriodDetailVO> paymentPeriodWithLedgerAdapter(ContractEntity contract,
                                                                               List<ContractPaymentPeriodEntity> paymentPeriods,
                                                                               Map<Integer, LedgerEntity> ledgerEntityMap,
                                                                               boolean planPaymentDateOrder,
                                                                               Map<Integer, String> supplierMap) {
        if (CollectionUtils.isEmpty(paymentPeriods)) {
            return null;
        }
        // 中间状态
        Stream<ContractPaymentPeriodDetailVO> transitionStream = paymentPeriods.stream().map(e -> {
            ContractPaymentPeriodDetailVO vo = contractConvert.toEditDetailVO(e);
            vo.setSupplierName(supplierMap.getOrDefault(e.getSupplierId(), vo.getSupplierName()));
            Integer key = CONTRACT_CHANGE_TYPES.contains(contract.getContractType()) ? e.getParentId() : e.getId();
            vo.setPaidAmount(ledgerEntityMap.containsKey(key) ? ledgerEntityMap.get(key)
                    .getPaidAmount() : BigDecimal.ZERO);
            return vo;
        });
        return planPaymentDateOrder ? transitionStream.sorted(Comparator.comparing(ContractPaymentPeriodDetailVO::getPlanPaymentDate, Comparator.nullsLast(Comparator.naturalOrder())))
                .toList() : transitionStream.sorted(Comparator.comparing(ContractPaymentPeriodDetailVO::getStartDate, Comparator.nullsLast(Comparator.naturalOrder())))
                .toList();
    }

    /**
     * 子合同付款周期的数据
     */
    public void fillingSubContractPaymentPeriods(ContractEntity contract, Supplier<List<ContractProjectDetailVO>> projectSupplier, boolean fillingLedger, boolean planPaymentDateOrder) {
        List<ContractProjectDetailVO> projects = projectSupplier.get();
        if (CollectionUtils.isEmpty(projects)) return;

        // 查找子合同
        List<SubContractDetailVO> subContracts = projects.stream().map(ContractProjectDetailVO::getSubContracts)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).toList();
        if (CollectionUtils.isEmpty(subContracts)) return;


        // 子合同, key: 子合同ID, val: feeType -> periods
        Map<Integer, Map<String, List<ContractPaymentPeriodEntity>>> paymentPeriodMap = paymentPeriodService.lambdaQuery()
                .eq(ContractPaymentPeriodEntity::getContractId, contract.getId())
                .gt(ContractPaymentPeriodEntity::getSubContractId, 0).list().stream()
                .collect(Collectors.groupingBy(ContractPaymentPeriodEntity::getSubContractId, Collectors.groupingBy(ContractPaymentPeriodEntity::getFeeType)));
        if (MapUtils.isEmpty(paymentPeriodMap)) return;

        // 查询合同-供应商
        List<ContractSupplierEntity> suppliers = contractSupplierService.lambdaQuery()
                .gt(ContractSupplierEntity::getSubContractId, 0)
                .eq(ContractSupplierEntity::getContractId, contract.getId()).list();
        if (CollectionUtils.isEmpty(suppliers)) {
            return;
        }

        // 供应商银行信息
        Set<Integer> supplierIds = suppliers.stream().map(ContractSupplierEntity::getSupplierId).collect(Collectors.toSet());
        Map<Integer, String> supplierMap = supplierService.lambdaQuery()
                .in(SupplierEntity::getId, supplierIds).list().stream()
                .collect(Collectors.toMap(SupplierEntity::getId, SupplierEntity::getSupplierName));
        for (SubContractDetailVO subContract : subContracts) {
            Map<String, List<ContractPaymentPeriodEntity>> periodMap = paymentPeriodMap.get(subContract.getId());
            if (MapUtils.isEmpty(periodMap)) continue;

            // 获取台账数据
            Map<Integer, LedgerEntity> ledgerEntityMap = getLedgerOfPaymentPeriod(periodMap.values().stream()
                    .flatMap(Collection::stream)
                    .map(e -> CONTRACT_CHANGE_TYPES.contains(contract.getContractType()) ? e.getParentId() : e.getId())
                    .collect(Collectors.toSet()), fillingLedger);

            // 分成有费用对象和没有两种情况分别处理，
            if (CollectionUtils.isEmpty(subContract.getFees())) {
                subContract.setFees(Lists.newArrayList());
                periodMap.forEach((feeType, periods) -> {
                    SubContractFeeDetailVO feeVo = new SubContractFeeDetailVO();
                    feeVo.setFeeType(feeType);
                    feeVo.setPaymentPeriods(paymentPeriodWithLedgerAdapter(contract, periods, ledgerEntityMap, planPaymentDateOrder, supplierMap));
                    subContract.getFees().add(feeVo);
                });
            } else {
                Map<String, SubContractFeeDetailVO> feeMap = subContract.getFees().stream()
                        .collect(Collectors.toMap(SubContractFeeDetailVO::getFeeType, Function.identity(), (o, n) -> n));
                periodMap.forEach((feeType, periods) -> {
                    SubContractFeeDetailVO feeVo = feeMap.get(feeType);
                    if (Objects.isNull(feeVo)) {
                        feeVo = new SubContractFeeDetailVO();
                        feeVo.setFeeType(feeType);
                        subContract.getFees().add(feeVo);
                    }
                    feeVo.setPaymentPeriods(paymentPeriodWithLedgerAdapter(contract, periods, ledgerEntityMap, planPaymentDateOrder, supplierMap));
                });
            }
            // 按字典排序，方便每次看到数据都是一样的顺序
            subContract.getFees().sort(Comparator.comparing(SubContractFeeDetailVO::getFeeType));
        }

        // 翻译字典名字
        List<SubContractFeeDetailVO> allFees = subContracts.stream().map(SubContractDetailVO::getFees)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).filter(Objects::nonNull).toList();
        translatorFactory.translate(allFees);
    }
}
