/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-16
 */
package com.coocaa.ad.cheese.cms.venue.vo.building;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 楼宇基因实体类
 * 用于存储楼宇的基本特征和属性信息，包括楼宇编号、规格、电梯数量等基础信息
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
public class BuildingGeneVO implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 楼宇编号，唯一标识一栋楼宇
     */
    private String buildingRatingNo;

    /**
     * 提交系数，用于计算楼宇的得分
     */
    private BigDecimal submitCoefficient;

    /**
     * 复核系数，审核后确定的系数
     */
    private BigDecimal finalCoefficient;

    /**
     * 是否删除标记：0-未删除，1-已删除
     */
    private Boolean deleteFlag;
} 