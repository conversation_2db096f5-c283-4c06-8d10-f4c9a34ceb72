package com.coocaa.ad.cheese.cms.venue.bean.ledger;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LedgerExcelParam {
    /**
     * 台账ID
     */
    @ExcelProperty(value = "台账ID")
    private String ledgerId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String projectName;

    /**
     * 已支付金额
     */
    @ExcelProperty(value = "已支付金额")
    private String paidAmount;

    /**
     * 组织编码
     */
    @ExcelProperty(value = "已回票金额")
    private String returnInvoicedAmount;


    /**
     * 数据行数
     */
    @ExcelIgnore
    private Integer rowNum;


    /**
     * 导入结果
     */
    @ExcelProperty(value = "导入结果")
    private String importResult;

    /**
     * 导入信息
     */
    @ExcelProperty(value = "导入信息")
    private String message;
}
