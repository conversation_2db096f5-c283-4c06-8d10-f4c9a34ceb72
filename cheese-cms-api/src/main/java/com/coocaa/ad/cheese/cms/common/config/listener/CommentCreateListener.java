package com.coocaa.ad.cheese.cms.common.config.listener;

import com.coocaa.ad.cheese.cms.common.config.listener.event.CommentCreateEvent;
import com.coocaa.ad.cheese.cms.venue.service.CommentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/30
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CommentCreateListener {

    private final CommentService commentService;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleCommentCreateEvent(CommentCreateEvent commentCreateEvent) {
        commentService.sendCommentMessage(commentCreateEvent.getCommentEntity());
    }
}
