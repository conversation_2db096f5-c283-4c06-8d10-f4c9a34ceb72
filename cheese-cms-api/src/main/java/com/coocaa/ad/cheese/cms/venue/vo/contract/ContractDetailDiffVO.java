package com.coocaa.ad.cheese.cms.venue.vo.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 合同详情差异VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 14:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ContractDetailDiffVO extends ContractDetailVO {
    @Schema(description = "修改的数据项", type = "List")
    private List<String> replaceItems;

    @Schema(description = "新增的数据项", type = "List")
    private List<String> addItems;

    @Schema(description = "（加密）快照数据ID", type = "Int")
    private String snapshotId;
}
