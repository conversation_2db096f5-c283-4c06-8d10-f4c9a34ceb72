package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.util.translate.VenueTransTypes;
import com.coocaa.ad.common.serializer.DesensitizeSerializer;
import com.coocaa.ad.translate.anno.TransField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 合同-项目
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04
 */
@Data
public class ContractProjectDetailVO {
    @Schema(description = "项目ID", type = "Int", example = "1")
    private Integer id;

    @Schema(description = "楼宇编码", type = "String", example = "BRR001")
    private String buildingNo;

    @Schema(description = "项目编码(来自楼宇评级)", type = "String", example = "PRJ001")
    private String projectCode;

    @Schema(description = "项目名称", type = "String", example = "XX大厦")
    private String projectName;

    @TransField(type = VenueTransTypes.DICT, target = "propertyTypeName")
    @Schema(description = "物业类型(来自楼宇评级, 转字典)", type = "String", example = "写字楼")
    private String propertyType;
    private String propertyTypeName;

    @Schema(description = "城市ID(来自楼宇评级, 转城市编码)", type = "Int", example = "440300")
    private Integer cityId;

    @Schema(description = "省市区", type = "String", example = "广东省深圳市南山区")
    private String areaName;

    // @JSONField(serializeUsing = DesensitizeSerializer.class)
    @Schema(description = "详细地址", type = "String", example = "深南大道1000号")
    private String address;

    @Schema(description = "项目等级", type = "String", example = "A")
    private String level;

    @Schema(description = "评级版本", type = "String", example = "0")
    private String ratingVersion;

    @Schema(description = "大屏系数", type = "BigDecimal", example = "1.00")
    private BigDecimal largeScreenCoefficient;

    @TransField(type = VenueTransTypes.DICT, target = "paymentTypeName")
    @Schema(description = "付款方式(字典0027 季付、半年、一年、一次性、其它)", type = "String", example = "0027-3")
    private String paymentType;
    private String paymentTypeName;

    @Schema(description = "付款周期前N天支付", type = "Int", example = "30")
    private Integer intervalDay;

    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    @Schema(description = "开始日期", type = "String", example = "2024-01-01")
    private LocalDate startDate;

    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    @Schema(description = "结束日期", type = "String", example = "2024-12-31")
    private LocalDate endDate;

    @Schema(description = "项目年限", type = "BigDecimal", example = "1.00")
    private BigDecimal period;

    @Schema(description = "项目金额", type = "BigDecimal", example = "1.00")
    private BigDecimal amount;

    @Schema(description = "经度", type = "String", example = "104.065837")
    private String latitude;

    @Schema(description = "纬度", type = "String", example = "30.657349")
    private String longitude;

    @Schema(description = "top值", type = "String", example = "1级")
    private String topLevel;

    @Schema(description = "来源记录id", type = "Int", example = "1")
    private Integer parentId;
    
    @Schema(description = "城市核心区、城市非核心区", type = "String", example = "城市核心区")
    private String locationName;

    @Schema(description = "项目价格申请列表")
    private List<ContractPriceApplyDetailVO> priceApplies;

    @Schema(description = "子合同列表")
    private List<SubContractDetailVO> subContracts;

    // 下面全是已经废弃的字段
    @JsonIgnore
    @Schema(description = "押金记录ID", type = "Int", example = "1", deprecated = true)
    private Integer depositId;

    @JsonIgnore
    @Schema(description = "是否有押金 [0:无, 1:有]", type = "Boolean", example = "true", deprecated = true)
    private Integer depositFlag;

    @JsonIgnore
    @Schema(description = "押金金额", type = "BigDecimal", example = "10000.00", deprecated = true)
    private BigDecimal depositAmount;

    @JsonIgnore
    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    @Schema(description = "押金付款时间", type = "String", example = "2024-01-01", deprecated = true)
    private LocalDate depositPaymentDate;

    @JsonIgnore
    @Schema(description = "押金收款方", type = "Int", example = "1", deprecated = true)
    private Integer depositSupplierId;

    @JsonIgnore
    @Schema(description = "押金收款方名称", type = "Int", example = "1", deprecated = true)
    private String depositSupplierName;

    @JsonIgnore
    @Schema(description = "押金收款方银行账号", example = "1", deprecated = true)
    @JSONField(serializeUsing = DesensitizeSerializer.class)
    private String depositSupplierAccountNo;

    @JsonIgnore
    @Schema(description = "押金收款方银行账号id", example = "1", deprecated = true)
    private Integer depositSupplierBankId;
}
