package com.coocaa.ad.cheese.cms.venue.bean.notify;

import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 合同状态变更，修改商机状态
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-31
 */
@Data
@Accessors(chain = true)
public class ContractStatusChangeLogDTO {
    /**
     * 数据类型, 字典: 0042-4
     */
    private String type;

    /**
     * 商机编码
     */
    private String bizCode;

    /**
     * 业务状态, 字典: 0043-5
     */
    private String status;

    /**
     * 状态变更时间
     */
    @JsonFormat(pattern = VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime changeTime;

    /**
     * 状态变更操作人工号
     */
    private String operatorWno;

    /**
     * 状态变更操作人姓名
     */
    private String operatorName;
}
