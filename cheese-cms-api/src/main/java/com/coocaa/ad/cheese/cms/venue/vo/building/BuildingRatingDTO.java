package com.coocaa.ad.cheese.cms.venue.vo.building;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 楼宇详情
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20
 */
@Data
public class BuildingRatingDTO {
    /**
     * 楼宇编码
     */
    @Schema(description = "楼宇编码")
    private String buildingNo;

    /**
     * 楼宇认证状态：0未认证，1认证中 2冻结中  3已认证
     */
    @Schema(description = "楼宇认证状态：0未认证，1认证中 2冻结中  3已认证")
    private Integer buildingStatus;

    /**
     * 楼宇状态：0待审核，1已审核 2已驳回 3审核不通过   4已放弃
     */
    @Schema(description = "楼宇状态：0待审核，1已审核 2已驳回 3审核不通过   4已放弃")
    private Integer status;

    /**
     * 楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区
     */
    @Schema(description = "楼宇类型 0 写字楼 1 商住楼  2 综合体 3 产业园区")
    private Integer buildingType;

    /**
     * 楼宇名称
     */
    @Schema(description = "楼宇名称")
    private String buildingName;

    /**
     * 地图楼宇编码
     */
    @Schema(description = "地图楼宇编码")
    private String mapNo;

    /**
     * 省名称
     */
    @Schema(description = "省名称")
    private String mapProvince;

    /**
     * 市名称
     */
    @Schema(description = "市名称")
    private String mapCity;

    /**
     * 区名称
     */
    @Schema(description = "区名称")
    private String mapRegion;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址")
    private String mapAddress;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private String mapLatitude;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String mapLongitude;

    /**
     * 行政区域编码
     */
    @Schema(description = "行政区域编码")
    private String mapAdCode;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String buildingDesc;

    @Schema(description = "楼宇评级ai")
    private String projectLevelAi;

    @Schema(description = "楼宇评级")
    private String projectLevel;

    @Schema(description = "商机编码集合")
    private List<String> businessCodes;
}
