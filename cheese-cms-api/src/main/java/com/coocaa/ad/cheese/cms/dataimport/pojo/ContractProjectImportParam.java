package com.coocaa.ad.cheese.cms.dataimport.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 对应飞书 项目明细几列
 */
@Data
public class ContractProjectImportParam {

    //项目明细
    @Schema(description = "项目明细",example = "项目明细 1")
    private String projectDetail;

    @Schema(description = "楼宇评级申请编码",example = "BRR2024122902371")
    private String lypjApplyCode;

    @Schema(description = "AI评级",example = "AAA")
    private String aiLevel;

    @Schema(description = "项目名称",example = "安东尼国际商务大厦")
    private String projectName;

    @Schema(description = "项目所在城市（已废弃）",example = "")
    private String cityNameNotUse;

    @Schema(description = "项目详细地址",example = "佛山市-禅城区-塱沙路179号")
    private String projectAddr;

    @Schema(description = "项目类型（废弃）",example = "")
    private String projectTypeNameNotUse;

    @Schema(description = "楼宇等级",example = "AA")
    private String levelName;

    @Schema(description = "评分详情",example = "{\"title\":\"评分详情\",\"url\":\"https://meth.cshimedia.com/detailPage?buildingNo=BRR2024122902371\",\"linkType\":1}")
    private String socreDetail;

    @Schema(description = "N1款：30.1",example = "4")
    private String n1301;

    @Schema(description = "查看地图（PC端）",example = "{\"title\":\"查看地图（PC端）\",\"url\":\"https://map.baidu.com/search/安东尼国际商务大厦/@12590676.866386492,2620985.597069528,19z?querytype=s&wd=安东尼国际商务大厦\",\"linkType\":1}")
    private String mapPc;

    @Schema(description = "查看地图（移动端）",example = "{\"title\":\"查看地图（移动端）\",\"url\":\"https://map.baidu.com/mobile/webapp/place/list/qt=con&wd=安东尼国际商务大厦&c=138&contp=1/vt=map\",\"linkType\":1}")
    private String mapApp;

    @Schema(description = "N2款：35.7",example = "0")
    private String n2357;

    @Schema(description = "P1款：32",example = "0")
    private String p132;

    @Schema(description = "签约单价（台/年/元）",example = "1500")
    private BigDecimal price;

    @Schema(description = "签约点位数",example = "4")
    private Integer pointNum;

    @Schema(description = "点位尺寸规格",example = "")
    private String pointSpec;

    @Schema(description = "签约单价（废弃）",example = "")
    private String priceNotUse;

    @Schema(description = "项目所在城市",example = "福建省-莆田市-荔城区")
    private String city;

    @Schema(description = "项目类型",example = "商住楼")
    private String projectType;

    @Schema(description = "城市项目名称,台账excel",example = "广州合汇广场B1办公楼")
    private String cityProjectName;



    @Schema(description = "项目总金额",example = "9100")
    private BigDecimal amount;

}
