package com.coocaa.ad.cheese.cms.venue.rpc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 查询审批节点名称和自定义ID
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-04
 */
@Data
public class ApprovalNodeVO {

    @Schema(description = "节点code")
    private String nodeCode;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "任务节点人")
    private Integer userId;
} 