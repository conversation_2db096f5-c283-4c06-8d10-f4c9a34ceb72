/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.coocaa.ad.cheese.cms.download.service;

import com.alibaba.fastjson2.JSONObject;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.download.handler.CmsSysTypeHandlerService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.service.ContractExportService;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.ad.downloader.bean.dto.AttachmentDTO;
import com.coocaa.ad.downloader.core.AbstractDownloaderProcessor;
import com.coocaa.ad.downloader.enums.IDownloaderType;
import com.coocaa.ad.downloader.rpc.FeignAttachmentRpc;
import com.coocaa.ad.downloader.rpc.FeignTaskRpc;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * 有效合同-按列表数据导出
 * 有效合同-按合同导出
 * <AUTHOR>
 * @since 1.1.0
 */
@Slf4j
@Service
public class ValidContractService extends AbstractDownloaderProcessor {

    private final ContractExportService contractExportService;

    public ValidContractService(RedissonClient redissonClient, FeignTaskRpc taskRpc,
                                FeignAttachmentRpc attachmentRpc,
                                UserCacheHelper userCacheHelper,
                                CmsSysTypeHandlerService cmsSysTypeHandlerService,
                                ContractExportService contractExportService) {
        super(redissonClient, taskRpc, attachmentRpc, userCacheHelper, cmsSysTypeHandlerService);
        this.contractExportService = contractExportService;
    }

    @Override
    protected IDownloaderType getProcessorType() {
        return DownLoadTypeEnum.YXHT_LB;
    }

    @Override
    protected List<AttachmentDTO> doProcess(String executeParams) {
        ContractQueryParam queryParam = JSONObject.parseObject(executeParams, ContractQueryParam.class);
        String url = contractExportService.exportValidContracts(queryParam, null);
        return List.of(new AttachmentDTO().setName(DownLoadTypeEnum.YXHT_LB.getDesc()).setUrl(url));
    }


}
