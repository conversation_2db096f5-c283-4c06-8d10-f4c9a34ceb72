package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.util.translate.VenueTransTypes;
import com.coocaa.ad.translate.anno.TransField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24
 */
@Data
public class ContractAbnormalVO {

    @Schema(description = "异常合同ID", type = "Int", example = "0")
    private Integer id;

    @Schema(description = "原合同id", type = "Int", example = "0")
    private Integer contractId;

    @Schema(description = "异常合同编码", type = "String", example = "0")
    private String applyCode;

    @Schema(description = "原合同编码", type = "String", example = "0")
    private String contractCode;

    @Schema(description = "项目名称", type = "String", example = "0")
    private String projectName;

    @Schema(description = "状态", type = "String", example = "0")
    @TransField(type = VenueTransTypes.DICT, target = "applyStatusName")
    private String applyStatus;

    @Schema(description = "状态", type = "String", example = "0")
    private String applyStatusName;

    @Schema(description = "供应商名称", type = "String", example = "0")
    private String supplierName;

    @Schema(description = "合同金额", type = "BigDecimal", example = "0.99")
    private BigDecimal totalAmount;

    @Schema(description = "合同年限", type = "BigDecimal", example = "0.99")
    private BigDecimal period;

    @Schema(description = "异常点位数", type = "Int", example = "3")
    private Integer abnormalCount;

    @Schema(description = "异常类型", type = "String", example = "xx")
    @TransField(type = VenueTransTypes.DICT, target = "typeName")
    private String type;
    @Schema(description = "异常类型", type = "String", example = "xx")
    private String typeName;

    @Schema(description = "处理方式", type = "String", example = "xx")
    @TransField(type = VenueTransTypes.DICT, target = "dealTypeName")
    private String dealType;
    @Schema(description = "处理方式", type = "String", example = "0")
    private String dealTypeName;

    @Schema(description = "异常原因", type = "String", example = "xx")
    @TransField(type = VenueTransTypes.DICT, target = "abnormalReasonName")
    private String abnormalReason;
    @Schema(description = "异常原因", type = "String", example = "0")
    private String abnormalReasonName;

    @Schema(description = "申请人", type = "Int", example = "01")
    @TransField(type = VenueTransTypes.USER, target = "creatorName")
    private Integer creator;
    @Schema(description = "申请人", type = "String", example = "0")
    private String creatorName;

    @Schema(description = "跟进人", type = "Int", example = "01")
    @TransField(type = VenueTransTypes.USER, target = "followerName")
    private Integer follower;
    @Schema(description = "跟进人", type = "String", example = "0")
    private String followerName;


    @Schema(description = "创建时间", type = "String", example = "2024-12-31")
    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "是否超期未处理", type = "Int", example = "0")
    private Integer overDateFlag;

    @Schema(description = "处理人", type = "Int", example = "0")
    @TransField(type = VenueTransTypes.USER, target = "handlerName")
    private Integer handler;
    @Schema(description = "处理人", type = "String", example = "0")
    private String handlerName;

    @Schema(description = "完成时间", type = "String", example = "2024-12-31")
    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    private LocalDateTime handleTime;

    @Schema(description = "城市ID", type = "Int", example = "0")
    @TransField(type = VenueTransTypes.CITY, target = "cityName")
    private Integer cityId;

    @Schema(description = "城市名称", type = "String", example = "xx")
    private String cityName;


}
