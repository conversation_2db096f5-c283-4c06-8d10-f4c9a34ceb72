package com.coocaa.ad.cheese.cms.venue.bean.contract;


import com.coocaa.ad.cheese.cms.common.validation.ValidationGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.groups.Default;
import lombok.Data;

/**
 * 合同终端点位参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14
 */
@Data
public class ContractDevicePointParam {
    @NotBlank(message = "点位编码不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class})
    @Schema(description = "点位编码", type = "String", example = "FZ000412")
    private String code;

    @NotBlank(message = "点位名称不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class})
    @Schema(description = "点位名称", type = "String", example = "1栋_2单元_1层_大堂_1号电梯厅_点位2")
    private String name;
}
