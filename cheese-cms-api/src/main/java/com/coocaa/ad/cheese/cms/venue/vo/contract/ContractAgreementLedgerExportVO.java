package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.coocaa.ad.cheese.cms.common.serializer.LocalDateTimeSerializer;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 补充协议-按照台账导出
 *
 * <AUTHOR>
 * @since 2025/07/10
 */
@Data
public class ContractAgreementLedgerExportVO {

    @ExcelProperty(value = "变更申请编号")
    private String applyCode;

    @ExcelProperty(value = "合同编号")
    private String contractCode;

    @ExcelProperty(value = "楼宇评级编码")
    private String buildingNo;

    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ExcelProperty(value = "物业类型")
    private String propertyTypeName;

    @ExcelProperty(value = "所属大区")
    private String regionName;

    @ExcelProperty(value = "城市")
    private String cityName;

    @ExcelProperty(value = "地理位置")
    private String locationName;

    @ExcelProperty(value = "楼宇等级")
    private String level;

    @ExcelProperty(value = "楼宇AI等级")
    private String aiLevel;

    @ExcelProperty(value = "大屏系数")
    private BigDecimal largeScreenCoefficient;

    @ExcelProperty(value = "项目激励(元/台)")
    private String incentivePrice;

    @ExcelProperty(value = "签约单价(元/台/年)")
    private String pricePeriodYear;

    @ExcelProperty(value = "是否终止")
    private String stopFlagName;

    @ExcelProperty(value = "标准合同")
    private String normalFlagName;

    @ExcelProperty(value = "审批状态")
    private String applyStatusName;

    @ExcelProperty(value = "执行状态")
    private String formalStatusName;

    @ExcelProperty(value = "供应商1")
    private String supplierName1;

    @ExcelProperty(value = "供应商2")
    private String supplierName2;

    @ExcelProperty(value = "发票类型")
    private String invoiceTypeName;

    @ExcelProperty(value = "税点")
    private String taxPointName;

    @ExcelProperty(value = "业务类型")
    private String businessTypeName;

    @ExcelProperty(value = "公司")
    private String agentName;

    @ExcelProperty(value = "是否大屏")
    private String largeScreen;

    @ExcelProperty(value = "尺寸规格")
    private String sizeSpec;

    @ExcelProperty(value = "合同总金额(元)")
    private BigDecimal totalAmount;

    @ExcelProperty(value = "合同开始时间")
    @DateTimeFormat(VenueConstants.DATE_FORMAT)
    private LocalDate startDate;

    @ExcelProperty(value = "合同结束时间")
    @DateTimeFormat(VenueConstants.DATE_FORMAT)
    private LocalDate endDate;

    @ExcelProperty(value = "合同年限")
    private BigDecimal period;

    @ExcelProperty(value = "付款方式")
    private String paymentTypeName;

    @ExcelProperty(value = "期数")
    private Integer periods;

    @ExcelProperty(value = "费用类型")
    private String feeTypeName;

    @ExcelProperty(value = "付款周期")
    private String payPeriod;

    @ExcelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ExcelProperty(value = "计划付款日")
    private LocalDate plannedPaymentDate;

    @ExcelProperty(value = "签约点位数")
    private Integer signCount;

    @ExcelProperty(value = "付款要求")
    private String claimName;

    @ExcelProperty(value = "免租期")
    private String freeFlagName;

    @ExcelProperty(value = "免租期开始日期")
    private LocalDate freeStartDate;

    @ExcelProperty(value = "免租期结束日期")
    private LocalDate freeEndDate;

    @ExcelProperty(value = "是否归档")
    private String archiveFlagName;

    @ExcelProperty(value = "归档时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime archiveTime;

    @ExcelProperty(value = "申请时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime applyTime;

    @ExcelProperty(value = "审批通过时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime approveTime;

    @ExcelProperty(value = "收款供应商名称")
    private String supplierName;

    @ExcelProperty(value = "供应商收款户名")
    private String accountName;

    @ExcelProperty(value = "供应商开户行")
    private String bankName;

    @ExcelProperty(value = "联系人")
    private String contactPerson;

    @ExcelProperty(value = "联系电话")
    private String contactPhone;

    @ExcelProperty(value = "联系地址")
    private String contactAddress;

    @ExcelProperty(value = "申请人")
    private String creatorName;

    @ExcelProperty(value = "跟进人")
    private String followerName;

}
