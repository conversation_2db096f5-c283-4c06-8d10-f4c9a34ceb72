package com.coocaa.ad.cheese.cms.dataimport.service;

import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Service
@Data
public class YearPayPeriodService implements PayPeriodService {

    @Override
    public LocalDate getNextStart(LocalDate currentStartDate, LocalDate end) {
        return currentStartDate.plusMonths(12);
    }

    @Override
    public LocalDate getEndDate(LocalDate currentStartDate, LocalDate end) {
        return currentStartDate.plusMonths(12).minusDays(1);
    }


    public static void main(String[] args) {
        LocalDate localDate = LocalDate.of(2024,1,1);
        System.out.println(localDate.plusMonths(12).format(DateTimeFormatter.ofPattern(VenueConstants.DATE_FORMAT)));
        System.out.println(localDate.plusYears(1).format(DateTimeFormatter.ofPattern(VenueConstants.DATE_FORMAT)));
        System.out.println(localDate.plusMonths(12).minusDays(1).format(DateTimeFormatter.ofPattern(VenueConstants.DATE_FORMAT)));
        System.out.println(localDate.plusYears(1).minusDays(1).format(DateTimeFormatter.ofPattern(VenueConstants.DATE_FORMAT)));
    }
}
