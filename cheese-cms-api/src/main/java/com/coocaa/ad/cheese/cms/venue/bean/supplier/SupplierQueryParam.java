package com.coocaa.ad.cheese.cms.venue.bean.supplier;

import com.coocaa.ad.cheese.cms.common.tools.venue.enums.SupplierTypeEnum;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.validation.EnumType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class SupplierQueryParam {

    @EnumType(message = "供应商类型不能为空", value = SupplierTypeEnum.class)
    @Schema(description = "供应商类型[1:自营, 2:代理]", type = "Int", example = "1")
    private Integer supplierType;

    @Size(min = 2, max = 50, message = "供应商编码长度必须在2-50之间")
    @Schema(description = "供应商编码", type = "String", example = "EEREW089891")
    private String supplierCode;

    @Size(min = 2, max = 50, message = "供应商名称长度必须在2-50之间")
    @Schema(description = "供应商名称", type = "String", example = "XXXXX公司")
    private String supplierName;

    @EnumType(message = "供应商状态不能为空", value = BooleFlagEnum.class)
    @Schema(description = "供应商状态[1:启用, 0:禁用]", type = "Int", example = "1")
    private Integer status;

    @Schema(description = "必须包含的供应商ID", type = "Int", example = "223")
    private Integer include;

}


