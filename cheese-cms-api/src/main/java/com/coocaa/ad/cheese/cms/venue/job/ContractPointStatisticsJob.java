package com.coocaa.ad.cheese.cms.venue.job;

import com.coocaa.ad.cheese.cms.venue.service.KanbanService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDate;

/**
 * 合同点位状态数量统计
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractPointStatisticsJob {

    private final KanbanService kanbanService;

    @XxlJob("contractPointStatisticsJob")
    public void executeStatistics() {
        XxlJobHelper.log("统计合同点位状态数量开始");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("统计合同点位状态");

        LocalDate statisticsDate = getStatisticsDate();

        try {
            validateDate(statisticsDate);
            kanbanService.statisticsPointData(statisticsDate);
            logSuccess(stopWatch.getTotalTimeMillis());
        } catch (Exception ex) {
            logError(ex);
            throw new IllegalStateException("统计合同点位状态异常", ex);
        } finally {
            stopWatch.stop();
        }
    }

    private LocalDate getStatisticsDate() {
        String param = XxlJobHelper.getJobParam();
        return param != null && !param.isEmpty() ? LocalDate.parse(param) : LocalDate.now().minusDays(1);
    }

    private void validateDate(@NotNull LocalDate date) {
        if (date.isAfter(LocalDate.now())) {
            throw new IllegalArgumentException("统计日期不能超过当前日期");
        }
    }

    private void logSuccess(long executionTime) {
        String message = String.format("统计合同点位状态数量完成, 耗时: %dms", executionTime);
        log.info(message);
        XxlJobHelper.log(message);
        XxlJobHelper.handleSuccess();
    }

    private void logError(Exception ex) {
        String errorMessage = "统计合同点位状态数量失败";
        log.error(errorMessage, ex);
        XxlJobHelper.handleFail(errorMessage + ": " + ex.getMessage());
    }
}
