package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.coocaa.ad.cheese.cms.common.serializer.LocalDateTimeSerializer;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 补充协议-按照点位导出VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/8
 */
@Data
public class ContractAgreementPointExportVO {
    @ExcelProperty(value = "变更申请编号")
    private String applyCode;

    @ExcelProperty(value = "合同编号")
    private String contractCode;

    @ExcelProperty(value = "楼宇评级编码")
    private String buildingNo;

    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ExcelProperty(value = "物业类型")
    private String propertyTypeName;

    @ExcelProperty(value = "所属大区")
    private String regionName;

    @ExcelProperty(value = "城市")
    private String cityName;

    @ExcelProperty(value = "地理位置")
    private String locationName;

    @ExcelProperty(value = "楼宇等级")
    private String level;

    @ExcelProperty(value = "楼宇AI等级")
    private String aiLevel;

    @ExcelProperty(value = "大屏系数")
    private BigDecimal largeScreenCoefficient;

    @ExcelProperty(value = "点位编码")
    private String pointCode;

    @ExcelProperty(value = "安装位置")
    private String pointName;

    @ExcelProperty(value = "设备尺寸")
    private String pointSizeName;

    @ExcelProperty(value = "审批状态")
    private String applyStatusName;

    @ExcelProperty(value = "执行状态")
    private String formalStatusName;

    @ExcelProperty(value = "业务类型")
    private String businessTypeName;

    @ExcelProperty(value = "公司")
    private String agentName;

    @ExcelProperty(value = "是否归档")
    private String archiveFlagName;

    @ExcelProperty(value = "归档时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime archiveTime;

    @ExcelProperty(value = "申请时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime applyTime;

    @ExcelProperty(value = "审批通过时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime approveTime;

    @ExcelProperty(value = "申请人")
    private String creatorName;

    @ExcelProperty(value = "跟进人")
    private String followerName;
}
