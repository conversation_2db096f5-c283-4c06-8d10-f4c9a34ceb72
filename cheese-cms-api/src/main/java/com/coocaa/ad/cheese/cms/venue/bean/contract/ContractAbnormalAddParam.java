package com.coocaa.ad.cheese.cms.venue.bean.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/24
 */
@Data
public class ContractAbnormalAddParam {

    @Schema(description = "原合同id", type = "Int", example = "0")
    @NotNull(message = "原合同id不能为空")
    private Integer contractId;

    @Schema(description = "异常合同id", type = "Int", example = "0")
    private Integer id;

    @Schema(description = "异常类型", type = "String", example = "竟对打压，物业公司内部原因，技术限制，我方原因")
    @NotNull(message = "异常类型不能为空")
    private String type;

    @Schema(description = "异常总点位数不能为空", type = "Integer", example = "1")
    @NotNull(message = "异常总点位数不能为空")
    private Integer abnormalCount;

    @Schema(description = "备注", type = "String", example = "0")
    @NotEmpty(message = "异常说明不能为空")
    private String remark;


    @Valid
    @Schema(description = "处理方式")
    @NotNull(message = "处理方式不能为空")
    private ContractAbnormalDealAddParam abnormalDeal;

    @Valid
    @Schema(description = "异常项目信息", type = "List")
    @NotEmpty(message = "异常项目信息不能为空")
    List<ContractAbnormalProjectAddParam> projectList;

    @Valid
    @Schema(description = "合同附件")
    @NotEmpty(message = "合同附件不能为空")
    private List<ContractAbnormalAttachmentParam> attachments;
}
