package com.coocaa.ad.cheese.cms.venue.vo.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 合同终端点位
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14
 */
@Data
public class ContractDevicePointDetailVO {
    @Schema(description = "点位编码", type = "String", example = "FZ000412")
    private String code;

    @Schema(description = "点位名称", type = "String", example = "1栋_2单元_1层_大堂_1号电梯厅_点位2")
    private String name;
}
