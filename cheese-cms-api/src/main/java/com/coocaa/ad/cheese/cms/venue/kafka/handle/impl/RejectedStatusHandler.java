package com.coocaa.ad.cheese.cms.venue.kafka.handle.impl;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAbnormalService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IExamineApproveService;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractAbnormalFlagEnum;
import com.coocaa.ad.cheese.cms.venue.kafka.handle.ApprovalStatusHandler;
import com.coocaa.ad.cheese.cms.venue.service.StatusChangeLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 已拒绝节点状态处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-04
 */
@Slf4j
@Component("REJECTED")
public class RejectedStatusHandler extends ApprovalStatusHandler {

    @Autowired
    private IContractService contractService;

    public RejectedStatusHandler(IExamineApproveService examineApproveService, IContractAbnormalService contractAbnormalService, StatusChangeLogService statusChangeLogService, StringRedisTemplate stringRedisTemplate) {
        super(examineApproveService, contractAbnormalService, statusChangeLogService, stringRedisTemplate);
    }

    @Override
    protected String getNewStatus() {
        return ApplyStatusEnum.REJECT.getCode();
    }

    @Override
    protected String getPreviousStatus() {
        return ApplyStatusEnum.CHECKING.getCode();
    }

    @Override
    protected void otherHandle(Integer id) {

        Integer contractId = contractService.queryApprovalDetail(id);
        contractService.lambdaUpdate()
                .set(ContractEntity::getAbnormalFlag, ContractAbnormalFlagEnum.NONE.getCode())
                .eq(ContractEntity::getId, contractId)
                .update();
    }
} 