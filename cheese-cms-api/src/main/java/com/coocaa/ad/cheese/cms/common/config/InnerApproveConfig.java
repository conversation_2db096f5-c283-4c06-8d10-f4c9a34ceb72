package com.coocaa.ad.cheese.cms.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 内部审批配置
 *
 * <AUTHOR>
 * @since 2025/6/6
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "inner.approve.rule.code")
public class InnerApproveConfig {
    /**
     * 合同修改申请
     */
    private Integer contractModify;
}
