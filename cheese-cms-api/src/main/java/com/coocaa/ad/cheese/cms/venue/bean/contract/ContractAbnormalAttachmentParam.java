package com.coocaa.ad.cheese.cms.venue.bean.contract;

import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AttachmentBelongEnum;
import com.coocaa.ad.common.validation.EnumType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 异常合同附件参数
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@Data
@Schema(description = "异常合同附件参数")
public class ContractAbnormalAttachmentParam extends ContractAttachmentParam{

    @Schema(description = "附件归属的主表： [1:合同附件，2:异常申请附件]", type = "integer", example = "1")
    @NotNull(message = "附件归属不能为空")
    @EnumType(message = "附件类型不正确", value = AttachmentBelongEnum.class)
    private Integer belongTo;
}
