package com.coocaa.ad.cheese.cms.venue.bean.kanban;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @file StatisticsDeviceImportProdExcelParam
 * @date 2025/1/15 14:45
 * @description 设备生产数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatisticsDeviceImportProdExcelParam {

    @ExcelProperty(value = "下单数")
    private String orderCount;

    @ExcelProperty(value = "生产交付数")
    private String productionDeliveryCount;

    @ExcelProperty(value = "校验状态")
    private String checkStatus;

    @ExcelProperty(value = "备注")
    private String remark;

}
