package com.coocaa.ad.cheese.cms.venue.vo.user;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/26
 * @description
 */
@Data
public class SysUserApproveVO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 工号
     */
    @Schema(description = "工号")
    private String empCode;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String realName;

    /**
     * 飞书user_id
     */
    @Schema(description = "飞书user_id")
    private String fsUserId;

    /**
     * 飞书open_id
     */
    @Schema(description = "飞书open_id")
    private String fsOpenId;

    /**
     * 飞书union_id
     */
    @Schema(description = "飞书union_id")
    private String fsUnionId;

    /**
     * 三级组织名称
     */
    @Schema(description = "三级组织名称")
    private String threeOrg;

    /**
     * 四级组织名称
     */
    @Schema(description = "四级组织名称")
    private String fourOrg;

    /**
     * 审批人工号
     */
    @Schema(description = "审批人工号")
    private String approvalCode;

    /**
     * 审批人姓名
     */
    @Schema(description = "审批人姓名")
    private String approvalName;

    /**
     * 价格申请审批人工号
     */
    @Schema(description = "价格申请审批人工号")
    private String priceApplyCode;

    /**
     * 价格申请审批人姓名
     */
    @Schema(description = "价格申请审批人姓名")
    private String priceApplyName;

    /**
     * 合同申请审批人工号
     */
    @Schema(description = "合同申请审批人工号")
    private String contractApplyCode;

    /**
     * 合同申请审批人姓名
     */
    @Schema(description = "合同申请审批人姓名")
    private String contractApplyName;

}
