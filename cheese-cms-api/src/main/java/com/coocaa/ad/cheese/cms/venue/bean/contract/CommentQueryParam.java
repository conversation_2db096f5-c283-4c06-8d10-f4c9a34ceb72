package com.coocaa.ad.cheese.cms.venue.bean.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/6
 */
@Data
public class CommentQueryParam {
    @Schema(description = "业务id")
    @NotNull
    private Integer bizId;

    @Schema(description = "业务类型 1 合同修改")
    @NotNull
    private Integer bizType;
}
