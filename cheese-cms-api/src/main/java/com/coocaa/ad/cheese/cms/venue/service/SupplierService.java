package com.coocaa.ad.cheese.cms.venue.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDepositSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDepositSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierService;
import com.coocaa.ad.cheese.cms.common.tools.common.cos.ObjectUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.SupplierTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.supplier.ImportResult;
import com.coocaa.ad.cheese.cms.venue.bean.supplier.OldContractSupplierExcel;
import com.coocaa.ad.cheese.cms.venue.bean.supplier.SupplierBankExcelParam;
import com.coocaa.ad.cheese.cms.venue.bean.supplier.SupplierBankParam;
import com.coocaa.ad.cheese.cms.venue.bean.supplier.SupplierExcelParam;
import com.coocaa.ad.cheese.cms.venue.bean.supplier.SupplierParam;
import com.coocaa.ad.cheese.cms.venue.bean.supplier.SupplierQueryParam;
import com.coocaa.ad.cheese.cms.venue.convert.supplier.SupplierBankConvert;
import com.coocaa.ad.cheese.cms.venue.convert.supplier.SupplierConvert;
import com.coocaa.ad.cheese.cms.venue.vo.agent.CompanyBaseInfoVO;
import com.coocaa.ad.cheese.cms.venue.vo.supplier.SupplierBankVO;
import com.coocaa.ad.cheese.cms.venue.vo.supplier.SupplierDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.supplier.SupplierExcelVO;
import com.coocaa.ad.cheese.cms.venue.vo.supplier.SupplierVO;
import com.coocaa.ad.common.exception.CommonException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.util.AesUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SupplierService {

    private final ISupplierService supplierService;
    private final ISupplierBankService supplierBankService;
    private final AgentService agentService;
    private final IContractPaymentPeriodService contractPaymentPeriodService;
    private final IContractDepositSupplierService contractDepositSupplierService;
    private final IContractSupplierBankService contractSupplierBankService;

    /**
     * 供应商导入模板地址
     */
    @Value("${supplier.excel.template:https://test-**********.cos.ap-guangzhou.myqcloud.com/cms/supplier/2024/12/16/3ff1b1a3-67d1-407e-8ba0-3b7f13059885.xls}")
    private String supplierExcelTemplateUrl;

    /**
     * 获取供应商详情
     *
     * @param supplierId 供应商ID
     * @return 供应商详情
     */
    public SupplierDetailVO getSupplierDetail(Integer supplierId) {
        // 根据ID获取供应商信息；
        SupplierEntity supplierEntity = supplierService.getById(supplierId);
        if (supplierEntity == null) {
            throw new CommonException("供应商不存在");
        }
        SupplierDetailVO detailVO = SupplierConvert.INSTANCE.toSupplierDetailVO(supplierEntity);
        // 根据供应商ID获取银行卡信息
        List<SupplierBankEntity> bankEntities = supplierBankService.list(new LambdaQueryWrapper<SupplierBankEntity>().eq(SupplierBankEntity::getSupplierId, supplierId));
        List<SupplierBankVO> bankVOs = bankEntities.stream().map(SupplierBankConvert.INSTANCE::toDetailVO)
                .collect(Collectors.toList());

        // 组装成VO数据
        detailVO.setBankAccounts(bankVOs);
        return detailVO;
    }

    /**
     * 获取供应商详情（含台账）
     */
    public SupplierDetailVO getSupplierDetail(Integer supplierId, Integer contractId) {
        SupplierDetailVO detailVO = getSupplierDetail(supplierId);
        // 补全银行账号信息
        Optional.ofNullable(contractId).ifPresent(id -> {
            // 付款周期供应商银行
            Stream<SupplierBankVO> ppBankVos = contractPaymentPeriodService.lambdaQuery()
                    .eq(ContractPaymentPeriodEntity::getContractId, id)
                    .eq(ContractPaymentPeriodEntity::getSupplierId, supplierId)
                    .select(ContractPaymentPeriodEntity::getId, ContractPaymentPeriodEntity::getSupplierId,
                            ContractPaymentPeriodEntity::getAccountNo, ContractPaymentPeriodEntity::getAccountName,
                            ContractPaymentPeriodEntity::getBankName, ContractPaymentPeriodEntity::getBankCode)
                    .list().stream()
                    .filter(pp -> StringUtils.isNotBlank(pp.getAccountNo()))
                    .map(pp -> {
                        SupplierBankVO bankVO = new SupplierBankVO();
                        bankVO.setSupplierId(pp.getSupplierId());
                        bankVO.setAccountNo(pp.getAccountNo());
                        bankVO.setAccountName(pp.getAccountName());
                        bankVO.setBankName(pp.getBankName());
                        bankVO.setBankCode(pp.getBankCode());
                        return bankVO;
                    });
            // 押金供应商银行
            Stream<SupplierBankVO> depositBankVos = contractDepositSupplierService.lambdaQuery()
                    .eq(ContractDepositSupplierEntity::getContractId, id)
                    .eq(ContractDepositSupplierEntity::getSupplierId, supplierId)
                    .select(ContractDepositSupplierEntity::getId, ContractDepositSupplierEntity::getSupplierId,
                            ContractDepositSupplierEntity::getAccountNo, ContractDepositSupplierEntity::getAccountName,
                            ContractDepositSupplierEntity::getBankName, ContractDepositSupplierEntity::getBankCode)
                    .list().stream()
                    .filter(pp -> StringUtils.isNotBlank(pp.getAccountNo()))
                    .map(pp -> {
                        SupplierBankVO bankVO = new SupplierBankVO();
                        bankVO.setSupplierId(pp.getSupplierId());
                        bankVO.setAccountNo(pp.getAccountNo());
                        bankVO.setAccountName(pp.getAccountName());
                        bankVO.setBankName(pp.getBankName());
                        bankVO.setBankCode(pp.getBankCode());
                        return bankVO;
                    });
            // 合同供应商银行
            Stream<SupplierBankVO> contractSupplierBankVos = contractSupplierBankService.lambdaQuery()
                    .eq(ContractSupplierBankEntity::getContractId, id)
                    .eq(ContractSupplierBankEntity::getSupplierId, supplierId)
                    .select(ContractSupplierBankEntity::getId, ContractSupplierBankEntity::getSupplierId,
                            ContractSupplierBankEntity::getBankAccountCode, ContractSupplierBankEntity::getAccountName,
                            ContractSupplierBankEntity::getBankName, ContractSupplierBankEntity::getBankCode)
                    .list().stream()
                    .filter(pp -> StringUtils.isNotBlank(pp.getBankAccountCode()))
                    .map(pp -> {
                        SupplierBankVO bankVO = new SupplierBankVO();
                        bankVO.setSupplierId(pp.getSupplierId());
                        bankVO.setAccountNo(pp.getBankAccountCode());
                        bankVO.setAccountName(pp.getAccountName());
                        bankVO.setBankName(pp.getBankName());
                        bankVO.setBankCode(pp.getBankCode());
                        return bankVO;
                    });
            //
            detailVO.setBankAccounts(Stream.of(ppBankVos, depositBankVos, contractSupplierBankVos, detailVO.getBankAccounts().stream())
                    .flatMap(e -> e)
                    .collect(Collectors.toMap(SupplierBankVO::getAccountNo, e -> e, (o, n) -> n))
                    .values().stream().toList());
        });
        return detailVO;
    }

    /**
     * 创建或更新供应商
     *
     * @param applyParam 申请供应商的参数
     * @return 供应商ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer createOrUpdateSupplier(SupplierParam applyParam) {

        // 验证类型数据，如果类型为01，则需要验证统一社会信用代码，如果类型为02，则需要验证身份证号
        if (applyParam.getSupplierType().equals(SupplierTypeEnum.ENTERPRISE.getCode())) {
            if (applyParam.getUnifiedSocialCreditCode() == null) {
                throw new CommonException("企业类型必须填写统一社会信用代码");
            }
        } else if (applyParam.getSupplierType().equals(SupplierTypeEnum.PERSON.getCode())) {
            if (applyParam.getIdCard() == null) {
                throw new CommonException("个人类型必须填写身份证号");
            }
        }

        // 验证供应商名称是否重复
        SupplierEntity supplier = supplierService.getOne(new LambdaQueryWrapper<SupplierEntity>().eq(SupplierEntity::getSupplierCode, applyParam.getSupplierCode()));
        if (supplier != null && !supplier.getId().equals(applyParam.getId())) {
            throw new CommonException("供应商编码已存在");
        }

        // 保存或更细Supplier信息
        SupplierEntity entity = SupplierConvert.INSTANCE.toEntity(applyParam);
        boolean result = supplierService.saveOrUpdate(entity);
        Integer supplierId = entity.getId();
        if (!result || supplierId == null) {
            throw new CommonException("创建或更新供应商失败");
        }

        // 处理供应商关联银行信息
        List<SupplierBankParam> bankAccounts = applyParam.getBankAccounts();

        if (bankAccounts == null || bankAccounts.isEmpty()) {
            // 如果关联关系为空，则不更新银行账户信息
            return supplierId;
        } else {
            // 新增或更新银行账户信息
            for (SupplierBankParam bankAccount : bankAccounts) {
                SupplierBankEntity bankEntity = SupplierBankConvert.INSTANCE.toEntity(bankAccount);
                bankEntity.setSupplierId(entity.getId());
                try {
                    supplierBankService.saveOrUpdate(bankEntity);
                } catch (DuplicateKeyException e) {
                    log.error("违法唯一约束,存在相同的数据", e);
                    throw new RuntimeException("违法唯一约束,存在相同的数据");
                } catch (Exception e) {
                    log.error("保存银行账户失败", e);
                    throw new RuntimeException("保存银行账户失败");
                }
            }
        }
        return entity.getId();
    }

    /**
     * 分页获取供应商列表
     *
     * @param pageRequest 分页参数
     * @return 供应商分页数据结果
     */
    public PageResponseVO<SupplierVO> getSupplierList(PageRequestVO<SupplierQueryParam> pageRequest) {
        // 如果当前页码小于零或者为空，则设置为1
        if (pageRequest.getCurrentPage() == null || pageRequest.getCurrentPage() <= 0) {
            pageRequest.setCurrentPage(1L);
        }
        // 如果每页大小小于等于或者为空，则设置为20
        if (pageRequest.getPageSize() == null || pageRequest.getPageSize() <= 0) {
            pageRequest.setPageSize(VenueConstants.DEFAULT_PAGE_SIZE);
        }
        // 分页查询供应商列表
        SupplierQueryParam query = pageRequest.getQuery();
        // 按照query条件查询供应商列表，query为空则查询所有供应商,字段有值则按照字段查询，字段为空则不设置此字段的查询条件
        LambdaQueryWrapper<SupplierEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (query != null) {
            queryWrapper.like(StringUtils.isNotBlank(query.getSupplierName()), SupplierEntity::getSupplierName, query.getSupplierName())
                    .like(StringUtils.isNotBlank(query.getSupplierCode()), SupplierEntity::getSupplierCode, query.getSupplierCode())
                    .eq(query.getSupplierType() != null, SupplierEntity::getSupplierType, query.getSupplierType())
                    .eq(query.getStatus() != null, SupplierEntity::getStatus, query.getStatus());
        }

        Page<SupplierEntity> page = supplierService.page(new Page<>(pageRequest.getCurrentPage(), pageRequest.getPageSize()), queryWrapper);
        List<SupplierEntity> records = new ArrayList<>(page.getRecords());
        Integer includeSupplierId = query.getInclude();
        // 判断records中是否包含includeSupplierId，如果不包含且includeSupplierId不等于null则添加到records中
        Set<Integer> ids = records.stream().map(SupplierEntity::getId).collect(Collectors.toSet());
        if (includeSupplierId != null && !ids.contains(includeSupplierId)) {
            SupplierEntity supplierEntity = supplierService.getById(includeSupplierId);
            if (supplierEntity != null) {
                records.add(0, supplierEntity);
            }
        }

        List<SupplierVO> list = records.stream().map(SupplierConvert.INSTANCE::toSupplierVO)
                .collect(Collectors.toList());
        return new PageResponseVO<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list, page.getTotal());
    }

    /**
     * 批量导入供应商
     *
     * @param file 导入文件
     * @return 是否导入成功
     */
    @Transactional(rollbackFor = Exception.class)
    public ImportResult importSupplier(MultipartFile file) {
        String importMessage = "导入完成，所有数据导入成功！";
        if (file == null || file.isEmpty()) {
            throw new CommonException("上传文件不能为空");
        }

        try {
            // 读取Excel数据
            List<SupplierExcelParam> supplierData = readExcelSupplierInfo(file);
            List<SupplierBankExcelParam> bankData = readExcelSupplierBankInfo(file);

            if (supplierData != null && supplierData.size() > 1000) {
                throw new IllegalArgumentException("导入供应商数量不能超过1000条");
            }

            if (bankData != null && bankData.size() > 1000) {
                throw new IllegalArgumentException("导入供应商银行信息数量不能超过1000条");
            }

            // 导入供应商数据
            importSupplierData(supplierData);
            importSupplierBank(bankData);

            List<SupplierExcelVO> volist = supplierData.stream().map(bean -> convertToExcel(bean))
                    .collect(Collectors.toList());

            // 创建临时文件
            File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
            tempFile.deleteOnExit();

            try {
                // 写入Excel
                writeExcelFile(tempFile, volist, bankData);

                // 上传到云存储
                String fileUrl = uploadToCloud(tempFile);

                // 判断是否有错误
                boolean supplierError = supplierData.stream().anyMatch(bean -> bean.getImportResult().equals("失败"));
                boolean bankError = bankData.stream().anyMatch(bean -> bean.getImportResult().equals("失败"));

                if (supplierError || bankError) {
                    importMessage = "部分数据导入失败，请导出查看具体情况";
                    return ImportResult.builder().success(true).fileUrl(fileUrl).message(importMessage).build();
                } else {
                    return ImportResult.builder().success(true).build();
                }
            } finally {
                if (tempFile.exists()) {
                    tempFile.delete(); // 主动删除临时文件
                }
            }
        } catch (Exception e) {
            log.error("导入供应商异常：", e);
            return ImportResult.failure(e.getMessage());
        }
    }

    /**
     * 导入供应商银行信息
     *
     * @param supplierBankList
     */
    private void importSupplierBank(List<SupplierBankExcelParam> supplierBankList) {
        if (CollectionUtils.isEmpty(supplierBankList)) {
            return;
        }
        Map<String, Integer> codeIdMap = new HashMap<>();

        for (SupplierBankExcelParam bankAccount : supplierBankList) {
            boolean bankHasError = validBankRowData(bankAccount);
            if (bankHasError) {
                continue;
            }
            String supplierCode = bankAccount.getSupplierCode();
            SupplierBankEntity bankEntity = convertBankInfo(bankAccount);
            Integer supplierId = codeIdMap.get(supplierCode);
            if (supplierId == null) {
                SupplierEntity supplier = supplierService.getOne(new LambdaQueryWrapper<SupplierEntity>().eq(SupplierEntity::getSupplierCode, supplierCode));
                if (supplier != null) {
                    supplierId = supplier.getId();
                    codeIdMap.put(supplierCode, supplierId);
                    bankEntity.setSupplierId(supplierId);
                } else {
                    bankAccount.setImportResult("失败");
                    bankAccount.setMessage("供应商不存在");
                    continue;
                }
            } else {
                bankEntity.setSupplierId(supplierId);
            }
            try {
                supplierBankService.saveOrUpdate(bankEntity);
                bankAccount.setImportResult("成功");
                bankAccount.setMessage("");
            } catch (Exception e) {
                if (e instanceof DuplicateKeyException) {
                    bankAccount.setMessage("此银行账号已存在");
                } else {
                    bankAccount.setMessage("数据库保存失败");
                }
            }
        }
    }

    /**
     * 导入供应商信息
     *
     * @param supplierData
     */
    private void importSupplierData(List<SupplierExcelParam> supplierData) {
        if (supplierData.isEmpty()) {
            return;
        }

        // 验证供应商数据
        for (SupplierExcelParam rowParam : supplierData) {

            boolean hasError = validSupplierRowData(rowParam);
            if (hasError) {
                continue;
            }

            String rowSupplierCode = rowParam.getSupplierCode();
            if (StringUtils.isEmpty(rowSupplierCode)) {
                continue;
            }

            // 验证供应商名称是否重复
            SupplierEntity supplier = supplierService.getOne(new LambdaQueryWrapper<SupplierEntity>().eq(SupplierEntity::getSupplierCode, rowSupplierCode));
            if (supplier != null) {
                rowParam.setImportResult("失败");
                rowParam.setMessage("供应商编码已存在");
                continue;
            }

            // 保存或更细Supplier信息
            SupplierEntity entity = convertToSupplierEntity(rowParam);
            boolean result = supplierService.saveOrUpdate(entity);
            if (!result) {
                // 保存不成功
                rowParam.setImportResult("失败");
                rowParam.setMessage("数据库保存失败");
                continue;
            } else {
                rowParam.setImportResult("成功");
            }
        }
    }

    /**
     * 上传文件到COS
     *
     * @param file 导入文件
     * @return 是否导入成功
     */
    public ImportResult uploadFile(MultipartFile file, String fileName) {
        if (file == null || file.isEmpty()) {
            throw new CommonException("上传文件不能为空");
        }

        try {
            // 把file内容写入tempFile
            File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
            tempFile.deleteOnExit();
            try {
                file.transferTo(tempFile);
                // 上传到云存储
                String feature = "supplier";
                String finalName = fileName + ".xlsx";
                ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, finalName), tempFile);
                String accessUrl = ObjectUtils.getAccessUrl(feature, finalName);

                return ImportResult.success(accessUrl);
            } finally {
                if (tempFile.exists()) {
                    tempFile.delete(); // 主动删除临时文件
                }
            }
        } catch (Exception e) {
            log.error("上传模板异常：", e);
            return ImportResult.failure(e.getMessage());
        }
    }

    private SupplierExcelVO convertToExcel(SupplierExcelParam bean) {
        SupplierExcelVO vo = SupplierExcelVO.builder().importResult(bean.getImportResult())
                .organizeCode(bean.getOrganizeCode()).rowNum(bean.getRowNum()).supplierCode(bean.getSupplierCode())
                .supplierName(bean.getSupplierName()).message(bean.getMessage()).supplierType(bean.getSupplierType())
                .build();
        return vo;
    }

    private String uploadToCloud(File tempFile) {
        String feature = "supplier";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, fileName), tempFile);
        return ObjectUtils.getAccessUrl(feature, fileName);
    }

    private String uploadExcelToCloud(File tempFile) {
        String feature = "supplier";
        String fileName = "old-" + UUID.randomUUID().toString() + ".xlsx";
        ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, fileName), tempFile);
        return ObjectUtils.getAccessUrl(feature, fileName);
    }

    private void writeExcelFile(File tempFile, List<SupplierExcelVO> supplierData, List<SupplierBankExcelParam> bankData) {
        if (tempFile == null) {
            throw new IllegalArgumentException("临时文件不能为空");
        }
        if (supplierData == null) {
            throw new IllegalArgumentException("供应商不能为空");
        }
        // 使用同一个ExcelWriter写入多个sheet
        try (ExcelWriter excelWriter = EasyExcel.write(tempFile).build()) {
            // 写入第一个sheet
            WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "供应商信息").head(SupplierExcelVO.class).build();
            excelWriter.write(supplierData, writeSheet1);

            // 写入第二个sheet
            if (CollectionUtils.isNotEmpty(bankData)) {
                WriteSheet writeSheet2 = EasyExcel.writerSheet(1, "银行账户信息").head(SupplierBankExcelParam.class)
                        .build();
                excelWriter.write(bankData, writeSheet2);
            }
        } catch (Exception e) {
            log.error("写入Excel文件失败", e);
            throw new CommonException("写入Excel文件失败: " + e.getMessage());
        }

    }


    private void writeOldExcelFile(File tempFile, List<OldContractSupplierExcel> supplierData) {
        if (tempFile == null) {
            throw new IllegalArgumentException("临时文件不能为空");
        }
        if (supplierData == null) {
            throw new IllegalArgumentException("供应商不能为空");
        }
        // 使用同一个ExcelWriter写入多个sheet
        try (ExcelWriter excelWriter = EasyExcel.write(tempFile).build()) {
            // 写入第一个sheet
            WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "供应商信息").head(OldContractSupplierExcel.class)
                    .build();
            excelWriter.write(supplierData, writeSheet1);
        } catch (Exception e) {
            log.error("写入Excel文件失败", e);
            throw new CommonException("写入Excel文件失败: " + e.getMessage());
        }

    }


    /**
     * @param supplierData
     * @param bankData
     * @return
     */
    public void importExcelData(List<SupplierExcelParam> supplierData, List<SupplierBankExcelParam> bankData) {

        if (supplierData.isEmpty()) {
            throw new CommonException("供应商数据不能为空");
        }
        if (CollectionUtils.isEmpty(bankData)) {
            bankData = new ArrayList<>();
        }

        Map<String, List<SupplierBankExcelParam>> bankDataMap = bankData.stream()
                .collect(Collectors.groupingBy(SupplierBankExcelParam::getSupplierCode));

        // 验证供应商数据
        for (SupplierExcelParam rowParam : supplierData) {

            boolean hasError = validSupplierRowData(rowParam);
            if (hasError) {
                continue;
            }

            String rowSupplierCode = rowParam.getSupplierCode();
            if (StringUtils.isEmpty(rowSupplierCode)) {
                continue;
            }

            List<SupplierBankExcelParam> supplierBankList = bankDataMap.get(rowSupplierCode);

            // 验证供应商名称是否重复
            SupplierEntity supplier = supplierService.getOne(new LambdaQueryWrapper<SupplierEntity>().eq(SupplierEntity::getSupplierCode, rowSupplierCode));
            if (supplier != null) {
                rowParam.setImportResult("失败");
                rowParam.setMessage("供应商编码已存在");
                continue;
            }

            // 保存或更细Supplier信息
            SupplierEntity entity = convertToSupplierEntity(rowParam);
            boolean result = supplierService.saveOrUpdate(entity);
            if (!result) {
                // 保存不成功
                rowParam.setImportResult("失败");
                rowParam.setMessage("数据库保存失败");
                continue;
            } else {
                rowParam.setImportResult("成功");
            }

            if (CollectionUtils.isEmpty(supplierBankList)) {
                continue;
            }

            // 新增或更新银行账户信息
            for (SupplierBankExcelParam bankAccount : supplierBankList) {
                boolean bankHasError = validBankRowData(bankAccount);
                if (bankHasError) {
                    continue;
                }
                SupplierBankEntity bankEntity = convertBankInfo(bankAccount);
                bankEntity.setSupplierId(entity.getId());
                try {
                    supplierBankService.saveOrUpdate(bankEntity);
                    bankAccount.setImportResult("成功");
                    bankAccount.setMessage("");
                } catch (Exception e) {
                    bankAccount.setMessage(e.getMessage());
                }
            }
        }
    }

    private SupplierBankEntity convertBankInfo(SupplierBankExcelParam param) {
        SupplierBankEntity entity = new SupplierBankEntity();
        entity.setAccountName(param.getAccountName());
        entity.setAccountNo(param.getBankAccountNo());
        entity.setBankCode(param.getBankCode());
        entity.setBankName(param.getBankName());
        return entity;
    }

    /**
     * 校验数据是否完整
     *
     * @param rowParam
     * @return
     */
    private boolean validBankRowData(SupplierBankExcelParam rowParam) {
        if (rowParam == null) {
            return true;
        }

        if (StringUtils.isEmpty(rowParam.getSupplierCode())) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("供应商编码为空");
            return true;
        }
        if (StringUtils.isEmpty(rowParam.getAccountName())) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("户名为空");
            return true;
        }
        if (StringUtils.isEmpty(rowParam.getBankName())) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("开户银行为空");
            return true;
        }
        if (StringUtils.isEmpty(rowParam.getBankCode())) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("银联号为空");
            return true;
        }
        if (StringUtils.isEmpty(rowParam.getBankAccountNo())) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("银行账号为空");
            return true;
        }

        return false;
    }

    private SupplierEntity convertToSupplierEntity(SupplierExcelParam excelParam) {
        SupplierEntity entity = new SupplierEntity();
        entity.setSupplierName(excelParam.getSupplierName());
        entity.setSupplierCode(excelParam.getSupplierCode());

        if (org.apache.commons.lang3.StringUtils.equals(SupplierTypeEnum.PERSON.getDesc(), excelParam.getSupplierType())) {
            entity.setSupplierType(SupplierTypeEnum.PERSON.getCode());
            entity.setIdCard(excelParam.getOrganizeCode());
        } else if (org.apache.commons.lang3.StringUtils.equals(SupplierTypeEnum.ENTERPRISE.getDesc(), excelParam.getSupplierType())) {
            entity.setSupplierType(SupplierTypeEnum.ENTERPRISE.getCode());
            entity.setUnifiedSocialCreditCode(excelParam.getOrganizeCode());
        }

        return entity;
    }

    private boolean validSupplierRowData(SupplierExcelParam rowParam) {
        if (rowParam == null) {
            return true;
        }
        if (StringUtils.isEmpty(rowParam.getSupplierName())) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("供应商名称为空");
            return true;
        }
        if (StringUtils.isEmpty(rowParam.getSupplierCode())) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("供应商编码为空");
            return true;
        }
        if (StringUtils.isEmpty(rowParam.getSupplierType())) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("供应商类型为空");
            return true;
        }
        if (StringUtils.isEmpty(rowParam.getOrganizeCode())) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("组织编码为空");
            return true;
        }

        return false;
    }


    /**
     * 读取Excel 供应商银行信息
     *
     * @param file
     * @return
     */
    private List<SupplierBankExcelParam> readExcelSupplierBankInfo(MultipartFile file) {
        List<SupplierBankExcelParam> bankData = new ArrayList<>();

        try {
            // Reset input stream for second sheet
            InputStream fileInputStream = file.getInputStream();

            // 读第二个表单
            EasyExcel.read(fileInputStream, SupplierBankExcelParam.class, new AnalysisEventListener<SupplierBankExcelParam>() {
                private int rowIndex = 0;

                @Override
                public void invoke(SupplierBankExcelParam data, AnalysisContext context) {
                    rowIndex++;
                    data.setRowNum(rowIndex);
                    data.setImportResult("失败");
                    data.setMessage("未找到供应商");
                    bankData.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Sheet 2 读取完成，总行数：{}", rowIndex);
                }
            }).sheet(1).doRead();

            return bankData;
        } catch (IOException e) {
            log.error("读取Excel文件失败", e);
            throw new RuntimeException("读取Excel文件失败", e);
        }
    }

    /**
     * 读取Excel 供应商信息
     *
     * @param file
     * @return
     */
    private List<SupplierExcelParam> readExcelSupplierInfo(MultipartFile file) {
        List<SupplierExcelParam> supplierData = new ArrayList<>();
        try {
            InputStream fileInputStream = file.getInputStream();

            EasyExcel.read(fileInputStream, SupplierExcelParam.class, new AnalysisEventListener<SupplierExcelParam>() {
                private int rowIndex = 0;

                @Override
                public void invoke(SupplierExcelParam data, AnalysisContext context) {
                    rowIndex++;
                    data.setRowNum(rowIndex);
                    supplierData.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("供应商信息读取完成，总行数：{}", rowIndex);
                }
            }).sheet(0).doRead();

        } catch (IOException e) {
            log.error("读取Excel供应商信息失败", e);
            throw new RuntimeException("读取Excel文件失败", e);
        }
        return supplierData;
    }


    /**
     * 下载供应商导入模板
     */
    public String downloadSupplierTemplate() {
        return supplierExcelTemplateUrl;
    }

    public void processImportSupplier(MultipartFile supplierFileInfo) {
        List<OldContractSupplierExcel> supplierExcelList = readOldContractExcelSupplierInfo(supplierFileInfo);
        if (CollectionUtils.isEmpty(supplierExcelList)) {
            log.info("没有需要导入的供应商");
            return;
        }
        log.info("开始导入供应商,共{}条数据", supplierExcelList.size());

        for (OldContractSupplierExcel supplierExcel : supplierExcelList) {
            Integer idNum = supplierExcel.getIdNum();
            boolean result = false;
            try {
                result = processOldContractSupplier(supplierExcel);
            } catch (Exception e) {
                log.error("导入供应商异常,序号：{}", idNum, e);
            }
            if (result) {
                log.info("导入供应商成功,序号：{}", idNum);
                supplierExcel.setImportResult("成功");
            } else {
                log.info("导入供应商失败,序号：{}", idNum);
                supplierExcel.setImportResult("失败");
            }
        }

        try {
            // 创建临时文件
            File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
            tempFile.deleteOnExit();

            try {
                // 写入Excel
                writeOldExcelFile(tempFile, supplierExcelList);
                // 上传到云存储
                uploadExcelToCloud(tempFile);


            } finally {
                if (tempFile.exists()) {
                    tempFile.delete(); // 主动删除临时文件
                }
            }
        } catch (IOException e) {
            log.error("导出供应商模板异常", e);
        }
        log.info("导入供应商结束,已上传到COS");

    }

    /**
     * @param supplierExcel
     * @return
     */
    private boolean processOldContractSupplier(OldContractSupplierExcel supplierExcel) {
        if (supplierExcel == null) {
            return false;
        }

        String supplierName = supplierExcel.getSupplierName();
        if (StringUtils.isEmpty(supplierName)) {
            supplierExcel.setImportResult("失败");
            supplierExcel.setMessage("供应商名称为空");
            return false;
        }

        // 查询供应商表是否存在
        SupplierEntity supplierEntity = supplierService.lambdaQuery()
                .eq(SupplierEntity::getSupplierName, supplierName)
                .last("limit 1").one();
        if (supplierEntity == null) {
            supplierExcel.setMessage("供应商不存在,新增供应商");
            supplierEntity = new SupplierEntity();
            supplierEntity.setSupplierName(supplierName);
            supplierEntity.setSupplierCode("IMP-" + System.currentTimeMillis());
            supplierEntity.setCreator(0);
            supplierEntity.setOperator(0);
            supplierEntity.setStatus(1);
            // 天眼查获取供应商信息
            try {
                List<CompanyBaseInfoVO> companyInfoList = agentService.getCompanyInfoList(supplierName, 10, 1);

                if (CollectionUtils.isNotEmpty(companyInfoList)) {
                    for (CompanyBaseInfoVO companyBaseInfoVO : companyInfoList) {
                        if (StringUtils.equals(companyBaseInfoVO.getName(), supplierName)) {
                            supplierEntity.setUnifiedSocialCreditCode(companyBaseInfoVO.getUnifiedSocialCreditCode());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("天眼查获取供应商信息失败");
            }
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                log.error("线程休眠异常", e);
            }

            // 保存供应商信息
            supplierService.saveOrUpdate(supplierEntity);
        } else {
            supplierExcel.setMessage("供应商已存在");
        }

        String payerAccount = supplierExcel.getPayerAccount();
        if (StringUtils.isEmpty(payerAccount)) {
            supplierExcel.setMessage(supplierExcel.getMessage() + "银行账户为空，不新增银行账户");
            return true;
        }

        // 保存银行信息
        SupplierBankEntity bankEntity = supplierBankService.lambdaQuery()
                .eq(SupplierBankEntity::getSupplierId, supplierEntity.getId())
                .eq(SupplierBankEntity::getAccountNo, encrypt(payerAccount))
                .last("limit 1").one();
        if (bankEntity == null) {
            bankEntity = new SupplierBankEntity();
            bankEntity.setSupplierId(supplierEntity.getId());
            bankEntity.setAccountNo(payerAccount);
            bankEntity.setAccountName(supplierExcel.getPayerName());
            bankEntity.setBankName(supplierExcel.getPayerBankAddr());
            bankEntity.setCreator(0);
            bankEntity.setOperator(0);
            supplierExcel.setMessage(supplierExcel.getMessage() + "，新增银行信息");
        } else {
            bankEntity.setAccountName(supplierExcel.getPayerName());
            bankEntity.setBankName(supplierExcel.getPayerBankAddr());
            supplierExcel.setMessage(supplierExcel.getMessage() + "，更新银行信息");
        }
        supplierExcel.setMessage(supplierExcel.getMessage() + "，银行信息成功");
        supplierBankService.saveOrUpdate(bankEntity);

        return true;
    }

    private String encrypt(String value) {
        try {
            return org.apache.commons.lang3.StringUtils.isEmpty(value) ? null : AesUtils.encryptHex(value);
        } catch (Exception ex) {
            log.warn("加密存储数据({})错误", value, ex);
            return value;
        }
    }

    private List<OldContractSupplierExcel> readOldContractExcelSupplierInfo(MultipartFile file) {
        List<OldContractSupplierExcel> supplierData = new ArrayList<>();
        try {
            InputStream fileInputStream = file.getInputStream();

            EasyExcel.read(fileInputStream, OldContractSupplierExcel.class, new AnalysisEventListener<OldContractSupplierExcel>() {
                private int rowIndex = 0;

                @Override
                public void invoke(OldContractSupplierExcel data, AnalysisContext context) {
                    rowIndex++;
                    data.setRowNum(rowIndex);
                    supplierData.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("供应商信息读取完成，总行数：{}", rowIndex);
                }
            }).sheet(0).doRead();

        } catch (IOException e) {
            log.error("读取Excel供应商信息失败", e);
            throw new RuntimeException("读取Excel文件失败", e);
        }
        return supplierData;
    }

    /**
     * 更新供应商统一社会信用编码
     *
     * @param ids
     * @return
     */
    public List<SupplierEntity> updateSocialCode(List<Integer> ids) {
        List<SupplierEntity> allSupplier = null;
        if (CollectionUtils.isEmpty(ids)) {
            allSupplier = supplierService.lambdaQuery().list();
        } else {
            allSupplier = supplierService.lambdaQuery().in(SupplierEntity::getId, ids).list();
        }

        if (CollectionUtils.isEmpty(allSupplier)) {
            return null;
        }
        for (SupplierEntity supplierEntity : allSupplier) {
            String supplierName = supplierEntity.getSupplierName();
            Integer supplierType = supplierEntity.getSupplierType();
            String unifiedSocialCreditCode = supplierEntity.getUnifiedSocialCreditCode();
            if (supplierType == null || supplierType != 1) {
                continue;
            }
            if (StringUtils.isEmpty(supplierName) || StringUtils.isNotEmpty(unifiedSocialCreditCode)) {
                continue;
            }
            try {
                List<CompanyBaseInfoVO> companyInfoList = agentService.getCompanyInfoList(supplierName, 1, 10);
                if (CollectionUtils.isEmpty(companyInfoList)) {
                    continue;
                }
                for (CompanyBaseInfoVO companyBaseInfoVO : companyInfoList) {
                    if (StringUtils.equals(companyBaseInfoVO.getName(), supplierName)) {
                        supplierEntity.setUnifiedSocialCreditCode(companyBaseInfoVO.getUnifiedSocialCreditCode());
                    }
                }
            } catch (Exception e) {
                log.error("天眼查获取供应商信息失败");
            }
        }
        supplierService.updateBatchById(allSupplier);
        return allSupplier;
    }
}
