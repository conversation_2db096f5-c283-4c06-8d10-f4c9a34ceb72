package com.coocaa.ad.cheese.cms.venue.service;

import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ConfigEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IConfigService;
import com.coocaa.ad.cheese.cms.venue.bean.comment.ConfigStatusParam;
import com.coocaa.ad.cheese.cms.venue.convert.ConfigConvert;
import com.coocaa.ad.cheese.cms.venue.vo.ConfigVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 售卖服务配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-13
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ConfigService {
    private static final String CONFIG_CACHE_KEY = "cms:config";
    private final IConfigService configService;
    private final StringRedisTemplate redisTemplate;

    /**
     * 查询配置值
     */
    public String getConfigValue(String code) {
        return Optional.ofNullable(getConfigEntity(code)).map(ConfigEntity::getValue).orElse(null);
    }

    /**
     * 查询配置
     */
    public ConfigVO getConfig(String code) {
        return Optional.ofNullable(getConfigEntity(code)).map(ConfigConvert.INSTANCE::toVo).orElse(null);
    }

    /**
     * 根据父编码查询配置
     */
    public List<ConfigVO> listConfigByParent(String parentCode) {
        if (StringUtils.isBlank(parentCode)) {
            return Collections.emptyList();
        }

        return configService.lambdaQuery()
                .eq(ConfigEntity::getParentCode, parentCode)
                .list().stream()
                .map(ConfigConvert.INSTANCE::toVo).toList();
    }

    /**
     * 修改启用禁用状态
     */
    public Boolean updateConfigStatus(String code, ConfigStatusParam param) {

        boolean updated = configService.lambdaUpdate()
                .set(ConfigEntity::getStatus, param.getStatus())
                .eq(ConfigEntity::getCode, code)
                .update();

        // 更新缓存
        if (updated) {
            ConfigEntity entity = configService.lambdaQuery()
                    .eq(ConfigEntity::getCode,  code)
                    .one();
            updateCache(entity);
        }

        return updated;
    }

    /**
     * 创建或修改配置
     */
    public boolean createOrUpdate(ConfigVO config) {
        // 查询已存在的配置
        ConfigEntity existed = configService.lambdaQuery()
                .eq(ConfigEntity::getCode, config.getCode())
                .eq(ConfigEntity::getParentCode, config.getParentCode())
                .one();
        // 校验同一个parent_code是否有相同code
        checkCode(existed, config);

        ConfigEntity newConfig = ConfigConvert.INSTANCE.toEntity(config);
        boolean result;
        if (Objects.isNull(existed)) {
            newConfig.setStatus(BooleFlagEnum.YES.getCode());
            result = configService.save(newConfig);
        } else {
            newConfig.setId(existed.getId());
            result = configService.updateById(newConfig);
        }

        // 更新缓存
        if (result) {
            updateCache(configService.getById(newConfig.getId()));
        }

        return result;
    }

    /**
     * 刷新缓存
     */
    @PostConstruct
    public boolean refreshCache() {
        log.info("重新缓存所有配置信息");
        // 查出所有配置
        List<ConfigEntity> configs = configService.lambdaQuery()
                .select(ConfigEntity::getId, ConfigEntity::getName, ConfigEntity::getCode, ConfigEntity::getValue,
                        ConfigEntity::getExt1, ConfigEntity::getExt2, ConfigEntity::getExt3)
                .eq(ConfigEntity::getStatus, BooleFlagEnum.YES.getCode()).list();
        redisTemplate.delete(CONFIG_CACHE_KEY);

        // 重新缓存所有配置
        if (CollectionUtils.isNotEmpty(configs)) {
            Map<String, String> configMap = configs.stream()
                    .filter(config -> StringUtils.isNotBlank(config.getCode()))
                    .collect(Collectors.toMap(config -> getKey(config.getCode()), JSON::toJSONString));
            redisTemplate.opsForHash().putAll(CONFIG_CACHE_KEY, configMap);
            log.info("重新缓存所有[{}]条配置成功", configs.size());
        }
        return true;
    }


    /**
     * 根据编码查配置
     */
    private ConfigEntity getConfigEntity(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        // 优先查缓存
        String json = redisTemplate.<String, String>opsForHash().get(CONFIG_CACHE_KEY, getKey(code));
        ConfigEntity config = null;
        if (StringUtils.isNotBlank(json)) {
            config = JSON.parseObject(json, ConfigEntity.class);
        }

        // 再查数据库
        if (Objects.isNull(config)) {
            config = configService.lambdaQuery()
                    .select(ConfigEntity::getId, ConfigEntity::getName, ConfigEntity::getCode, ConfigEntity::getValue,
                            ConfigEntity::getExt1, ConfigEntity::getExt2, ConfigEntity::getExt3)
                    .eq(ConfigEntity::getCode, code).last("LIMIT 1").one();

            // 更新缓存
            if (Objects.nonNull(config)) {
                updateCache(config);
            }
        }

        return config;
    }

    /**
     * 校验同一个parent_code是否有相同code
     */
    private void checkCode(ConfigEntity existed, ConfigVO config) {

        ConfigEntity repeat;
        if (Objects.nonNull(existed) && Objects.nonNull(existed.getId())) {
            repeat = configService.lambdaQuery()
                    .eq(ConfigEntity::getCode, config.getCode())
                    .ne(ConfigEntity::getId, existed.getId())
                    .last("LIMIT 1")
                    .one();
        } else {
            repeat = configService.lambdaQuery()
                    .eq(ConfigEntity::getCode, config.getCode())
                    .last("LIMIT 1")
                    .one();
        }

        if (Objects.nonNull(repeat)) {
            throw new BusinessException("该配置已存在");
        }
    }

    /**
     * 更新缓存
     */
    private boolean updateCache(ConfigEntity config) {
        if (Objects.isNull(config) || StringUtils.isBlank(config.getCode())) {
            return false;
        }

        // 更新缓存
        redisTemplate.opsForHash().put(CONFIG_CACHE_KEY, getKey(config.getCode()), JSON.toJSONString(config));
        return true;
    }

    /**
     * 生成缓存Key
     */
    private String getKey(String code) {
        return StringUtils.isBlank(code) ? "null" : code.trim().toLowerCase();
    }
}
