package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.util.translate.VenueTransTypes;
import com.coocaa.ad.translate.anno.TransField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 变更合同导出VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-12 14:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ContractAmendmentExportVO extends ContractExportVO {
    /**
     * 合同ID
     */
    @ExcelIgnore
    private Integer id;

    @ExcelIgnore
    private Integer businessType;

    @ExcelIgnore
    private Integer normalFlag;

    @ExcelIgnore
    private Integer supplier1;

    @ExcelIgnore
    private Integer supplier2;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.USER, target = "followerName")
    private Integer follower;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.USER, target = "creatorName")
    private Integer creator;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.DICT, target = "applyStatusName")
    private String applyStatus;


    @ExcelProperty(value = "合同编号")
    private String contractCode;

    @ExcelProperty(value = "变更申请编号")
    private String applyCode;

    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ExcelProperty(value = "物业类型")
    private String propertyTypeName;

    @ExcelProperty(value = "城市")
    private String cityName;

    @ExcelProperty(value = "楼宇等级")
    private String buildingLevel;

    @ExcelProperty(value = "楼宇AI等级")
    private String aiBuildingLevel;

    @ExcelProperty(value = "状态")
    private String applyStatusName;

    @ExcelProperty(value = "标准合同")
    private String normalFlagName;

    @ExcelProperty(value = "供应商1")
    private String supplierName1;

    @ExcelProperty(value = "供应商2")
    private String supplierName2;

    @ExcelProperty(value = "业务类型")
    private String businessTypeName;

    @ExcelProperty(value = "公司")
    private String agentName;

    @ExcelProperty(value = "是否大屏")
    private String largeScreen;

    @ExcelProperty(value = "尺寸规格")
    private String sizeSpec;

    @ExcelProperty(value = "合同总金额(元)")
    private BigDecimal totalAmount;

    @ExcelProperty(value = "合同开始时间")
    @DateTimeFormat(VenueConstants.DATE_FORMAT)
    private LocalDate startDate;

    @ExcelProperty(value = "合同结束时间")
    @DateTimeFormat(VenueConstants.DATE_FORMAT)
    private LocalDate endDate;

    @ExcelProperty(value = "合同年限")
    private BigDecimal period;

    @ExcelProperty(value = "签约数量")
    private Integer signCount;

    @ExcelProperty(value = "申请人")
    private String creatorName;

    @ExcelProperty(value = "申请时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime applyTime;

    @ExcelProperty(value = "跟进人")
    private String followerName;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.DICT, target = "propertyTypeName")
    private List<String> propertyTypeList;

    @ExcelProperty(value = "是否归档")
    private String archiveFlagName;

    @ExcelProperty(value = "是否生效")
    private String effectFlagName;

    @ExcelIgnore
    private Integer effectFlag;

    @ExcelIgnore
    private Integer archiveFlag;

    @Override
    public Integer getContractId() {
        return id;
    }
}

