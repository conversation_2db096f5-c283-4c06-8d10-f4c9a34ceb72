package com.coocaa.ad.cheese.cms.venue.vo.supplier;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.coocaa.ad.cheese.cms.venue.bean.supplier.SupplierBankExcelParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierExcelVO {

    /**
     * 供应商编码
     */
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 供应商类型
     */
    @ExcelProperty(value = "供应商类型")
    private String supplierType;

    /**
     * 组织编码
     * （企业：填统一社会信用代码；个人：填身份证号）
     */
    @ExcelProperty(value = "组织编码")
    private String organizeCode;

    /**
     * 数据行数
     */
    @ExcelIgnore
    private Integer rowNum;


    /**
     * 导入结果
     */
    @ExcelProperty(value = "导入结果")
    private String importResult;

    /**
     * 导入信息
     */
    @ExcelProperty(value = "导入信息")
    private String message;


}


