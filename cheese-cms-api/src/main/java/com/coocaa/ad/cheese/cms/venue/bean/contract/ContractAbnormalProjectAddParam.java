package com.coocaa.ad.cheese.cms.venue.bean.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 异常合同添加入参
 *
 * <AUTHOR>
 * @since 2025/3/24
 */
@Data
public class ContractAbnormalProjectAddParam {

    @Schema(description = "签约点位数", type = "Int", example = "0")
    @NotNull(message = "签约点位数不能为空")
    private Integer signCount;

    @Schema(description = "异常点位数", type = "Int", example = "0")
    @NotNull(message = "异常点位数不能为空")
    private Integer abnormalCount;

    @Schema(description = "付款金额", type = "BigDecimal", example = "0.00")
    private BigDecimal paidAmount;

    @Schema(description = "回票金额", type = "BigDecimal", example = "0.00")
    private BigDecimal returnInvoicedAmount;

    @Schema(description = "安装点位", type = "Int", example = "1")
    @NotNull(message = "安装点位不能为空")
    private Integer installCount;

    @Schema(description = "商机名称", type = "Int", example = "1")
    @NotEmpty(message = "商机名称不能为空")
    private String projectName;

    @Schema(description = "商机号", type = "String", example = "1")
    @NotEmpty(message = "商机号不能为空")
    private String projectCode;

    private Integer cityId;

    private Integer regionId;
}
