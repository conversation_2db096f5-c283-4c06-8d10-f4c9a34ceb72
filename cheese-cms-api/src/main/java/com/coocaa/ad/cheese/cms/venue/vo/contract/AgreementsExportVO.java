package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.util.translate.VenueTransTypes;
import com.coocaa.ad.translate.anno.TransField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 补充协议导出
 *
 * <AUTHOR>
 * @since 2025/2/26
 */
@Data
public class AgreementsExportVO implements IFillingProject, IFillingSupplier, IFillingCity {
    /**
     * 合同ID
     */
    @ExcelIgnore
    private Integer id;

    @ExcelIgnore
    private Integer businessType;

    @ExcelIgnore
    private Integer normalFlag;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.DICT, target = "formalStatusName")
    private String formalStatus;

    @ExcelIgnore
    private Integer supplier1;

    @ExcelIgnore
    private Integer supplier2;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.USER, target = "followerName")
    private Integer follower;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.USER, target = "creatorName")
    private Integer creator;

    @ExcelIgnore
    private Integer partyAStampStatus;

    @ExcelIgnore
    private Integer partyBStampStatus;

    @ExcelIgnore
    private Integer partyCStampStatus;

    @ExcelIgnore
    private Integer uploadFlag;

    @ExcelIgnore
    private Integer uploadSubFlag;

    @ExcelIgnore
    private Integer archiveFlag;

    @ExcelIgnore
    private Integer abnormalFlag;


    @ExcelProperty(value = "原合同编号")
    private String contractCode;

    @ExcelProperty(value = "申请编号")
    private String applyCode;

    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ExcelIgnore
    private String projectCode;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.DICT, target = "propertyTypeName")
    private String propertyType;

    @ExcelProperty(value = "物业类型")
    private String propertyTypeName;

    @ExcelProperty(value = "所属大区")
    private String region;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.CITY, target = "cityName")
    private Integer cityId;

    @ExcelProperty(value = "城市")
    private String cityName;

    @ExcelIgnore
    private Integer stopFlag;
    @ExcelProperty(value = "终止协议")
    private String stopFlagName;

    @ExcelProperty(value = "楼宇等级")
    private String buildingLevel;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.DICT, target = "applyStatusName")
    private String applyStatus;
    @ExcelProperty(value = "状态")
    private String applyStatusName;

    @ExcelProperty(value = "合同状态")
    private String formalStatusName;

    @ExcelProperty(value = "异常状态")
    private String abnormalFlagName;

    @ExcelProperty(value = "标准合同")
    private String normalFlagName;

    @ExcelProperty(value = "供应商1")
    private String supplierName1;

    @ExcelProperty(value = "供应商2")
    private String supplierName2;

    @ExcelProperty(value = "业务类型")
    private String businessTypeName;

    @ExcelProperty(value = "公司")
    private String agentName;

    @ExcelIgnore
    private BigDecimal depositAmount;

    @ExcelProperty(value = "押金（元）")
    private String depositAmountStr;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.DICT, target = "invoiceTypeName")
    private String invoiceType;

    @ExcelProperty(value = "发票类型")
    private String invoiceTypeName;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.DICT, target = "taxPointName")
    private String taxPoint;

    @ExcelProperty(value = "税点")
    private String taxPointName;

    @ExcelProperty(value = "合同总金额(元)")
    private BigDecimal totalAmount;

    @ExcelProperty(value = "合同开始时间")
    @DateTimeFormat(VenueConstants.DATE_FORMAT)
    private LocalDate startDate;

    @ExcelProperty(value = "合同结束时间")
    @DateTimeFormat(VenueConstants.DATE_FORMAT)
    private LocalDate endDate;

    @ExcelProperty(value = "合同年限")
    private BigDecimal period;

    @ExcelProperty(value = "付款方式")
    private String paymentTypeName;

    @ExcelProperty(value = "变更状态")
    private String changeStatus;

    @ExcelProperty(value = "签约数量")
    private Integer firstSignCount;

    @ExcelIgnore
    private Integer lastSignCount;

    @ExcelProperty(value = "变更后签约数量")
    private String lastSignCountStr;

    @ExcelProperty(value = "是否派工单")
    private String workOrderStatus;

    @ExcelProperty(value = "验收数量")
    private Integer finishCount;

    @ExcelProperty(value = "验收数-签约数")
    private Integer realCount;

    @ExcelProperty(value = "收款供应商名称")
    private String supplierName;

    @ExcelProperty(value = "供应商收款户名")
    private String accountName;

    @ExcelProperty(value = "供应商开户行")
    private String bankName;

    @ExcelProperty(value = "供应商收款账号")
    private String accountNo;

    @ExcelProperty(value = "联系人")
    private String contactPerson;

    @ExcelProperty(value = "联系电话")
    private String contactPhone;

    @ExcelProperty(value = "联系地址")
    private String contactAddress;

    @ExcelProperty(value = "期数")
    private Integer periods;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.DICT, target = "feeTypeName")
    private String feeType;

    @ExcelProperty(value = "费用类型")
    private String feeTypeName;

    @ExcelProperty(value = "付款周期")
    private String payPeriod;

    @ExcelIgnore
    @TransField(type = VenueTransTypes.DICT, target = "paymentTypeName")
    private String paymentType;

    @ExcelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ExcelProperty(value = "计划付款日")
    private LocalDate plannedPaymentDate;

    @ExcelProperty(value = "已付金额")
    private BigDecimal paidAmount;

    @ExcelProperty(value = "已回票金额")
    private BigDecimal returnInvoicedAmount;

    @ExcelProperty(value = "申请人")
    private String creatorName;

    @ExcelProperty(value = "申请时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime applyTime;

    @ExcelProperty(value = "跟进人")
    private String followerName;

    @ExcelProperty(value = "合同归档状态")
    private String archiveFlagName;

    @ExcelProperty(value = "归档时间")
    @DateTimeFormat(VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime archiveTime;


    @Override
    public Integer getContractId() {
        return id;
    }

}
