package com.coocaa.ad.cheese.cms.venue.rpc.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 审批发起参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-04
 */
@Data
public class ApprovalInitiateParam {
    
    @Schema(description = "审批类型")
    @NotBlank(message = "审批类型不能为空")
    private String approveType;

    @Schema(description = "审批表单数据")
    @NotBlank(message = "审批表单不能为空") 
    private String formStr;

    @Schema(description = "流程实例code")
    private String processCode;
} 