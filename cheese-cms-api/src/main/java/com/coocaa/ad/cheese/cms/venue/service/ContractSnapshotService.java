package com.coocaa.ad.cheese.cms.venue.service;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAttachmentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDepositSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDeviceEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPriceApplyEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPricePeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSnapshotEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSubEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.LedgerEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAttachmentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDepositSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDeviceService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPriceApplyService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPricePeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSnapshotService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSubService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.ILedgerService;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractSignTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractSnapshotParam;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailDiffVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.common.exception.CommonException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * 合同快照数据服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-12 19:57
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractSnapshotService {
    private final IContractSnapshotService contractSnapshotService;
    private final IContractService contractService;
    private final IContractSupplierService contractSupplierService;
    private final IContractProjectService contractProjectService;
    private final IContractPriceApplyService contractPriceApplyService;
    private final IContractDeviceService contractDeviceService;
    private final IContractDevicePointService contractDevicePointService;
    private final IContractPricePeriodService contractPricePeriodService;
    private final IContractPaymentPeriodService contractPaymentPeriodService;
    private final IContractSubService contractSubService;
    private final IContractSupplierBankService contractSupplierBankService;
    private final IContractAttachmentService contractAttachmentService;
    private final IContractDepositSupplierService contractDepositSupplierService;
    private final ILedgerService ledgerService;
    private final ContractReadService contractReadService;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            // 注册模块
            .registerModule(new JavaTimeModule())
            // 禁用时间戳
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            // 设置时间格式
            .setDateFormat(new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN));

    public ContractDetailDiffVO contractModifyApproveDetail(String instanceCode) {
        ContractSnapshotEntity snapshot = contractSnapshotService.lambdaQuery()
                .eq(ContractSnapshotEntity::getInstanceCode, instanceCode)
                .ne(ContractSnapshotEntity::getInvolvedId, VenueConstants.ZERO)
                .orderByDesc(ContractSnapshotEntity::getId)
                .last("limit 1 ")
                .one();
        if (Objects.isNull(snapshot)) {
            log.error("instanceCode未找到审批快照:{}", instanceCode);
            throw new CommonException("instanceCode未找到审批快照");
        }
        log.info("instanceCode:{},找到审批快照:{}", instanceCode, JSON.toJSONString(snapshot));
        ContractDetailVO contractDetailVO = convertSnapshotDetail(snapshot.getId());

        // 查找前一个快照
        ContractSnapshotEntity preSnapshot = contractSnapshotService.lambdaQuery()
                .eq(ContractSnapshotEntity::getInstanceCode, instanceCode)
                .eq(ContractSnapshotEntity::getInvolvedId, VenueConstants.ZERO)
                .orderByDesc(ContractSnapshotEntity::getId)
                .last("limit 1 ")
                .one();
        log.info("instanceCode:{},前一个快照:{}", instanceCode, JSON.toJSONString(preSnapshot));
        return contractReadService.contractDetailDiffHandle(Objects.isNull(preSnapshot) ? snapshot : preSnapshot, contractDetailVO);
    }

    @Data
    @Accessors(chain = true)
    public static class ContractSnapshotDataItemDTO<D> {
        private String clazz;
        private D data;
    }

    /**
     * 保存合同快照
     *
     * @param param 快照数据
     */
    public boolean saveSnapshot(ContractSnapshotParam param) {
        try {
            // 查询合同对象
            ContractEntity contract = contractService.getById(param.getContractId());
            if (Objects.isNull(contract)) {
                log.warn("合同[id={}]不存在", param.getContractId());
                return false;
            }
            // 查询该合同关联的所有数据
            List<String> relationData = getContractRelationData(contract);
            // log.info("=============================>>>> 查询合同[id={}]关联的所有数据:{}", param.getContractId(), relationData);

            // 保存合同关联的所有数据
            ContractSnapshotEntity snapshot = new ContractSnapshotEntity();
            snapshot.setSourceType(param.getSourceType());
            snapshot.setContractId(param.getContractId());
            snapshot.setApplyCode(contract.getApplyCode());
            snapshot.setContractCode(contract.getContractCode());
            snapshot.setBusinessType(contract.getBusinessType());
            snapshot.setCityId(contract.getCityId());
            snapshot.setStartDate(contract.getStartDate());
            snapshot.setEndDate(contract.getEndDate());
            snapshot.setFollower(contract.getFollower());
            snapshot.setApplyStatus(contract.getApplyStatus());
            snapshot.setFormalStatus(contract.getFormalStatus());
            snapshot.setContractType(contract.getContractType());
            snapshot.setSnapshotData(relationData.toString());
            snapshot.setCreator(Objects.isNull(param.getUserId()) ? UserThreadLocal.getUserId() : param.getUserId());
            snapshot.setInvolvedId(param.getInvolvedId());
            snapshot.setSnapshotVo(dataToString(contractReadService.getContractDetail(param.getContractId(), true), "合同详情VO"));
            snapshot.setInstanceCode(param.getInstanceCode());

            log.info("=============================>>>> 保存合同快照数据:{}", snapshot);

            return contractSnapshotService.save(snapshot);
        } catch (Exception e) {
            log.error("保存合同快照数据失败", e);
            return false;
        }
    }

    /**
     * 查询该合同关联的所有数据
     */
    private List<String> getContractRelationData(ContractEntity contract) {
        return Stream.of(
                // 查询合同对象
                dataToString(contract, "合同"),
                // 查询合同项目对象
                dataToString(contractProjectService.lambdaQuery().eq(ContractProjectEntity::getContractId, contract.getId()).list(),
                        "合同项目"),
                // 查询合同项目押金供应商对象
                dataToString(contractDepositSupplierService.lambdaQuery().eq(ContractDepositSupplierEntity::getContractId, contract.getId()).list(),
                        "合同项目押金供应商"),
                // 查询子合同对象
                dataToString(contractSubService.lambdaQuery().eq(ContractSubEntity::getContractId, contract.getId()).list(),
                        "子合同"),
                // 查询合同价格申请对象
                dataToString(contractPriceApplyService.lambdaQuery().eq(ContractPriceApplyEntity::getContractId, contract.getId()).list(),
                        "合同价格申请"),
                // 查询合同设备对象
                dataToString(contractDeviceService.lambdaQuery().eq(ContractDeviceEntity::getContractId, contract.getId()).list(),
                        "合同设备"),
                // 查询合同设备点对象
                dataToString(contractDevicePointService.lambdaQuery().eq(ContractDevicePointEntity::getContractId, contract.getId()).list(),
                        "合同设备点"),
                // 查询合同价格周期对象
                dataToString(contractPricePeriodService.lambdaQuery().eq(ContractPricePeriodEntity::getContractId, contract.getId()).list(),
                        "合同价格周期"),
                // 查询合同付款周期对象
                dataToString(contractPaymentPeriodService.lambdaQuery().eq(ContractPaymentPeriodEntity::getContractId, contract.getId()).list(),
                        "合同付款周期"),
                // 查询合同供应商对象
                dataToString(contractSupplierService.lambdaQuery().eq(ContractSupplierEntity::getContractId, contract.getId()).list(),
                        "合同供应商"),
                // 查询合同供应商银行对象
                dataToString(contractSupplierBankService.lambdaQuery().eq(ContractSupplierBankEntity::getContractId, contract.getId()).list(),
                        "合同供应商银行"),
                // 查询合同附件对象
                dataToString(contractAttachmentService.lambdaQuery().eq(ContractAttachmentEntity::getContractId, contract.getId()).list(),
                        "合同附件"),
                // 查询合同台账对象
                dataToString(ledgerService.lambdaQuery().eq(LedgerEntity::getContractId, contract.getId()).list(),
                        "合同台账")
        ).filter(StringUtils::isNotBlank).toList();
    }

    /**
     * 格式化数据
     */
    private <D> String dataToString(D data, String flag) {
        if (data instanceof List && CollectionUtils.isEmpty((List<?>) data)) {
            return StringUtils.EMPTY;
        }
        if (Objects.isNull(data)) {
            return StringUtils.EMPTY;
        }
        //
        try {
            log.info("=============================>>>> 打印数据啊：{} -> {}", flag, data);
            return OBJECT_MAPPER.writeValueAsString(new ContractSnapshotDataItemDTO<D>()
                    .setClazz((data instanceof List ? ((List<?>) data).get(0) : data).getClass().getName())
                    .setData(data));
        } catch (JsonProcessingException e) {
            log.error("{}数据格式化失败！", flag);
            throw new RuntimeException(e);
        }
    }

    /**
     * 合同快照数据解析
     *
     * @param id 快照ID
     * @return 合同详情VO
     */
    public ContractDetailVO convertSnapshotDetail(Integer id) {
        ContractSnapshotEntity snapshot = contractSnapshotService.lambdaQuery().eq(ContractSnapshotEntity::getId, id).one();
        if (Objects.isNull(snapshot) || StringUtils.isBlank(snapshot.getSnapshotVo())) {
            return null;
        }
        // 合同快照数据解析
        try {
            ContractDetailVO vo = OBJECT_MAPPER.readValue(snapshot.getSnapshotVo(),
                    new TypeReference<ContractSnapshotDataItemDTO<ContractDetailVO>>() {
                    }).getData();
            // 检查是否需要补全缺少的相关信息
            checkAndFillLackedInfo(vo);
            return vo;
        } catch (JsonProcessingException e) {
            log.error("合同快照[{}]数据解析失败！", id);
            throw new CommonException("合同快照数据解析失败！");
        }
    }

    /**
     * 检查并填充楼宇信息
     */
    private void checkAndFillLackedInfo(ContractDetailVO vo) {
        if (CollectionUtils.isEmpty(vo.getProjects())) {
            return;
        }
        // 发现有缺失的楼宇编码信息时，补充数据
        if (vo.getProjects().stream().anyMatch(e -> StringUtils.isBlank(e.getBuildingNo()))) {
            contractReadService.fillingBuildingRating(null, vo::getProjects);
        }
        // 发现缺少楼宇评级版本信息时，补充数据
        vo.getProjects().stream()
                .filter(e -> StringUtils.isBlank(e.getRatingVersion()))
                .forEach(e -> e.setRatingVersion("0"));
        // 发现缺少签约信息时，补充数据
        if (StringUtils.isBlank(vo.getSignType())) {
            vo.setSignType(ContractSignTypeEnum.NEW_SIGN.getCode());
            vo.setSignTypeName(ContractSignTypeEnum.NEW_SIGN.getDesc());
        }
    }
}
