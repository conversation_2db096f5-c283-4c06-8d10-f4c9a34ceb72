package com.coocaa.ad.cheese.cms.venue.kafka.consumer;

import cn.hutool.json.JSONUtil;
import com.coocaa.ad.cheese.cms.venue.kafka.constant.KafkaConstants;
import com.coocaa.ad.cheese.cms.venue.kafka.handle.ApprovalStatusHandler;
import com.coocaa.ad.cheese.cms.venue.kafka.vo.ApprovalStatusChangeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 审批状态变更监听器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-04
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ApprovalStatusChangedListener {

    private final Map<String, ApprovalStatusHandler> statusHandlers;

    /**
     * 审批状态变更，修改业务数据状态
     */
    @KafkaListener(topics = KafkaConstants.TOPIC_CHEESE_APPROVE_CONTRACT_CHANGE, groupId = KafkaConstants.CHEESE_APPROVE_CONTRACT_CHANGE_GROUP_ID, batch = "true")
    public void processApprovalStatusChange(String message) {
        log.info("Topic:{}, 消费消息:{}", KafkaConstants.TOPIC_CHEESE_APPROVE_CONTRACT_CHANGE, message);
        if (StringUtils.isBlank(message)) {
            log.warn("消息为空，忽略处理");
            return;
        }
        try {
            try {
                // 解析消息内容
                ApprovalStatusChangeVO changeVO = JSONUtil.toBean(message, ApprovalStatusChangeVO.class);
                // 校验必要字段
                if (Objects.isNull(changeVO) || StringUtils.isAnyBlank(
                        changeVO.getType(),
                        changeVO.getApprovalCode(),
                        changeVO.getProcessCode(),
                        changeVO.getStatus())) {
                    log.warn("审批状态变更信息不完整, 忽略处理");
                    return;
                }
                if(!Objects.equals("approval_instance", changeVO.getType())){
                    log.info("审批状态变更信息type不正确, 忽略处理");
                    return;
                }
                ApprovalStatusHandler handler = statusHandlers.get(changeVO.getStatus());
                if (Objects.isNull(handler)) {
                    log.warn("未找到状态[{}]对应的处理器", changeVO.getStatus());
                    return;
                }
                handler.handle(changeVO);
            } catch (Exception e) {
                // 记录异常但不抛出，避免消息重试
                log.error("处理审批状态变更失败，消息内容: {},e:{}", message, e);
            }
        } catch (Exception e) {
            log.error("批量处理审批状态变更失败", e);
        }
    }
}