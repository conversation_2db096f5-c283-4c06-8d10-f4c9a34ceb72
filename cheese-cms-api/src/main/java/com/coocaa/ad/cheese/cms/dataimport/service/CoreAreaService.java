package com.coocaa.ad.cheese.cms.dataimport.service;

import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethWebRpc;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.result.ResultTemplate;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/21
 */
@Service
@Slf4j
public class CoreAreaService {
    @Autowired
    private FeignMethWebRpc feignMethWebRpc;

    @Autowired
    private IContractProjectService iContractProjectService;

    @Autowired
    private IContractService iContractService;

    private static final String CITY_CORE_AREA = "城市核心区";
    private static final String NO_CITY_CORE_AREA = "城市非核心区";
    private static final int MAX_BATCH_SIZE = 500;


    /**
     * 初始化项目的 核心区域信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void initCoreArea() {
        List<Integer> contractIds = iContractService.lambdaQuery()
                .select(ContractEntity::getId)
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list()
                .stream()
                .map(ContractEntity::getId)
                .toList();
        if (CollectionUtils.isEmpty(contractIds)) {
            return;
        }
        Map<String, List<String>> map = new HashMap<>();
        List<ContractProjectEntity> contractProjectEntities = iContractProjectService.lambdaQuery()
                .select(ContractProjectEntity::getProjectCode)
                .in(ContractProjectEntity::getContractId, contractIds)
                .eq(ContractProjectEntity::getLocationName, StringUtils.EMPTY)
                .list()
                .stream()
                .filter(contractProjectEntity -> StringUtils.isNotBlank(contractProjectEntity.getProjectCode()))
                .toList();
        for (ContractProjectEntity contractProjectEntity : contractProjectEntities) {
            String builingNo = contractProjectEntity.getProjectCode().split("-")[0];
            String projectCode = contractProjectEntity.getProjectCode();
            if (map.containsKey(builingNo)) {
                List<String> projectCodes = map.get(builingNo);
                projectCodes.add(projectCode);
                map.put(builingNo, projectCodes);
            } else {
                List<String> projectCodes = new ArrayList<>();
                projectCodes.add(projectCode);
                map.put(builingNo, projectCodes);
            }
        }
        log.info("查询出核心区域为空的项目:{},{}", contractProjectEntities.size(), JSON.toJSONString(map.values()));
        List<String> buildingNoList = map.keySet().stream().toList();
        List<List<String>> partitions = Lists.partition(buildingNoList, MAX_BATCH_SIZE);
        List<String> coreAreas = new ArrayList<>();
        for (List<String> partition : partitions) {
            ResultTemplate<List<String>> filterCoreArea = feignMethWebRpc.filterCoreArea(partition);
            if (filterCoreArea.getSuccess() && CollectionUtils.isNotEmpty(filterCoreArea.getData())) {
                log.info("楼宇返回:{}", JSON.toJSONString(filterCoreArea));
                coreAreas.addAll(filterCoreArea.getData());
            }
        }

        log.info("所有的核心区域buildingNo:{},{}", coreAreas.size(), JSON.toJSONString(coreAreas));

        List<String> coreAreaProjectCodes = new ArrayList<>();
        List<String> noCoreAreaProjectCodes = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            if (coreAreas.contains(entry.getKey())) {
                coreAreaProjectCodes.addAll(entry.getValue());
            } else {
                noCoreAreaProjectCodes.addAll(entry.getValue());
            }
        }
        log.info("所有的核心区域projectCode:{},{}", coreAreaProjectCodes.size(), JSON.toJSONString(coreAreaProjectCodes));
        log.info("所有的非核心区域projectCode:{},{}", noCoreAreaProjectCodes.size(), JSON.toJSONString(noCoreAreaProjectCodes));

        // 处理核心区域
        List<List<String>> coreAreaPartitions = Lists.partition(coreAreaProjectCodes, MAX_BATCH_SIZE);
        for (List<String> coreAreaPartition : coreAreaPartitions) {
            log.info("更新核心区域:{}", JSON.toJSONString(coreAreaPartition));
            iContractProjectService.lambdaUpdate()
                    .set(ContractProjectEntity::getLocationName, CITY_CORE_AREA)
                    .in(ContractProjectEntity::getProjectCode, coreAreaPartition)
                    .update();
        }

        // 处理非核心区域
        List<List<String>> noCoreAreaPartitions = Lists.partition(noCoreAreaProjectCodes, MAX_BATCH_SIZE);
        for (List<String> noCoreAreaPartition : noCoreAreaPartitions) {
            log.info("更新非核心区域:{}", JSON.toJSONString(noCoreAreaPartition));
            iContractProjectService.lambdaUpdate()
                    .set(ContractProjectEntity::getLocationName, NO_CITY_CORE_AREA)
                    .in(ContractProjectEntity::getProjectCode, noCoreAreaPartition)
                    .update();
        }
    }
}
