package com.coocaa.ad.cheese.cms.dataimport.controller.clean;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSnapshotEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSnapshotService;
import com.coocaa.ad.cheese.cms.dataimport.controller.clean.vo.TempContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.service.ContractSnapshotService;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.result.ResultTemplate;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

/**
 * 清洗快照中的历史数据
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-21 10:47
 */
@Slf4j
@RestController
@RequestMapping("/clean/snapshot")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CleanSnapshotDepositData {
    private final IContractSnapshotService snapshotService;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            // 注册模块
            .registerModule(new JavaTimeModule())
            // 禁用时间戳
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            // 设置时间格式
            .setDateFormat(new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN));

    // 注入自身对象
    @Autowired
    private CleanSnapshotDepositData selfBean;

    @Value("${data-import.clean.snapshot.batch-size:50}")
    private Integer batchSize;

    @Operation(summary = "清理快照数据")
    @PostMapping
    public ResultTemplate<String> cleanSnapshotData(@RequestBody(required = false) Collection<Integer> snapshotIds) {
        Set<Integer> errorIds = clean(snapshotIds);
        return CollectionUtils.isEmpty(errorIds)
                ? ResultTemplate.success("数据全部更新完成")
                : ResultTemplate.success("快照更新失败ID：" + errorIds);
    }

    private Set<Integer> clean(Collection<Integer> ids) {
        // 查询所有的快照ID
        List<Integer> snapshotIds = snapshotService.lambdaQuery()
                .select(ContractSnapshotEntity::getId)
                .in(CollectionUtils.isNotEmpty(ids), ContractSnapshotEntity::getId, ids)
                .eq(ContractSnapshotEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .list().stream().map(ContractSnapshotEntity::getId).toList();

        // 分批处理
        int offset = 0;
        Set<Integer> errorIds = new HashSet<>();
        for (int i = 0; i < snapshotIds.size(); i += batchSize) {
            // 打印当前出处理进度
            List<Integer> batchIds = snapshotIds.subList(i, Math.min(i + batchSize, snapshotIds.size()));
            batchCleaning(batchIds, offset, snapshotIds.size(), errorIds);
            offset += batchIds.size();
        }

        log.warn("处理完成，错误ID：{}", errorIds);
        return errorIds;
    }

    /**
     * 处理某一批的数据
     */
    private void batchCleaning(List<Integer> snapshotIds, int offset, int total, Set<Integer> errorIds) {
        // 数据库查询vo数据
        List<ContractSnapshotEntity> entities = snapshotService.lambdaQuery()
                .select(ContractSnapshotEntity::getId, ContractSnapshotEntity::getSnapshotVo)
                .in(ContractSnapshotEntity::getId, snapshotIds)
                .list();
        for (int i = 0; i < entities.size(); i++) {
            var e = entities.get(i);
            log.info("当前处理进度：{}/{}", offset + i + 1, total);
            // 修正VO数据
            Boolean b = selfBean.needToFixVoData(e.getSnapshotVo(), e::setSnapshotVo);
            if (Objects.isNull(b)) {
                log.error("!!!!!!!!!! ==>> 快照[{}]数据解析失败！", e.getId());
                errorIds.add(e.getId());
                continue;
            }
            if (!b) {
                log.info("==============>> 快照[{}]数据不需要更新", e.getId());
                continue;
            }
            // 更新数据
            boolean ret = snapshotService.updateById(e);
            log.info("+++++>> 快照[{}]数据更新{}！", e.getId(), ret ? "成功" : "失败");
            if (!ret) {
                log.error("!!!!!!!!!! ==>> 快照[{}]数据更新失败！", e.getId());
                errorIds.add(e.getId());
            }
        }

    }

    /**
     * 修正VO数据
     */
    @Transactional(rollbackFor = Exception.class)
    protected Boolean needToFixVoData(String snapshotVo, Consumer<String> voConsumer) {
        if (StringUtils.isBlank(snapshotVo)) {
            return false;
        }
        // vo转对象
        try {
            TempContractDetailVO detailVO = OBJECT_MAPPER.readValue(snapshotVo,
                    new TypeReference<ContractSnapshotService.ContractSnapshotDataItemDTO<TempContractDetailVO>>() {
                    }).getData();

            if (Objects.isNull(detailVO)) {
                return false;
            }

            // 处理意向金数据
            Optional.ofNullable(detailVO.getIntentionalDeposit()).ifPresent(
                    intentionalDeposit -> detailVO.setTotalAmount(detailVO.getTotalAmount().subtract(intentionalDeposit)));

            // 处理押金数据
            boolean b = needToFixDepositData(detailVO);
            if (!b) {
                return false;
            }

            // 对象转vo
            String s = OBJECT_MAPPER.writeValueAsString(
                    new ContractSnapshotService.ContractSnapshotDataItemDTO<ContractDetailVO>()
                            .setClazz(ContractDetailVO.class.getName())
                            .setData(BeanUtil.toBean(detailVO, ContractDetailVO.class)));
            voConsumer.accept(s);
            return true;
        } catch (JsonProcessingException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    // 处理押金数据
    private boolean needToFixDepositData(TempContractDetailVO detailVO) {
        if (Objects.nonNull(detailVO.getDepositAmount()) && detailVO.getDepositAmount().compareTo(BigDecimal.ZERO) > 0) {
            log.info("快照数据[{}]不需要处理押金数据", detailVO.getId());
            return false;
        }
        if (CollectionUtils.isEmpty(detailVO.getProjects())) {
            log.info("快照数据[{}]：合同下没有项目信息！", detailVO.getId());
            return false;
        }
        // 汇总项目上的数据到合同
        AtomicBoolean isFirstHolder = new AtomicBoolean(false);
        AtomicReference<BigDecimal> sumDepositAmountRef = new AtomicReference<>(BigDecimal.ZERO);
        detailVO.getProjects().forEach(project -> {
            if (Objects.isNull(project.getDepositFlag())
                    || Objects.equals(project.getDepositFlag(), BooleFlagEnum.NO.getCode())) {
                return;
            }
            if (!isFirstHolder.get()) {
                Optional.ofNullable(project.getDepositId()).ifPresent(detailVO::setDepositId);
                detailVO.setDepositFlag(project.getDepositFlag());
                detailVO.setDepositSupplierId(project.getDepositSupplierId());
                detailVO.setDepositSupplierName(project.getDepositSupplierName());
                detailVO.setDepositSupplierAccountNo(project.getDepositSupplierAccountNo());
                detailVO.setDepositSupplierBankId(project.getDepositSupplierBankId());
                detailVO.setDepositPaymentDate(project.getDepositPaymentDate());
                isFirstHolder.set(true);
            }
            // 合计押金数据
            Optional.ofNullable(project.getDepositAmount()).ifPresent(val -> {
                sumDepositAmountRef.set(sumDepositAmountRef.get().add(val));
            });
        });
        // 合计押金数据
        if (!isFirstHolder.get() || sumDepositAmountRef.get().compareTo(BigDecimal.ZERO) == 0) {
            log.info("快照数据[{}]不需要处理押金数据", detailVO.getId());
            return false;
        }
        detailVO.setDepositAmount(sumDepositAmountRef.get());

        return isFirstHolder.get();
    }
}
