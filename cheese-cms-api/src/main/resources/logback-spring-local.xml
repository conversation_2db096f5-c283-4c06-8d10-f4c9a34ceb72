<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%clr([%level]) [%d{yyyy-MM-dd HH:mm:ss.SSS}] %clr([${PID:-}]){faint} %clr([%thread]){magenta} %clr([%-40.40logger{80}:%line]){cyan} %msg%n</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>