package com.coocaa.ad.cheese.cms.common.tools.common.util.operatelog;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 扩展ChangedItem
 * @since 2025-02-20 11:42
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ExpandChangedItem extends ChangedItem {
    /**
     * 隐藏
     */
    private Boolean hide = false;

    public ExpandChangedItem(String fieldName, String beforeChanged, String afterChanged, boolean hide) {
        this.hide = hide;
        this.setFieldName(fieldName);
        this.setBeforeChanged(beforeChanged);
        this.setAfterChanged(afterChanged);
    }
}
