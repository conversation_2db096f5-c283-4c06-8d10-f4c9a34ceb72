package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-01-06
 */
@Getter
@AllArgsConstructor
public enum DictEnum implements IEnumType<String> {
    PAYMENT_TYPE("0027", "付款方式");

    private final String code;
    private final String desc;

    private static final Map<Integer, CooperateTypeEnum> BY_CODE_MAP =
            Arrays.stream(CooperateTypeEnum.values())
                    .collect(Collectors.toMap(CooperateTypeEnum::getCode, Function.identity()));


    /**
     * 将代码转成枚举
     */
    public static CooperateTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static CooperateTypeEnum parse(String code, CooperateTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(CooperateTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}
