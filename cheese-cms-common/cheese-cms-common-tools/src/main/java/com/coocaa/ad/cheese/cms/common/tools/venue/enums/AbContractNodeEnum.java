package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 异常合同审批节点(字典0106)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum AbContractNodeEnum implements IEnumType<String> {

    SUPERVISOR("supervisor", "直接上级"),
    REGION("region", "大区总"),
    TREASURER("treasurer", "财务BP"),
    LEGAL("legal", "法务BP"),
    MEDIA("media", "媒资负责人");

    private final String code;
    private final String desc;

    private static final Map<String, AbContractNodeEnum> BY_CODE_MAP =
            Arrays.stream(AbContractNodeEnum.values())
                    .collect(Collectors.toMap(AbContractNodeEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static AbContractNodeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static AbContractNodeEnum parse(String code, AbContractNodeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(AbContractNodeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 