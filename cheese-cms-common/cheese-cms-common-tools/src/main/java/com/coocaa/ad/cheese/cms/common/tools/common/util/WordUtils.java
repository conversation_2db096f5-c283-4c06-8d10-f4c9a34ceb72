package com.coocaa.ad.cheese.cms.common.tools.common.util;

import com.coocaa.ad.cheese.cms.common.tools.common.cos.ObjectUtils;
import org.apache.poi.xwpf.usermodel.*;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.UUID;

public class WordUtils {

    /**
     * 生成Word文档并上传到COS
     *
     * @param title    文档标题
     * @param content  文档内容
     * @param filename 文件名
     * @param feature  特征标识
     * @return 文件访问URL
     */
    public static String createWordToCos(String title, String content, String filename, String feature) throws Exception {
        // 生成word文档字节数组
        byte[] wordBytes = createWord(title, content);

        // 将字节数组转换为临时文件
        String tempFileName = UUID.randomUUID().toString();
        File tempFile = new File(System.getProperty("java.io.tmpdir"), tempFileName);
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(wordBytes);
        }

        // 上传到COS
        ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, filename), tempFile);

        // 返回文件访问URL
        return ObjectUtils.getAccessUrl(feature, filename);
    }

    /**
     * 生成Word文档
     *
     * @param title   文档标题
     * @param content 文档内容
     * @return 文档字节数组
     */
    public static byte[] createWord(String title, String content) throws Exception {
        try (XWPFDocument document = new XWPFDocument()) {
            // 设置标题样式
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);

            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText(title);
            titleRun.setBold(true);
            titleRun.setFontSize(16);
            titleRun.setFontFamily("宋体");

            // 添加空行
            document.createParagraph();

            // 设置正文样式
            XWPFParagraph contentParagraph = document.createParagraph();
            contentParagraph.setAlignment(ParagraphAlignment.LEFT);

            XWPFRun contentRun = contentParagraph.createRun();
            contentRun.setText(content);
            contentRun.setFontSize(12);
            contentRun.setFontFamily("宋体");

            // 转换为字节数组
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                document.write(baos);
                return baos.toByteArray();
            }
        }
    }
}