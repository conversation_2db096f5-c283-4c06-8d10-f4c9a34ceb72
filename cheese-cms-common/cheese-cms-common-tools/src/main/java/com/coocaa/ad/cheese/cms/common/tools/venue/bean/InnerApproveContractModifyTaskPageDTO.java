package com.coocaa.ad.cheese.cms.common.tools.venue.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/4
 */
@Data
public class InnerApproveContractModifyTaskPageDTO {
    /**
     * 合同修改申请id
     */
    private Integer id;

    /**
     * 合同修改申请编号
     */
    private String applyCode;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 大区
     */
    private Integer regionId;

    /**
     * 城市
     */
    private Integer cityId;

    /**
     * 申请状态
     */
    private String applyStatus;

    /**
     * 供应商
     */
    private String supplierId;

    /**
     * 合同金额
     */
    private BigDecimal amount;

    /**
     * 合同年限
     */
    private BigDecimal period;

    /**
     * 合同跟进人
     */
    private Integer follower;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 当前审批节点
     */
    private String approveNodeName;

    /**
     * 当前审批人
     */
    private String approverName;
}
