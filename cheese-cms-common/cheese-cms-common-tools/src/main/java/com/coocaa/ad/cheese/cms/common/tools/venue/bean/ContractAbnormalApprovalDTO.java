package com.coocaa.ad.cheese.cms.common.tools.venue.bean;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 异常合同分页查询
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
public class ContractAbnormalApprovalDTO implements Serializable {

    /**
     * 异常合同id
     */
    private Integer abContractId;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 异常合同编号
     */
    private String abContractCode;

    /**
     * 原合同id
     */
    private Integer contractId;

    /**
     * 原合同编号
     */
    private String code;

    /**
     * 原合同类型
     */
    private Integer contractType;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 状态(字典0102)
     */
    private String applyStatus;

    /**
     * 供应商
     */
    private String agentName;

    /**
     * 合同金额(元)
     */
    private BigDecimal totalAmount;

    /**
     * 合同年限
     */
    private BigDecimal period;

    /**
     * 异常点位数
     */
    private Integer abnormalCount;

    /**
     * 申请人
     */
    private Integer creator;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 跟进人
     */
    private Integer follower;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 当前审批节点code
     */
    private String nodeCode;

    /**
     * 当前审批节点
     */
    private String nodeName;

    /**
     * 当前审批人
     */
    private Integer userId;

    /**
     * 异常类型
     */
    private String type;

    /**
     * 异常说明
     */
    private String abnormalRemark;

    /**
     * 异常原因
     */
    private String abnormalReason;

    /**
     * 是否是普通用户
     */
    Boolean ordinaryUser;
}
