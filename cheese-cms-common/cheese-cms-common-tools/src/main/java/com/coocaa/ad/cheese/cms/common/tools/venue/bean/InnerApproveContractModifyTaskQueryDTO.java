package com.coocaa.ad.cheese.cms.common.tools.venue.bean;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/4
 */
@Data
public class InnerApproveContractModifyTaskQueryDTO {
    /**
     * 合同编号
     */
    private String contractCode;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 城市Id
     */
    private Integer cityId;
    /**
     * 创建开始时间
     */
    private LocalDateTime startCreateTime;
    /**
     * 创建结束时间
     */
    private LocalDateTime endCreateTime;

    /**
     * 城市ID
     */
    private Collection<Integer> cityIds;

    /**
     * 用户ID
     */
    private Collection<Integer> userIds;

    /**
     * 代理商ID
     */
    private Collection<Integer> agentIds;

    /**
     * 合同类型 [1:默认, 2:变更, 3:飞书导入, 4:补充协议, 5:合同变更, 6:合同修改]
     */
    private List<Integer> contractTypes;

    /**
     * 代办关联的合同修改申请ids
     */
    private List<Integer> contractIds;

}
