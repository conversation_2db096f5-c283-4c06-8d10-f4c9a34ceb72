package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 飞书审批状态
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum ExamineApproveEnum implements IEnumType<String> {
    PENDING("PENDING", "审批中"),
    REJECTED("REJECTED", "拒绝"),
    APPROVED("APPROVED", "通过"),
    TRANSFERRED("TRANSFERRED", "转交"),
    DONE("DONE", "已完成"),
    PROCESSED("PROCESSED", "已处理");

    private final String code;
    private final String desc;

    private static final Map<String, ExamineApproveEnum> BY_CODE_MAP =
            Arrays.stream(ExamineApproveEnum.values())
                    .collect(Collectors.toMap(ExamineApproveEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static ExamineApproveEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static ExamineApproveEnum parse(String code, ExamineApproveEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(ExamineApproveEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 