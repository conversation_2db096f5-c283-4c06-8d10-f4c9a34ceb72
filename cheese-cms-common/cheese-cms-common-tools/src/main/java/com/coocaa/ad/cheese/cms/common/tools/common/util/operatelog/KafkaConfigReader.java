package com.coocaa.ad.cheese.cms.common.tools.common.util.operatelog;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 自定义Kafka配置文件读取
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-04
 */
@Slf4j
@Component
public class KafkaConfigReader implements InitializingBean {
    @Value("${spring.kafka.bootstrap-servers:}")
    private String servers;

    @Value("${cheese.operate.log.queue.topic:cheese-operate-log}")
    private String topicName;

    @Resource
    private Environment environment;
    private static final Map<String, String> DEFAULT_QUEUE_SERVER_MAPPING = new HashMap<String, String>() {
        {
            this.put("dev", "sy-oversea-pulbic-01-151-50:9092,sy-oversea-pulbic-02-151-51:9092,sy-oversea-pulbic-03-151-52:9092");
        }
    };


    @Override
    public void afterPropertiesSet() throws Exception {
        OperateLogUtils.setQueueConfig(this.generateQueueConfig(), this.topicName);
    }


    private Properties generateQueueConfig() {
        String queueServer = this.servers;
        if (StringUtils.isBlank(queueServer)) {
            String activeProfile = "";
            if (ArrayUtils.isNotEmpty(this.environment.getActiveProfiles())) {
                activeProfile = StringUtils.lowerCase(StringUtils.trimToEmpty(this.environment.getActiveProfiles()[0]));
            }
            queueServer = DEFAULT_QUEUE_SERVER_MAPPING.getOrDefault(activeProfile, "master");
        }

        Properties properties = new Properties();
        properties.put("bootstrap.servers", queueServer);
        properties.put("acks", "1");
        properties.put("retries", 1);
        properties.put("linger.ms", 0);
        properties.put("batch.size", 16384);
        properties.put("buffer.memory", 32768);
        properties.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        return properties;
    }
}
