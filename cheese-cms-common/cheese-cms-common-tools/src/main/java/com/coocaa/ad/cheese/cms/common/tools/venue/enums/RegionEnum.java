package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 异常合同大区
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum RegionEnum implements IEnumType<Integer> {
    CENTER_WEST(1, "创视-中西大区"),
    SOUTH(2, "创视-华南大区"),
    NORTH(3, "创视-华北大区"),
    EAST_ONE(4, "创视-华东一大区"),
    EAST_TWO(5, "创视-华东二大区");

    private final Integer code;
    private final String desc;

    private static final Map<Integer, RegionEnum> BY_CODE_MAP =
            Arrays.stream(RegionEnum.values())
                    .collect(Collectors.toMap(RegionEnum::getCode, item -> item));

    private static final Map<String, RegionEnum> DESC_MAP = new HashMap<>();

    static {
        for (RegionEnum code : values()) {
            DESC_MAP.put(code.desc, code);
        }
    }

    /**
     * 将代码转成枚举
     */
    public static RegionEnum parse(Integer code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static RegionEnum parse(Integer code, RegionEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(Integer code) {
        return Optional.ofNullable(parse(code)).map(RegionEnum::getDesc).orElse(StringUtils.EMPTY);
    }

    /**
     * 根据描述获取枚举
     */
    public static RegionEnum getByDesc(String desc) {
        return DESC_MAP.get(desc);
    }
} 