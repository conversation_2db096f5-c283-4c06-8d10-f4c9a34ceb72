package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 合同签约类型
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-08 15:27
 */
@Getter
@AllArgsConstructor
public enum ContractSignTypeEnum implements IEnumType<String> {
    NEW_SIGN("0174-1", "新签"),
    RENEW("0174-2", "续约");

    private final String code;
    private final String desc;

    private static final Map<String, ContractSignTypeEnum> BY_CODE_MAP =
            Arrays.stream(ContractSignTypeEnum.values())
                    .collect(Collectors.toMap(ContractSignTypeEnum::getCode, Function.identity()));

    /**
     * 将代码转成枚举
     */
    public static ContractSignTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static ContractSignTypeEnum parse(String code, ContractSignTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(ContractSignTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}
