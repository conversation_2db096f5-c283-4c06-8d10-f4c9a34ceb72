package com.coocaa.ad.cheese.cms.common.tools.common.util;

import com.coocaa.ad.common.exception.CommonException;
import com.coocaa.ad.common.result.ResultTemplate;

/**
 * @program: cheese-cms
 * @ClassName RpcUtils
 * @description:
 * @author: z<PERSON><PERSON><PERSON>n
 * @create: 2025-05-22 16:39
 * @Version 1.0
 **/
public class RpcUtils {
    public static <T> T unBox(ResultTemplate<T> resultTemplate) {
        String code = resultTemplate.getCode();
        if (!code.equals("1")) {
            throw new CommonException(resultTemplate.getMsg());
        }
        return resultTemplate.getData();
    }
}
