package com.coocaa.ad.cheese.cms.common.tools.venue.bean;

import lombok.Data;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/5
 */
@Data
public class InnerApproveTaskFinishQueryDTO {


    private String contractCode;

    private String projectName;

    private LocalDate startCreateDate;

    private LocalDate endCreateDate;

    private List<String> applyStatus;

    /**
     * 城市ID
     */
    private Collection<Integer> cityIds;

    /**
     * 用户ID
     */
    private Collection<Integer> userIds;
}
