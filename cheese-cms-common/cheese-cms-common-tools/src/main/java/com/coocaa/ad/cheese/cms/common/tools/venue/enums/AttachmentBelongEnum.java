package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 附件归属的主表[1:合同附件，2:异常申请附件]
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-25
 */
@Getter
@AllArgsConstructor
public enum AttachmentBelongEnum implements IEnumType<Integer> {

    CONTRACT(1, "合同附件"),
    ABNORMAL(2, "异常申请附件"),
    COMMENT(3, "评论附件"),
    ;


    private final Integer code;
    private final String desc;
}
