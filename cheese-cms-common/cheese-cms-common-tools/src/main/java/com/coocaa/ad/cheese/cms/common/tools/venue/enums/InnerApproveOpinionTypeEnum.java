package com.coocaa.ad.cheese.cms.common.tools.venue.enums;


import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 站内审批意见(字典0162)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-03
 */
@Getter
@AllArgsConstructor
public enum InnerApproveOpinionTypeEnum implements IEnumType<String> {

    AGREE("0162-1", "同意"),
    REJECT("0162-2", "拒绝");

    private final String code;
    private final String desc;

    private static final Map<String, InnerApproveOpinionTypeEnum> BY_CODE_MAP =
            Arrays.stream(InnerApproveOpinionTypeEnum.values())
                    .collect(Collectors.toMap(InnerApproveOpinionTypeEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static InnerApproveOpinionTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code         代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static InnerApproveOpinionTypeEnum parse(String code, InnerApproveOpinionTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(InnerApproveOpinionTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 