package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 附件类型
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10
 */
@Getter
@AllArgsConstructor
public enum AttachmentTypeEnum implements IEnumType<Integer> {
    CANCEL(4, "合同作废"),

    MAIN_SEAL_PENDING(11, "主合同-待签章合同原件"),
    MAIN_SEALED(12, "主合同-已签章合同原件"),
    MAIN_REFERENCE(13, "主合同-合同参考附件"),
    FEISHU_CONTRACT(14, "原始合同"),
    MAIN_DEPOSIT(15, "主合同-押金条"),
    MAIN_BY_TEMPLATE(18, "主合同-待签章合同原件"),
    MAIN_OTHER(19, "主合同-其它附件"),

    SUB_SEAL_PENDING(21, "子合同-待签章合同原件"),
    SUB_SEALED(22, "子合同-已签章合同原件"),
    SUB_REFERENCE(23, "子合同-合同参考附件"),
    SUB_OTHER(29, "子合同-其它附件"),

    ABNORMAL_MAIN(31, "异常合同-原合同文件"),
    ABNORMAL_SUPPORT(32, "异常合同-支持性附件"),

    COMMENT_ATT(41, "评论附件")

    ;

    private final Integer code;
    private final String desc;

    private static final Map<Integer, AttachmentTypeEnum> BY_CODE_MAP =
            Arrays.stream(AttachmentTypeEnum.values())
                    .collect(Collectors.toMap(AttachmentTypeEnum::getCode, Function.identity()));


    /**
     * 将代码转成枚举
     */
    public static AttachmentTypeEnum parse(Integer code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static AttachmentTypeEnum parse(Integer code, AttachmentTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(Integer code) {
        return Optional.ofNullable(parse(code)).map(AttachmentTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}
