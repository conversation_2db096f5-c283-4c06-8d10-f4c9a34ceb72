package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 业务类型枚举(1:异常合同)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum BusinessBelongEnum implements IEnumType<Integer> {
    ABNORMAL_CONTRACT(1, "异常合同");

    private final Integer code;
    private final String desc;

    private static final Map<Integer, BusinessBelongEnum> BY_CODE_MAP =
            Arrays.stream(BusinessBelongEnum.values())
                    .collect(Collectors.toMap(BusinessBelongEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static BusinessBelongEnum parse(Integer code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static BusinessBelongEnum parse(Integer code, BusinessBelongEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(Integer code) {
        return Optional.ofNullable(parse(code)).map(BusinessBelongEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 