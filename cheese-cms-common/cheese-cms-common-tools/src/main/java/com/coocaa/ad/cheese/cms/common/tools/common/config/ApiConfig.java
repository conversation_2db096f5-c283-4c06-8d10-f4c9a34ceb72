package com.coocaa.ad.cheese.cms.common.tools.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @className: ApiConfig
 * @description: NACOS API配置
 * @author: jx
 * @date: 2025/3/10 10:07
 */
@Configuration
public class ApiConfig extends BaseCosConfig {

    /**
     * 创视-中西大区
     */
    @Value("${ab.contract.feishu.centreWest:m8r2tocv-76nu3hroh4w-0}")
    public String abContractCentreWest;

    /**
     * 创视-华南大区
     */
    @Value("${ab.contract.feishu.south:m8r2tocv-z4fgsc8u4h-0}")
    public String abContractSouth;

    /**
     * 创视-华北大区
     */
    @Value("${ab.contract.feishu.north:m8r2tocv-ra9o1ddxsp-0}")
    public String abContractNorth;

    /**
     * 创视-华东一大区
     */
    @Value("${ab.contract.feishu.eastOne:m8r2todf-y2sz6bs0unj-1}")
    public String abContractEastOne;

    /**
     * 创视-华东二大区
     */
    @Value("${ab.contract.feishu.eastTwo:m8r2todf-qn7f1e85xud-3}")
    public String abContractEastTwo;

    /**
     * 后门程序审批人id(123,456)
     */
    @Value("${ab.contract.pending}")
    public String abContractPending;

    /**
     * 发送飞书消息通知pc端
     */
    @Value("${ab.contract.message.pcUrl:https://beta-cheese-ssp.coocaa.com}")
    public String abContractMessagePcUrl;

    /**
     * 发送飞书消息通知h5端
     */
    @Value("${ab.contract.message.h5Url:http://dev-meth-cshimedia.coocaa.com}")
    public String abContractMessageH5Url;
}
