package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 状态变更类型枚举(字典:0042)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum StatusChangeTypeEnum implements IEnumType<String> {
    ABNORMAL_CONTRACT("0042-6", "异常合同");

    private final String code;
    private final String desc;

    private static final Map<String, StatusChangeTypeEnum> BY_CODE_MAP =
            Arrays.stream(StatusChangeTypeEnum.values())
                    .collect(Collectors.toMap(StatusChangeTypeEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static StatusChangeTypeEnum parse(Integer code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static StatusChangeTypeEnum parse(Integer code, StatusChangeTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(Integer code) {
        return Optional.ofNullable(parse(code)).map(StatusChangeTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 