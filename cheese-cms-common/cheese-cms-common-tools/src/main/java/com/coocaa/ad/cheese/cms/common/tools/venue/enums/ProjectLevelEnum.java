package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/12
 */
@Getter
@AllArgsConstructor
public enum ProjectLevelEnum implements IEnumType<Integer> {
    AAA(4, "AAA"),
    AA(3, "AA"),
    A(2, "A"),
    NOT_A(1, "未达到A级"),
    OTHER(-999, "");

    private final Integer code;
    private final String desc;

    public static ProjectLevelEnum fromLevel(String level) {
        if (Objects.isNull(level)) {
            return OTHER;
        }
        return switch (level) {
            case "AAA" -> AAA;
            case "AA" -> AA;
            case "A" -> A;
            case "未达到A级" -> NOT_A;
            default -> OTHER;
        };
    }

    public static String max(String maxLevel, String level) {
        ProjectLevelEnum maxLevelEnum = fromLevel(maxLevel);
        ProjectLevelEnum levelEnum = fromLevel(level);
        return maxLevelEnum.getCode() > levelEnum.getCode() ?
                maxLevelEnum.getDesc() : levelEnum.getDesc();
    }
}
