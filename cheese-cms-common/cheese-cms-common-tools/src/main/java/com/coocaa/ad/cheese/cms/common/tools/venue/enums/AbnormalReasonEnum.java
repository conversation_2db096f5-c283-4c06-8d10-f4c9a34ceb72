package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 异常原因 (字典0101)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-25
 */
@Getter
@AllArgsConstructor
public enum AbnormalReasonEnum implements IEnumType<String> {

    OTHER_DEFAULT("0101-1", "对方单方违约"),
    BOTH_DEFAULT_OTHER("0101-2", "双方违约-对方责任较大"),
    OUR_DEFAULT_OTHER("0101-3", "双方违约-我方责任较大"),
    OUR_DEFAULT("0101-4", "我方单方违约"),
    ;

    private final String code;
    private final String desc;
}
