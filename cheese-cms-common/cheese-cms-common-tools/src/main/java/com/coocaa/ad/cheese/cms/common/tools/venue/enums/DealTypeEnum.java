package com.coocaa.ad.cheese.cms.common.tools.venue.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 处理方式 (字典0100)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-25
 */
@Getter
@AllArgsConstructor
public enum DealTypeEnum implements IEnumType<String> {

    BUSINESS("0100-1", "业务处理"),
    LEGAL("0100-2", "法务处理"),
    STOP("0100-3", "终止合同"),
    ;

    private final String code;
    private final String desc;
}
