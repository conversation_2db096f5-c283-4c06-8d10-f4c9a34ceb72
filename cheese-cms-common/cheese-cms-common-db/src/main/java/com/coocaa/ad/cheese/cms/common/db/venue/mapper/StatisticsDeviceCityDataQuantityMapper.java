package com.coocaa.ad.cheese.cms.common.db.venue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.bean.StatisticsDeviceCityWithImportRecordDTO;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.StatisticsDeviceCityDataQuantityEntity;
import java.time.LocalDate;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @file StatisticsDeviceCityDataQuantityMapper
 * @date 2025/1/15 15:35
 * @description This is a java file.
 */
@Mapper
public interface StatisticsDeviceCityDataQuantityMapper extends BaseMapper<StatisticsDeviceCityDataQuantityEntity> {
    /**
     * @param filterDate - 筛选日期
     * @param cityIdList - 城市列表
     * @Author：TanJie
     * @Date：2025-01-17 16:28
     * @Description：根据日期和城市列表获取设备统计数据
     */
    List<StatisticsDeviceCityWithImportRecordDTO> selectByStatisticsDateAndCityId(@Param("filterDate") LocalDate filterDate,
                                                                                  @Param("cityIdList") List<Integer> cityIdList);

    StatisticsDeviceCityWithImportRecordDTO selectTotalByStatisticsDateAndCityId(@Param("filterDate") LocalDate filterDate,
                                                                                       @Param("cityIdList") List<Integer> cityIdList);
}
