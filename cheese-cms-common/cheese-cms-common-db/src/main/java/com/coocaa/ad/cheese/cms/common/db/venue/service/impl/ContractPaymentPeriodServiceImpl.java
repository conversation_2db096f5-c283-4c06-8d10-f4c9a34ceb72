package com.coocaa.ad.cheese.cms.common.db.venue.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractPaymentPeriodMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPaymentPeriodPaidDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 合同-设备-付款周期 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Slf4j
@Primary
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractPaymentPeriodServiceImpl extends ServiceImpl<ContractPaymentPeriodMapper, ContractPaymentPeriodEntity> implements IContractPaymentPeriodService {

    @Override
    public List<ContractPaymentPeriodPaidDTO> selectContractPaymentPeriodPaidInfo(Integer contractId) {
        return getBaseMapper().selectContractPaymentPeriodPaidInfo(contractId);
    }
}
