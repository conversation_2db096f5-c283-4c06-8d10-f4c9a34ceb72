package com.coocaa.ad.cheese.cms.common.db.venue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAbnormalProjectEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 异常合同项目 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Mapper
public interface ContractAbnormalProjectMapper extends BaseMapper<ContractAbnormalProjectEntity> {

    /*
     * <AUTHOR>
     * @Description 更新异常合同项目数据
     * @Date 2025/3/31
     * @Param [id, contractAbnormalProjectEntities]
     * @return boolean
     **/
    boolean updateProject(@Param("id") Integer id, @Param("list") List<ContractAbnormalProjectEntity> contractAbnormalProjectEntities);
}
