package com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.supplier.SupplierBankMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.supplier.SupplierMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Slf4j
@Primary
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SupplierBankServiceImpl extends ServiceImpl<SupplierBankMapper, SupplierBankEntity> implements ISupplierBankService {

}


