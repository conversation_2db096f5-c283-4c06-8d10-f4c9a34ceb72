package com.coocaa.ad.cheese.cms.common.db.venue.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAmendmentChangeTypeEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractAmendmentChangeTypeMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAmendmentChangeTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/13
 */
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Service
@Primary
public class ContractAmendmentChangeTypeServiceImpl extends ServiceImpl<ContractAmendmentChangeTypeMapper, ContractAmendmentChangeTypeEntity>
        implements IContractAmendmentChangeTypeService {

}
