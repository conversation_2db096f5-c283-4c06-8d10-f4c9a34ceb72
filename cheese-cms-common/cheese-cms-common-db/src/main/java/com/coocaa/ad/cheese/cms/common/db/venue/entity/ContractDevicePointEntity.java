package com.coocaa.ad.cheese.cms.common.db.venue.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 合同-项目-设备-点位
 *
 * <AUTHOR>
 * @since 2024-12-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("venue_contract_device_point")
public class ContractDevicePointEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 合同ID
     */
    private Integer contractId;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 设备ID
     */
    private Integer deviceId;

    /**
     * 点位编码 (来自楼宇评级)
     */
    private String code;

    /**
     * 点位名称
     */
    private String name;

    /**
     * 设备尺寸(字典0013); 楼宇评级转字典
     */
    private String size;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
