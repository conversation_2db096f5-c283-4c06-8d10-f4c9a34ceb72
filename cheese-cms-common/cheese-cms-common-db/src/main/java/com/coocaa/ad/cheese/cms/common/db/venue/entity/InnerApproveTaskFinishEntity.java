package com.coocaa.ad.cheese.cms.common.db.venue.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/5
 */
@Data
@NoArgsConstructor
@TableName("venue_inner_approval_task_finish")
public class InnerApproveTaskFinishEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务ID
     */
    private Integer bizId;

    /**
     * 业务类型(字典0160)
     */
    private String type;

    /**
     * 流程实例code
     */
    private String instanceCode;

    /**
     * 审批任务ID
     */
    private Integer taskId;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 大区ID
     */
    private Integer regionId;

    /**
     * 字典0025
     */
    private String applyStatus;

    /**
     * 合同修改申请单号
     */
    private String applyCode;

    /**
     * 供应商名称，逗号分隔
     */
    private String supplierName;

    /**
     * 合同金额（元）
     */
    private BigDecimal totalAmount;

    /**
     * 合同年限
     */
    private BigDecimal period;

    /**
     * 跟进人
     */
    private Integer follower;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
