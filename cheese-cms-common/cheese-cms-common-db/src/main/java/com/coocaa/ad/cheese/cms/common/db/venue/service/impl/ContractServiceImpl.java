package com.coocaa.ad.cheese.cms.common.db.venue.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.BusinessOpportunityStatusVO;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractPageDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractQueryDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPageWithoutPayPeriodDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 场地合同 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Slf4j
@Primary
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractServiceImpl extends ServiceImpl<ContractMapper, ContractEntity> implements IContractService {

    private final ContractMapper contractMapper;

    @Override
    public IPage<ContractPageDTO> pageListContracts(IPage<ContractPageDTO> page, ContractQueryDTO condition) {
        return getBaseMapper().pageListContracts(page, condition);
    }

    @Override
    public List<Integer> listContractsIds(ContractQueryDTO condition) {
        return getBaseMapper().listContractsIds(condition);
    }

    @Override
    public List<ContractPageWithoutPayPeriodDTO> listContactsWithoutPayPeriodByContractIds(List<Integer> contractIds) {
        return getBaseMapper().listContactsWithoutPayPeriodByContractIds(contractIds);
    }

    @Override
    public Integer queryApprovalDetail(Integer id) {
        return getBaseMapper().queryApprovalDetail(id);
    }

    @Override
    public PageResponseVO<CodeNameVO> listFiled(String tableName, String query, String filed, Integer pageSize, Integer pageNumber) {
        IPage<CodeNameVO> page = null;
        try {
            page = getBaseMapper().listFiled(Page.of(pageNumber, pageSize), tableName, query, filed);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return new PageResponseVO<>();
        }
        return new PageResponseVO<>(page.getCurrent(), page.getSize(), page.getTotal(),
                page.getPages(), page.getRecords(), page.getTotal());
    }

    @Override
    public List<BusinessOpportunityStatusVO> queryBusinessOpportunityStatus(List<String> buildingCodes) {

        return contractMapper.queryBusinessOpportunityStatus(buildingCodes);
    }

    @Override
    public List<ContractEntity> findContractByContractCode(String contractCode) {
        return this.lambdaQuery().eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode()).eq(ContractEntity::getContractCode, contractCode).list();
    }
}
