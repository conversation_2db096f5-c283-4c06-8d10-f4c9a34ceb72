package com.coocaa.ad.cheese.cms.common.db.venue.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Set;

/**
 * 异常合同查询条件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Data
@Accessors(chain = true)
public class AbContractQueryDTO {

    /**
     * 合同编号
     */
    private String code;

    /**
     * 异常申请编号
     */
    private String abContractCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 创建开始时间
     */
    private LocalDate beginDate;

    /**
     * 创建结束时间
     */
    private LocalDate endDate;

    /**
     * 异常合同id
     */
    private Set<Integer> bizIds;
}
