package com.coocaa.ad.cheese.cms.common.db.venue.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.OperateLogEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.OperateLogMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IOperateLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 操作日志 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-25
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class OperateLogServiceImpl extends ServiceImpl<OperateLogMapper, OperateLogEntity> implements IOperateLogService {

}
