package com.coocaa.ad.cheese.cms.common.db.venue.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.RegionEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.RegionMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IRegionService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 大区信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Service
@Primary
public class RegionServiceImpl extends ServiceImpl<RegionMapper, RegionEntity> implements IRegionService {

    @Override
    public String queryNameByCity(Integer cityId) {
        return getBaseMapper().queryNameByCity(cityId);
    }
}
