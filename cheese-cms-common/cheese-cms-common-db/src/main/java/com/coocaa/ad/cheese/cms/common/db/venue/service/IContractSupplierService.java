package com.coocaa.ad.cheese.cms.common.db.venue.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity;

import java.util.List;

/**
 * 合同-供应商 服务类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
public interface IContractSupplierService extends IService<ContractSupplierEntity> {

    /*
     * <AUTHOR>
     * @Description 异常合同查询供应商
     * @Date 2025/4/8
     * @Param [contractIds]
     * @return java.util.List<com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity>
     **/
    List<ContractSupplierEntity> querySupplierForAbContract(List<Integer> contractIds);
}
