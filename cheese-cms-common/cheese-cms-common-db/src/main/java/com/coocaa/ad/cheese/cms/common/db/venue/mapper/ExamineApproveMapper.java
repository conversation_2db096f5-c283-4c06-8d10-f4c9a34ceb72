package com.coocaa.ad.cheese.cms.common.db.venue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ExamineApproveEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 审批业务关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Mapper
public interface ExamineApproveMapper extends BaseMapper<ExamineApproveEntity> {

    /*
     * <AUTHOR>
     * @Description 查询审批中的合同对应飞书code
     * @Date 2025/3/25
     * @Param [id, userId]
     * @return java.lang.String
     **/
    String queryPendingContract(@Param("id") Integer id, @Param("userId") Integer userId);

    /*
     * <AUTHOR>
     * @Description 查询审批业务关联表对应异常合同id
     * @Date 2025/3/31 
     * @Param [processCodes]
     * @return java.util.List<com.coocaa.ad.cheese.cms.common.db.venue.entity.ExamineApproveEntity>
     **/
    List<ExamineApproveEntity> queryBizId(@Param("processCodes") Set<String> processCodes);

    /*
     * <AUTHOR>
     * @Description 查询审批业务关联表对应processCode
     * @Date 2025/3/31
     * @Param [id]
     * @return java.lang.String
     **/
    String queryProcessCode(Integer id);
}
