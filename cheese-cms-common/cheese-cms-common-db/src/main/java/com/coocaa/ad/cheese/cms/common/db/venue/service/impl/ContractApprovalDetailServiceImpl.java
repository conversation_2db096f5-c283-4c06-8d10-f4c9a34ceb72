package com.coocaa.ad.cheese.cms.common.db.venue.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractApprovalDetailEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractApprovalDetailMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractApprovalDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 场地合同审批详情 服务实现类
 *
 * @since 2024-12-04
 */
@Slf4j
@Primary
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractApprovalDetailServiceImpl extends ServiceImpl<ContractApprovalDetailMapper, ContractApprovalDetailEntity> implements IContractApprovalDetailService {

}
