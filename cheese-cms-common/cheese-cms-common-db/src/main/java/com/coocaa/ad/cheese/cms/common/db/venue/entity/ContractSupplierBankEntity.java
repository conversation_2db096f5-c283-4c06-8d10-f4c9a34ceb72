package com.coocaa.ad.cheese.cms.common.db.venue.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.ad.cheese.cms.common.db.common.handler.EncryptHandler;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 合同-供应商-银行
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Data
@TableName(value = "venue_contract_supplier_bank", autoResultMap = true)
public class ContractSupplierBankEntity implements IFillingSupplierAndBankBoth {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 合同ID
     */
    private Integer contractId;

    /**
     * 子合同ID(主合同无)
     */
    private Integer subContractId;

    /**
     * 供应商ID
     */
    private Integer supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 银行银行账号
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String bankAccountCode;

    /**
     * 收款账号名
     */
    private String accountName;

    /**
     * 联行号
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 是否押金收款银行 [0:否, 1:是]
     *
     * @see com.coocaa.ad.common.enums.BooleFlagEnum
     */
    private Integer depositBank;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 虚拟字段：银行银行账号
     */
    @TableField(exist = false)
    private String accountNo;

    @Override
    public String getAccountNo() {
        return bankAccountCode;
    }
}
