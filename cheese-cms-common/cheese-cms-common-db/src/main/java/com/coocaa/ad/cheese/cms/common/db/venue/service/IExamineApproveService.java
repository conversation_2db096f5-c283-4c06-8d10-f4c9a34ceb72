package com.coocaa.ad.cheese.cms.common.db.venue.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ExamineApproveEntity;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 处理方式表	 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
public interface IExamineApproveService extends IService<ExamineApproveEntity> {

    /*
     * <AUTHOR>
     * @Description 查询审批中的合同对应飞书code
     * @Date 2025/3/25
     * @Param [id, userId]
     * @return java.lang.String
     **/
    String queryPendingContract(Integer id, Integer userId);

    /*
     * <AUTHOR>
     * @Description 查询审批业务关联表对应异常合同id
     * @Date 2025/3/31 
     * @Param [processCodes]
     * @return java.util.List<com.coocaa.ad.cheese.cms.common.db.venue.entity.ExamineApproveEntity>
     **/
    List<ExamineApproveEntity> queryBizId(Set<String> processCodes);

    /*
     * <AUTHOR>
     * @Description 查询审批业务关联表对应processCode
     * @Date 2025/3/31
     * @Param [id]
     * @return java.lang.String
     **/
    String queryProcessCode(Integer id);
}
