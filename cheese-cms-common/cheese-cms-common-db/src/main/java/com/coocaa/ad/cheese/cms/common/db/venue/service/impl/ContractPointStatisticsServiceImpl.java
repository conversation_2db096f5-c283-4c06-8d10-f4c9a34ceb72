package com.coocaa.ad.cheese.cms.common.db.venue.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPointStatisticsEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractPointStatisticsMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPointStatisticsService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;


/**
 * 合同-点位数量统计 服务实现类
 */
@Slf4j
@Primary
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractPointStatisticsServiceImpl extends ServiceImpl<ContractPointStatisticsMapper, ContractPointStatisticsEntity> implements IContractPointStatisticsService {
    @Value("${spring.datasource.username:cheese_cms}")
    private String schema;

    private final ContractPointStatisticsMapper contractPointStatisticsMapper;

    /**
     * 获取数据表column与comment的对应关系
     *
     * @param tableName 表名
     */
    @Override
    public List<Map<String, String>> getColumnComments(String tableName) {
        return contractPointStatisticsMapper.getColumnComments(schema, tableName);
    }
}
