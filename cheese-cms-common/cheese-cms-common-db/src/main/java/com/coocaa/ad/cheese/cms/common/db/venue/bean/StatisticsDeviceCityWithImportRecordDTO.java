package com.coocaa.ad.cheese.cms.common.db.venue.bean;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import lombok.Data;

/**
 * <AUTHOR>
 * @file StatisticsDeviceCityWithImportRecordDTO
 * @date 2025/1/17 17:46
 * @description 城市设备数据包含导入信息
 */
@Data
public class StatisticsDeviceCityWithImportRecordDTO {
    /**
     * 关联批次ID
     */
    private Long importNo;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 仓储物流数
     */
    private Long warehouseLogisticsCount;

    /**
     * 派工数
     */
    private Long workOrderDispatchCount;

    /**
     * 踏勘数
     */
    private Long siteSurveyCount;

    /**
     * 挂板数
     */
    private Long mountingPlateCount;

    /**
     * 安装点亮数
     */
    private Long installationAndLightingCount;


    // ==================================
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private LocalDate statisticsDate;
}
