<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractAbnormalDealMapper">

    <select id="queryAbnormalList" resultType="com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAbnormalDealEntity">
        SELECT
          s1.type,
          s1.creator,
          s1.deal_type,
          s1.deal_date,
          s1.remark,
          s1.create_time
        FROM venue_contract_abnormal_deal s1
          INNER JOIN (
            SELECT
                MAX(version) AS max_version
              FROM
                venue_contract_abnormal_deal
            WHERE abnormal_id = #{id}
              AND delete_flag = 0
              AND type in ('0106-1')
           ) s2
          ON s1.version = s2.max_version
         AND s1.abnormal_id = #{id}
         AND s1.delete_flag = 0
         AND s1.type in ('0106-1', '0106-3', '0106-6', '0106-7', '0106-8')
        ORDER BY s1.create_time
    </select>
</mapper>
