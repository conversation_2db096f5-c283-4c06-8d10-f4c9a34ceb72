<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.cms.common.db.venue.mapper.KanbanMapper">
    <select id="selectContractIndexesByCityId" resultType="java.util.LinkedHashMap">
        SELECT
        SUM(CASE WHEN apply_status='${@<EMAIL>}' THEN 1 ELSE 0 END) AS '${@<EMAIL>}',
        SUM(CASE WHEN formal_flag=1 AND formal_status='${@<EMAIL>}' AND apply_status='${@<EMAIL>}' THEN 1 ELSE 0 END) AS '${@<EMAIL>}',
        SUM(CASE WHEN formal_flag=1 AND formal_status='${@com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractStatusEnum@WAIT_EXECUTE.code}' AND apply_status='${@<EMAIL>}' THEN 1 ELSE 0 END) AS '${@com.coocaa.ad.cheese.cms.common.tools.venue.enums.KanbanContractIndexEnum@TO_BE_EXECUTED.code}',
        SUM(CASE WHEN formal_flag=1 AND formal_status='${@<EMAIL>}' AND apply_status='${@<EMAIL>}' THEN 1 ELSE 0 END) AS '${@<EMAIL>}',
        SUM(CASE WHEN formal_flag=1 AND formal_status='${@<EMAIL>}' AND end_date BETWEEN CURDATE() AND CURDATE() + INTERVAL 30 DAY THEN 1 ELSE 0 END) AS '${@com.coocaa.ad.cheese.cms.common.tools.venue.enums.KanbanContractIndexEnum@EXPIRES_IN_SEVERAL_DAYS.code}',
        SUM(CASE WHEN formal_flag=1 AND formal_status='${@<EMAIL>}' THEN 1 ELSE 0 END) AS '${@<EMAIL>}'
        FROM venue_contract
        <where>
            <if test="cityId != null and cityId != 0">
                AND city_id = #{cityId}
            </if>
            AND delete_flag = 0
        </where>
    </select>
</mapper>